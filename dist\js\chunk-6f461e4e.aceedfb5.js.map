{"version": 3, "sources": ["webpack:///./src/icons/svg-black/VehicleReservationSuccess.svg", "webpack:///./src/views/admin/VehicleReservationSuccess.vue?fc56", "webpack:///./src/views/admin/VehicleReservationSuccess.vue", "webpack:///./src/views/admin/VehicleReservationSuccess.vue?0209"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "form", "reactive", "data", "id", "yardCode", "yardName", "channelName", "plateNumber", "vehicleClassification", "merchantName", "releaseReason", "notifierName", "appointmentTime", "reserveTime", "remark", "appointmentFlag", "reserveFlag", "value2", "ref", "showChannelNameOptions", "shortcuts", "text", "value", "end", "Date", "start", "setTime", "getTime", "handleExport", "startDate", "endDate", "newStartDate", "formattedStartDate", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "replace", "newEndDate", "formattedEndDate", "console", "log", "window", "location", "href", "onReset", "handleEdit", "row", "dialogVisibleUpdate", "channelNameList", "vehicleClassificationList", "merchantNameList", "releaseReasonList", "notifierNameList", "appointmentTimeList", "yardNameList", "applicantUserId", "request", "get", "then", "res", "changeYardName", "params", "changeMerchantName", "localStorage", "getItem", "updateShowChannelNameOptions", "consoleForm", "dialogVisible", "query", "pageNum", "pageSize", "tableData", "pageTotal", "getData", "records", "total", "tableRowClassName", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "post", "ElMessage", "success", "splice", "error", "catch", "handExport", "formRef", "update", "length", "alert", "test", "chineseCharacters", "match", "validate", "valid", "url", "method", "code", "msg", "__exports__"], "mappings": "8HAAAA,EAAOC,QAAU,IAA0B,8C,kCCA3C,W,meC+JMC,EAAO,+B,mDACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CAEV,CAAEC,MAAO,OAAQC,KAAM,YACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,OAAQC,KAAM,eAEvB,CAAED,MAAO,OAAQC,KAAM,gBACvB,CAAED,MAAO,QAASC,KAAM,gBAExB,CAAED,MAAO,OAAQC,KAAM,mBACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,KAAMC,KAAM,UAErB,CAAED,MAAO,OAAQC,KAAM,eAGrBC,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJC,SAAU,GACVC,SAAU,GACVC,YAAa,GACbC,YAAa,GACbC,sBAAuB,GACvBC,aAAc,GACdC,cAAe,GACfC,aAAc,GACdC,gBAAiB,GACjBC,YAAa,GACbC,OAAQ,GACRC,iBAAkB,EAClBC,aAAc,KAIhBC,EAASC,iBAAI,IACbC,EAAyBD,kBAAI,GAE7BE,EAAY,CACd,CACIC,KAAM,MACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,OACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,MACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,OACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,OACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,MAIrBK,EAAeA,KACjB,MAAMC,EAAYZ,EAAOK,MAAM,GACzBQ,EAAUb,EAAOK,MAAM,GAEvBS,EAAe,IAAIP,KAAKK,GACxBG,EAAqBD,EAAaE,eAAe,QAAS,CAAEC,KAAM,UAAWC,MAAO,UAAWC,IAAK,UAAWC,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAAaC,QAAQ,MAAO,KAEvLC,EAAa,IAAIjB,KAAKM,GACtBY,EAAmBD,EAAWR,eAAe,QAAS,CAAEC,KAAM,UAAWC,MAAO,UAAWC,IAAK,UAAWC,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAAaC,QAAQ,MAAO,KACzLG,QAAQC,IAAI5C,EAAKE,KAAKG,UACtBsC,QAAQC,IAAIZ,GACZW,QAAQC,IAAIF,GACZG,OAAOC,SAASC,KACZ,8EAEAf,EACA,YACAU,EACA,aACA1C,EAAKE,KAAKG,SACV,gBACAL,EAAKE,KAAKI,aAGZ0C,EAAUA,KACZhD,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKE,SAAW,GACrBJ,EAAKE,KAAKG,SAAW,GACrBL,EAAKE,KAAKI,YAAc,GACxBN,EAAKE,KAAKK,YAAc,GACxBP,EAAKE,KAAKM,sBAAwB,GAClCR,EAAKE,KAAKO,aAAe,GACzBT,EAAKE,KAAKQ,cAAgB,GAC1BV,EAAKE,KAAKS,aAAe,GACzBX,EAAKE,KAAKU,gBAAkB,GAC5BZ,EAAKE,KAAKW,YAAc,GACxBb,EAAKE,KAAKY,OAAS,IAEjBmC,EAAcC,IAChBC,EAAoB7B,OAAQ,EAC5BtB,EAAKE,KAAKC,GAAK+C,EAAI/C,GACnBH,EAAKE,KAAKE,SAAW8C,EAAI9C,SACzBJ,EAAKE,KAAKG,SAAW6C,EAAI7C,SACzBL,EAAKE,KAAKI,YAAc4C,EAAI5C,YAC5BN,EAAKE,KAAKK,YAAc2C,EAAI3C,YAC5BP,EAAKE,KAAKM,sBAAwB0C,EAAI1C,sBACtCR,EAAKE,KAAKO,aAAeyC,EAAIzC,aAC7BT,EAAKE,KAAKQ,cAAgBwC,EAAIxC,cAC9BV,EAAKE,KAAKS,aAAeuC,EAAIvC,aAC7BX,EAAKE,KAAKU,gBAAkBsC,EAAItC,gBAChCZ,EAAKE,KAAKY,OAASoC,EAAIpC,QAGrBsC,EAAkBlC,iBAAI,IACtBmC,EAA4BnC,iBAAI,IAChCoC,EAAmBpC,iBAAI,IACvBqC,EAAoBrC,iBAAI,IACxBsC,EAAmBtC,iBAAI,IACvBuC,EAAsBvC,iBAAI,IAG1BwC,GAFWxC,kBAAI,GACLA,iBAAI,IACCA,iBAAI,KACnByC,EAAkBzC,iBAAI,IAC5B0C,OAAQC,IAAI,8BAA8BC,KAAMC,IAC5CL,EAAapC,MAAQyC,EAAI7D,OAE7B0D,OAAQC,IAAI,wDAAwDC,KAC/DC,IACGV,EAA0B/B,MAAQyC,EAAI7D,OAE9C0D,OAAQC,IAAI,sCAAsCC,KAC7CC,IACGT,EAAiBhC,MAAQyC,EAAI7D,OAErC0D,OAAQC,IAAI,wCAAwCC,KAC/CC,IACGR,EAAkBjC,MAAQyC,EAAI7D,OAEtC,MAAM8D,EAAiBA,KACnBrB,QAAQC,IAAI5C,EAAKE,KAAKE,UACtBwD,OACKC,IAAI,6BACD,CACII,OAAQ,CACJ5D,SAAUL,EAAKE,KAAKG,YAG/ByD,KAAMC,IACH/D,EAAKE,KAAKI,YAAc,GACxBN,EAAKE,KAAKM,sBAAwB,GAClCR,EAAKE,KAAKS,aAAe,GACzBX,EAAKE,KAAKO,aAAe,GACzBT,EAAKE,KAAKQ,cAAgB,GAC1BV,EAAKE,KAAKE,SAAW2D,EAAI7D,KAAK,GAC9B0D,OACKC,IAAI,iDACD,CACII,OAAQ,CACJ7D,SAAU2D,EAAI7D,KAAK,MAG9B4D,KAAMC,IACHpB,QAAQC,IAAI,SAAU5C,EAAKE,KAAKE,UAChCJ,EAAKE,KAAKM,sBAAwB,GAClCR,EAAKE,KAAKS,aAAe,GACzBX,EAAKE,KAAKO,aAAe,GACzBT,EAAKE,KAAKQ,cAAgB,GAC1B0C,EAAgB9B,MAAQyC,EAAI7D,UAM1CgE,EAAqBA,KACvBN,OACKC,IAAI,qCACD,CACII,OAAQ,CACJxD,aAAcT,EAAKE,KAAKO,gBAGnCqD,KAAMC,IACH/D,EAAKE,KAAKS,aAAe,GACzBX,EAAKE,KAAKQ,cAAgB,GAC1B8C,EAAiBlC,MAAQyC,EAAI7D,QAGzCyD,EAAgBrC,MAAQ6C,aAAaC,QAAQ,UAC7CR,OAAQC,IAAI,iCAAiCC,KAAMC,IAC/CL,EAAapC,MAAQyC,EAAI7D,OAE7B,MAAMmE,EAA+BA,KACN,SAAvBrE,EAAKE,KAAKG,WACVc,EAAuBG,OAAQ,IAGjCgD,EAAcA,KAChBtB,IACA/B,EAAOK,MAAQJ,iBAAI,IACnBC,EAAuBG,OAAQ,EAC/BiD,EAAcjD,OAAQ,GAEpBkD,EAAQvE,sBAAS,CACnBI,SAAU,GACVE,YAAa,GACbkE,QAAS,EACTC,SAAU,KAERC,EAAYzD,iBAAI,IAChB0D,EAAY1D,iBAAI,GAChBiC,EAAsBjC,kBAAI,GAE1BqD,GADSJ,aAAaC,QAAQ,UACdlD,kBAAI,IAGpB2D,EAAUA,KACZjB,OACKC,IAAIpE,EAAO,kBAAmB,CAC3BwE,OAAQO,IAEXV,KAAMC,IAMHY,EAAUrD,MAAQyC,EAAI7D,KAAK4E,QAC3BF,EAAUtD,MAAQyC,EAAI7D,KAAK6E,MAC3BpC,QAAQC,IAAImB,EAAI7D,SAGtB8E,EAAoBA,EAAG9B,MAAK+B,eAEzBA,EAAW,GAAK,GAAK,GACtBtC,QAAQC,IAAIqC,GACL,YACCA,EAAW,GAAK,GAAK,GAC7BtC,QAAQC,IAAIqC,GACL,iBAFJ,EAMLC,EAAYA,EAAGhC,MAAKiC,SAAQF,WAAUG,kBACxC,IAAIC,EAAQ,CAAEC,QAAS,WACvB,OAAOD,GAGXR,IAEA,MAAMU,EAAeA,KACjBf,EAAMC,QAAU,EAChBI,KAGEW,EAAoBC,IACtBjB,EAAME,SAAWe,EACjBZ,KAGEa,EAAoBD,IACtBjB,EAAMC,QAAUgB,EAChBZ,KAGEc,EAAeA,CAACC,EAAOC,KAEzBC,OAAaC,QAAQ,cAAe,KAAM,CACtCC,KAAM,YAELlC,KAAK,KACFF,OAAQqC,KAAKxG,EAAOoG,GAAK/B,KAAMC,IACvBA,EAAI7D,MACJgG,OAAUC,QAAQ,QAClB3B,EAAMC,QAAU,EAChBI,IACAF,EAAUrD,MAAM8E,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAI3BC,MAAM,SAKTC,EAAaA,KACfhC,EAAcjD,OAAQ,GAEpBkF,EAAUtF,iBAAI,MAEduF,EAASA,KACX,GAAIzG,EAAKE,KAAKK,YAAYmG,OAAS,GAAK1G,EAAKE,KAAKK,YAAYmG,OAAS,EAGnE,OAFAC,MAAM,oBACN3G,EAAKE,KAAKK,YAAc,IAErB,GAAI,kBAAkBqG,KAAK5G,EAAKE,KAAKK,aAAc,CAEtD,MAAMsG,EAAoB7G,EAAKE,KAAKK,YAAYuG,MAAM,oBACtD,GAAID,GAAqBA,EAAkBH,OAAS,EAGhD,YADA1G,EAAKE,KAAKK,YAAc,IAKhCiG,EAAQlF,MAAMyF,SAAUC,IACpB,IAAIA,EAiCA,OAAO,EAhCPpD,eAAQ,CACJqD,IAAK,qCACLC,OAAQ,OACRhH,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBC,SAAUL,EAAKE,KAAKG,SACpBC,YAAaN,EAAKE,KAAKI,YACvBC,YAAaP,EAAKE,KAAKK,YACvBC,sBAAuBR,EAAKE,KAAKM,sBACjCC,aAAcT,EAAKE,KAAKO,aACxBC,cAAeV,EAAKE,KAAKQ,cACzBC,aAAcX,EAAKE,KAAKS,aACxBC,gBAAiBZ,EAAKE,KAAKU,gBAC3BE,OAAQd,EAAKE,KAAKY,UAEvBgD,KAAMC,IACLpB,QAAQC,IAAI,QACZD,QAAQC,IAAImB,GACZpB,QAAQC,IAAImB,EAAI7D,MAChBF,EAAKE,KAAO,GACS,MAAjB6D,EAAI7D,KAAKiH,MACTtC,IACAqB,OAAUC,QAAQ,SAElBhD,EAAoB7B,OAAQ,IAE5B6B,EAAoB7B,OAAQ,EAC5B4E,OAAUG,MAAMtC,EAAIqD,W,qjVC7fxC,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD", "file": "js/chunk-6f461e4e.aceedfb5.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"img/VehicleReservationSuccess.7b3246a4.svg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./VehicleReservationSuccess.vue?vue&type=style&index=0&id=684408ed&lang=scss&scoped=true\"", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/VehicleReservationSuccess.svg\"></i>&nbsp; 外来车辆放行管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                        <el-input v-model=\"query.yardName\" placeholder=\"车场名称\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"车牌号码\">\r\n                        <el-input v-model=\"query.plateNumber\" placeholder=\"车牌号码\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\">搜索\r\n                    </el-button>\r\n\r\n                    <el-button type=\"success\" class=\"addButton\" @click=\"handExport\">导出数据\r\n                    </el-button>\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in props\" :key=\"item.prop\" align=\"center\" width=\"110px\">\r\n                </el-table-column>\r\n                <el-table-column label=\"预约状态\" prop=\"appointmentFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 0\" effect=\"dark\"\r\n                            size=\"large\">未预约</el-tag>\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 1\" effect=\"dark\"\r\n                            size=\"large\">已预约</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"入场状态\" prop=\"reserveFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"danger\" v-if=\"scope.row.reserveFlag === 0\" effect=\"dark\" size=\"large\">未放行</el-tag>\r\n                        <el-tag type=\"success\" v-else-if=\"scope.row.reserveFlag === 1\" effect=\"dark\"\r\n                            size=\"large\">已放行</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"200px\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\" size=\"small\"\r\n                            plain>编辑\r\n                        </el-button>\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" class=\"red\"\r\n                            @click=\"handleDelete(scope.$index, scope.row.id)\" size=\"small\" plain>删除\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"数据导出信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"选择导出时间\" prop=\"\" label-width=\"128px\">\r\n                        <el-date-picker v-model=\"value2\" type=\"datetimerange\" :shortcuts=\"shortcuts\"\r\n                            range-separator=\"--\" start-placeholder=\"开始时间\" end-placeholder=\"结束时间\" />\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\"\r\n                            @change=\"updateShowChannelNameOptions\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通道名称\" v-if=\"showChannelNameOptions\">\r\n                        <el-select v-model=\"form.data.channelName\" placeholder=\"请选择通道名称\" clearable>\r\n                            <el-option label=\"万象上东地库入口\" value=\"万象上东地库入口\" />\r\n                            <el-option label=\"四季三期地库入口\" value=\"四季上东地库入口\" />\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"consoleForm\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"handleExport\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"修改外来车辆预约信息\" v-model=\"dialogVisibleUpdate\" width=\"48%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"form.data.yardCode\" disabled></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 30%\"\r\n                            @input=\"convertToUpperCase\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\"\r\n                                :label=\"item.merchantName\" :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\"\r\n                                :label=\"item.notifierName\" :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"预约时间\" prop=\"appointmentTime\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 70%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisibleUpdate = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"update\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\nconst root = \"/parking/vehicleReservation/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    // { label: \"车场编码\", prop: \"yardCode\" },\r\n    { label: \"车场名称\", prop: \"yardName\" },\r\n    { label: \"入场通道\", prop: \"channelName\" },\r\n    { label: \"车牌号码\", prop: \"plateNumber\" },\r\n    // { label: \"车辆分类\", prop: \"vehicleClassification\" },\r\n    { label: \"商户名称\", prop: \"merchantName\" },\r\n    { label: \"通知人姓名\", prop: \"notifierName\" },\r\n    // { label: \"放行原因\", prop: \"releaseReason\" },\r\n    { label: \"预约时间\", prop: \"appointmentTime\" },\r\n    { label: \"放行时间\", prop: \"reserveTime\" },\r\n    { label: \"备注\", prop: \"remark\" },\r\n    // { label: \"创建时间\", prop: \"createTime\" },\r\n    { label: \"修改时间\", prop: \"updateTime\" },\r\n];\r\n\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        yardCode: '',\r\n        yardName: '',\r\n        channelName: '',\r\n        plateNumber: '',\r\n        vehicleClassification: '',\r\n        merchantName: '',\r\n        releaseReason: '',\r\n        notifierName: '',\r\n        appointmentTime: '',\r\n        reserveTime: '',\r\n        remark: '',\r\n        appointmentFlag: -1,\r\n        reserveFlag: -1,\r\n    },\r\n\r\n});\r\nconst value2 = ref([])\r\nconst showChannelNameOptions = ref(false);\r\n\r\nconst shortcuts = [\r\n    {\r\n        text: '前一天',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上一周',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上一个月',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上三个月',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)\r\n            return [start, end]\r\n        },\r\n    },\r\n]\r\nconst handleExport = () => {\r\n    const startDate = value2.value[0]\r\n    const endDate = value2.value[1]\r\n    //格式化开始时间\r\n    const newStartDate = new Date(startDate);\r\n    const formattedStartDate = newStartDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\\//g, '-');\r\n    //格式化结束时间\r\n    const newEndDate = new Date(endDate);\r\n    const formattedEndDate = newEndDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\\//g, '-');\r\n    console.log(form.data.yardName)\r\n    console.log(formattedStartDate)\r\n    console.log(formattedEndDate)\r\n    window.location.href =\r\n        \"http://www.xuerparking.cn:8543/parking/vehicleReservation/export?startDate=\" +\r\n        // \"http://localhost:8543/parking/vehicleReservation/export?startDate=\" +\r\n        formattedStartDate +\r\n        \"&endDate=\" +\r\n        formattedEndDate +\r\n        \"&yardName=\" +\r\n        form.data.yardName +\r\n        \"&channelName=\" +\r\n        form.data.channelName;\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.yardCode = ''\r\n    form.data.yardName = ''\r\n    form.data.channelName = ''\r\n    form.data.plateNumber = ''\r\n    form.data.vehicleClassification = ''\r\n    form.data.merchantName = ''\r\n    form.data.releaseReason = ''\r\n    form.data.notifierName = ''\r\n    form.data.appointmentTime = ''\r\n    form.data.reserveTime = ''\r\n    form.data.remark = ''\r\n};\r\nconst handleEdit = (row) => {\r\n    dialogVisibleUpdate.value = true\r\n    form.data.id = row.id\r\n    form.data.yardCode = row.yardCode\r\n    form.data.yardName = row.yardName\r\n    form.data.channelName = row.channelName\r\n    form.data.plateNumber = row.plateNumber\r\n    form.data.vehicleClassification = row.vehicleClassification\r\n    form.data.merchantName = row.merchantName\r\n    form.data.releaseReason = row.releaseReason\r\n    form.data.notifierName = row.notifierName\r\n    form.data.appointmentTime = row.appointmentTime\r\n    form.data.remark = row.remark\r\n};\r\n// const yardNameList = ref([]);\r\nconst channelNameList = ref([]);\r\nconst vehicleClassificationList = ref([]);\r\nconst merchantNameList = ref([]);\r\nconst releaseReasonList = ref([]);\r\nconst notifierNameList = ref([]);\r\nconst appointmentTimeList = ref([]);\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\nconst yardNameList = ref([]);\r\nconst applicantUserId = ref(\"\");\r\nrequest.get(\"/parking/yardInfo/yardName\").then((res) => {\r\n    yardNameList.value = res.data;\r\n});\r\nrequest.get(\"/parking/vehicleClassification/vehicleClassification\").then(\r\n    (res) => {\r\n        vehicleClassificationList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/notifierInfo/merchantName\").then(\r\n    (res) => {\r\n        merchantNameList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/releaseReason/releaseReason\").then(\r\n    (res) => {\r\n        releaseReasonList.value = res.data;\r\n    });\r\nconst changeYardName = () => {\r\n    console.log(form.data.yardCode);\r\n    request\r\n        .get(\"/parking/yardInfo/yardCode\",\r\n            {\r\n                params: {\r\n                    yardName: form.data.yardName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.channelName = \"\";\r\n            form.data.vehicleClassification = \"\";\r\n            form.data.notifierName = \"\";\r\n            form.data.merchantName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            form.data.yardCode = res.data[0]\r\n            request\r\n                .get(\"/parking/vehicleReservation/aikeGetChannelInfo\",\r\n                    {\r\n                        params: {\r\n                            yardCode: res.data[0]\r\n                        },\r\n                    })\r\n                .then((res) => {\r\n                    console.log(\"传递的参数为\", form.data.yardCode)\r\n                    form.data.vehicleClassification = \"\";\r\n                    form.data.notifierName = \"\";\r\n                    form.data.merchantName = \"\";\r\n                    form.data.releaseReason = \"\";\r\n                    channelNameList.value = res.data\r\n                });\r\n        });\r\n\r\n\r\n};\r\nconst changeMerchantName = () => {\r\n    request\r\n        .get(\"/parking/notifierInfo/notifierName\",\r\n            {\r\n                params: {\r\n                    merchantName: form.data.merchantName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.notifierName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            notifierNameList.value = res.data;\r\n        });\r\n};\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\nrequest.get(\"/parking/yardInfo/expYardName\").then((res) => {\r\n    yardNameList.value = res.data;\r\n});\r\nconst updateShowChannelNameOptions = () => {\r\n    if (form.data.yardName === '万象上东') {\r\n        showChannelNameOptions.value = true;\r\n    }\r\n};\r\nconst consoleForm = () => {\r\n    onReset();\r\n    value2.value = ref([]);\r\n    showChannelNameOptions.value = false;\r\n    dialogVisible.value = false\r\n};\r\nconst query = reactive({\r\n    yardName: \"\",\r\n    plateNumber: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst dialogVisibleUpdate = ref(false)\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\n\r\n// 获取表格数据\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"reservationPage\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            // tableData.appointmentTime.value = res.data.records.createTime\r\n            //给tableData.appointmentTime的值赋值\r\n            // console.log(res)\r\n            // console.log(res.data)\r\n            // console.log(res.data.records)\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n            console.log(res.data)\r\n        });\r\n};//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n    let style = { padding: '6px 0px' }\r\n    return style\r\n};\r\n\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除放行信息吗？\", \"提示\", {\r\n        type: \"warning\",\r\n    })\r\n        .then(() => {\r\n            request.post(root + sid).then((res) => {\r\n                if (res.data) {\r\n                    ElMessage.success(\"删除成功\");\r\n                    query.pageNum = 1;\r\n                    getData();\r\n                    tableData.value.splice(index, 1);\r\n                } else {\r\n                    ElMessage.error(\"删除失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n\r\n//弹窗显示要导出的数据时间节点\r\nconst handExport = () => {\r\n    dialogVisible.value = true;\r\n}\r\nconst formRef = ref(null);\r\n\r\nconst update = () => {\r\n    if (form.data.plateNumber.length < 7 || form.data.plateNumber.length > 8) {\r\n        alert('输入长度必须为7-8位');\r\n        form.data.plateNumber = \"\";\r\n        return;\r\n    } else if (/[\\u4e00-\\u9fa5]/.test(form.data.plateNumber)) {\r\n        // 检查输入值是否包含多个汉字\r\n        const chineseCharacters = form.data.plateNumber.match(/[\\u4e00-\\u9fa5]/g);\r\n        if (chineseCharacters && chineseCharacters.length > 2) {\r\n            ('除第一个和最后一个外不允许输入多个汉字');\r\n            form.data.plateNumber = \"\";\r\n            return;\r\n        }\r\n    }\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            request({\r\n                url: \"/parking/vehicleReservation/update\",\r\n                method: \"POST\",\r\n                data: {\r\n                    id: form.data.id,\r\n                    yardCode: form.data.yardCode,\r\n                    yardName: form.data.yardName,\r\n                    channelName: form.data.channelName,\r\n                    plateNumber: form.data.plateNumber,\r\n                    vehicleClassification: form.data.vehicleClassification,\r\n                    merchantName: form.data.merchantName,\r\n                    releaseReason: form.data.releaseReason,\r\n                    notifierName: form.data.notifierName,\r\n                    appointmentTime: form.data.appointmentTime,\r\n                    remark: form.data.remark\r\n                },\r\n            }).then((res) => {\r\n                console.log(\"修改页面\")\r\n                console.log(res)\r\n                console.log(res.data)\r\n                form.data = {}\r\n                if (res.data.code != null) {\r\n                    getData()\r\n                    ElMessage.success(\"修改成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisibleUpdate.value = false\r\n                } else {\r\n                    dialogVisibleUpdate.value = false\r\n                    ElMessage.error(res.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n    background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>\r\n", "import script from \"./VehicleReservationSuccess.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VehicleReservationSuccess.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VehicleReservationSuccess.vue?vue&type=style&index=0&id=684408ed&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-684408ed\"]])\n\nexport default __exports__"], "sourceRoot": ""}