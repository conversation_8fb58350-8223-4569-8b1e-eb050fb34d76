{"version": 3, "sources": ["webpack:///./src/assets/img/del-carCode.svg", "webpack:///./src/icons/svg-black/OwnerInfo.svg", "webpack:///./src/views/admin/OwnerInfo.vue", "webpack:///./src/views/admin/OwnerInfo.vue?e95f", "webpack:///./src/assets/img/addCarCode.svg", "webpack:///./src/views/admin/OwnerInfo.vue?2441"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "rules", "province", "required", "message", "trigger", "city", "district", "community", "building", "units", "floor", "roomnumber", "ownername", "ownerphone", "form", "reactive", "data", "id", "<PERSON><PERSON><PERSON>", "permitverify", "plates", "parkingspaces", "carDatas", "parkingDatas", "onReset", "push", "viewShow", "ref", "content", "content1", "applicantUserId", "value", "localStorage", "getItem", "departmentList", "request", "get", "then", "res", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "getData", "params", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleAdd", "handleEdit", "row", "carArr", "parkingArr", "split", "dataNum", "dataParkingNum", "i", "length", "provinceList", "cityList", "districtList", "communityList", "buildingList", "unitsList", "floorList", "roomnumberList", "changeProvince", "changeCity", "console", "log", "changeDistrict", "changeCommunity", "changeBuilding", "changeUnits", "changeFloor", "formRef", "save", "validate", "valid", "carstr", "parkingstr", "find", "j", "warning", "method", "url", "code", "msg", "upload", "fileList", "onUpload", "file", "files", "0", "raw", "readExcel", "state", "test", "name", "toLowerCase", "fileReader", "FileReader", "onload", "ev", "target", "result", "workbook", "XLSX", "read", "wsname", "SheetNames", "ws", "utils", "sheet_to_json", "Sheets", "alert", "confirmButtonText", "callback", "action", "e", "readAsBinaryString", "onErrorFile", "clearFiles", "onSuccessFile", "deleteCar", "tableRowClassName", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding", "addCar", "deleteParking", "addParking", "__exports__"], "mappings": "qGAAAA,EAAOC,QAAU,IAA0B,gC,uBCA3CD,EAAOC,QAAU,IAA0B,8B,0pBC6SjCC,EAAO,sB,mCACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACV,CAACC,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,QACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,aACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,SACpB,CAACD,MAAO,KAAMC,KAAM,SACpB,CAACD,MAAO,KAAMC,KAAM,cACpB,CAACD,MAAO,OAAQC,KAAM,aACtB,CAACD,MAAO,OAAQC,KAAM,cACtB,CAACD,MAAO,OAAQC,KAAM,WACtB,CAACD,MAAO,OAAQC,KAAM,gBACtB,CAACD,MAAO,OAAQC,KAAM,UACtB,CAACD,MAAO,OAAQC,KAAM,kBAGpBC,EAAQ,CACVC,SAAU,CACN,CACIC,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBC,KAAM,CACF,CACIH,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBE,SAAU,CACN,CACIJ,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBG,UAAW,CACP,CACIL,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBI,SAAU,CACN,CACIN,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBK,MAAO,CACH,CACIP,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBM,MAAO,CACH,CACIR,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBO,WAAY,CACR,CACIT,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBQ,UAAW,CACP,CACIV,UAAU,EACVC,QAAS,UACTC,QAAS,SAGjBS,WAAY,CACR,CAACX,UAAU,EAAMC,QAAS,UAAWC,QAAS,UAGhDU,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJhB,SAAU,GACVI,KAAM,GACNC,SAAU,GACVC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,MAAO,GACPC,WAAY,GACZC,UAAW,GACXC,WAAY,GACZK,QAAS,GACTC,aAAc,GACdC,OAAQ,GACRC,cAAe,GACfC,SAAU,CACN,CACIL,GAAI,EACJD,KAAM,KAGdO,aAAc,CACV,CACIN,GAAI,EACJD,KAAM,QAWhBQ,EAAUA,KACZV,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKf,SAAW,GACrBa,EAAKE,KAAKX,KAAO,GACjBS,EAAKE,KAAKV,SAAW,GACrBQ,EAAKE,KAAKT,UAAY,GACtBO,EAAKE,KAAKR,SAAW,GACrBM,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,GACvBG,EAAKE,KAAKJ,UAAY,GACtBE,EAAKE,KAAKH,WAAa,GACvBC,EAAKE,KAAKE,QAAU,GACpBJ,EAAKE,KAAKG,aAAe,GACzBL,EAAKE,KAAKI,OAAS,GACnBN,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKM,SAAW,GACrBR,EAAKE,KAAKM,SAASG,KAAK,CAChBR,GAAI,EACJD,KAAM,KAGdF,EAAKE,KAAKO,aAAe,GACzBT,EAAKE,KAAKO,aAAaE,KACnB,CACIR,GAAI,EACJD,KAAM,MAIZU,EAAWC,kBAAI,GACfC,EAAUD,iBAAI,IAWdE,GADYF,kBAAI,GACLA,iBAAI,KAUfG,EAAkBH,iBAAI,IAC5BG,EAAgBC,MAAQC,aAAaC,QAAQ,UAE7C,MAAMC,EAAiBP,iBAAI,IAC3BQ,OAAQC,IAAI,sCAAsCC,KAAMC,IACpDJ,EAAeH,MAAQO,EAAItB,OAE/B,MAAMuB,EAAQxB,sBAAS,CACnBR,UAAW,GACXK,UAAW,GACX4B,QAAS,EACTC,SAAU,KAERC,EAAYf,iBAAI,IAChBgB,EAAYhB,iBAAI,GAEhBiB,GADSZ,aAAaC,QAAQ,UACdN,kBAAI,IAKpBkB,EAAUA,KACZV,OACKC,IAAI3C,EAAO,YAAa,CACrBqD,OAAQP,IAEXF,KAAMC,IACHI,EAAUX,MAAQO,EAAItB,KAAK+B,QAC3BJ,EAAUZ,MAAQO,EAAItB,KAAKgC,SAGvCH,IAEA,MAAMI,EAAeA,KACjBV,EAAMC,QAAU,EAChBK,KAGEK,EAAoBC,IACtBZ,EAAME,SAAWU,EACjBN,KAGEO,EAAoBD,IACtBZ,EAAMC,QAAUW,EAChBN,KAGEQ,EAAeA,CAACC,EAAOC,KAEzBC,OAAaC,QAAQ,UAAW,KAAM,CAClCC,KAAM,YAELrB,KAAK,KACFF,OAAQwB,OAAOlE,EAAO8D,GAAKlB,KAAMC,IACzBA,EAAItB,MACJ4C,OAAUC,QAAQ,QAClBnB,EAAUX,MAAM+B,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAI3BC,MAAM,SAKTC,EAAYA,KACdrB,EAAcb,OAAQ,EACtBP,IACAV,EAAKE,KAAKE,QAAU,IACpBJ,EAAKE,KAAKG,aAAe,KAMvB+C,GADcvC,kBAAI,GACJwC,IAChBvB,EAAcb,OAAQ,EACtBjB,EAAKE,KAAKC,GAAKkD,EAAIlD,GACnBH,EAAKE,KAAKf,SAAWkE,EAAIlE,SACzBa,EAAKE,KAAKX,KAAO8D,EAAI9D,KACrBS,EAAKE,KAAKV,SAAW6D,EAAI7D,SACzBQ,EAAKE,KAAKT,UAAY4D,EAAI5D,UAC1BO,EAAKE,KAAKR,SAAW2D,EAAI3D,SACzBM,EAAKE,KAAKP,MAAQ0D,EAAI1D,MACtBK,EAAKE,KAAKN,MAAQyD,EAAIzD,MACtBI,EAAKE,KAAKL,WAAawD,EAAIxD,WAC3BG,EAAKE,KAAKJ,UAAYuD,EAAIvD,UAC1BE,EAAKE,KAAKH,WAAasD,EAAItD,WAC3BC,EAAKE,KAAKE,QAAUiD,EAAIjD,QACxBJ,EAAKE,KAAKG,aAAegD,EAAIhD,aAC7B,IAAIiD,EAAS,GACTC,EAAa,GACjBD,EAASD,EAAI/C,OAAOkD,MAAM,KAC1BD,EAAaF,EAAI9C,cAAciD,MAAM,KACrCxD,EAAKE,KAAKM,SAAW,GACrBR,EAAKE,KAAKO,aAAe,GACzBgD,GAAQxC,MAAQ,EAChByC,GAAezC,MAAQ,EACvB,IAAK,IAAI0C,EAAI,EAAGA,EAAIL,EAAOM,OAAQD,IAC/B3D,EAAKE,KAAKM,SAASG,KAEf,CACIR,GAAIsD,GAAQxC,QACZf,KAAMoD,EAAOK,KAIzB,IAAK,IAAIA,EAAI,EAAGA,EAAIJ,EAAWK,OAAQD,IACnC3D,EAAKE,KAAKO,aAAaE,KAEnB,CACIR,GAAIuD,GAAezC,QACnBf,KAAMqD,EAAWI,OAK3BE,EAAehD,iBAAI,IACnBiD,EAAWjD,iBAAI,IACfkD,EAAelD,iBAAI,IACnBmD,EAAgBnD,iBAAI,IACpBoD,EAAepD,iBAAI,IACnBqD,EAAYrD,iBAAI,IAChBsD,EAAYtD,iBAAI,IAChBuD,EAAiBvD,iBAAI,CACvB,CACIhB,WAAY,GAEhB,CACIA,WAAY,GAEhB,CACIA,WAAY,GAEhB,CACIA,WAAY,KAGpBwB,OAAQC,IAAI,+BAA+BC,KAAMC,IAC7CqC,EAAa5C,MAAQO,EAAItB,OAE7B,MAAMmE,EAAiBA,KACnBhD,OACKC,IAAI,0BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,YAG/BoC,KAAMC,IACHsC,EAAS7C,MAAQO,EAAItB,KACrBF,EAAKE,KAAKX,KAAO,GACjBS,EAAKE,KAAKV,SAAW,GACrBQ,EAAKE,KAAKT,UAAY,GACtBO,EAAKE,KAAKR,SAAW,GACrBM,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7ByE,EAAaA,KACfC,QAAQC,IAAIxE,EAAKE,KAAKf,UACtBkC,OACKC,IAAI,8BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,QAG3BgC,KAAMC,IACHuC,EAAa9C,MAAQO,EAAItB,KACzBF,EAAKE,KAAKV,SAAW,GACrBQ,EAAKE,KAAKT,UAAY,GACtBO,EAAKE,KAAKR,SAAW,GACrBM,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7B4E,EAAiBA,KACnBpD,OACKC,IAAI,+BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,KAChBC,SAAUQ,EAAKE,KAAKV,YAG/B+B,KAAMC,IACHwC,EAAc/C,MAAQO,EAAItB,KAC1BF,EAAKE,KAAKT,UAAY,GACtBO,EAAKE,KAAKR,SAAW,GACrBM,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7B6E,GAAkBA,KACpBrD,OACKC,IAAI,8BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,KAChBC,SAAUQ,EAAKE,KAAKV,SACpBC,UAAWO,EAAKE,KAAKT,aAGhC8B,KAAMC,IACHyC,EAAahD,MAAQO,EAAItB,KACzBF,EAAKE,KAAKR,SAAW,GACrBM,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7B8E,GAAiBA,KACnBtD,OACKC,IAAI,2BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,KAChBC,SAAUQ,EAAKE,KAAKV,SACpBC,UAAWO,EAAKE,KAAKT,UACrBC,SAAUM,EAAKE,KAAKR,YAG/B6B,KAAMC,IACH0C,EAAUjD,MAAQO,EAAItB,KACtBF,EAAKE,KAAKP,MAAQ,GAClBK,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7B+E,GAAcA,KAChBvD,OACKC,IAAI,2BACD,CACIU,OAAQ,CACJ7C,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,KAChBC,SAAUQ,EAAKE,KAAKV,SACpBC,UAAWO,EAAKE,KAAKT,UACrBC,SAAUM,EAAKE,KAAKR,SACpBC,MAAOK,EAAKE,KAAKP,SAG5B4B,KAAMC,IACH2C,EAAUlD,MAAQO,EAAItB,KACtBF,EAAKE,KAAKN,MAAQ,GAClBI,EAAKE,KAAKL,WAAa,MAI7BgF,GAAcA,KAEhB7E,EAAKE,KAAKL,WAAa,IAIrBiF,GAAUjE,iBAAI,MACdkE,GAAOA,KAETD,GAAQ7D,MAAM+D,SAAUC,IACpB,IAAIA,EA0EA,OAAO,EAxEP,IAAIC,EAAS,GACTC,EAAa,GACbC,GAAO,EACX,IAAK,IAAIzB,EAAI,EAAGA,EAAI3D,EAAKE,KAAKM,SAASoD,OAAQD,IAC3C,IAAK,IAAI0B,EAAI1B,EAAI,EAAG0B,EAAIrF,EAAKE,KAAKM,SAASoD,OAAQyB,IAC/C,GAAIrF,EAAKE,KAAKM,SAASmD,GAAGzD,MAAQF,EAAKE,KAAKM,SAAS6E,GAAGnF,KAAM,CAC1DkF,GAAO,EACP,MAIZ,GAAIA,EAEA,OADAtC,OAAUwC,QAAQ,YACX,EAEX,IAAK,IAAI3B,EAAI,EAAGA,EAAI3D,EAAKE,KAAKM,SAASoD,OAAQD,IACzBuB,EAAJ,IAAVA,EAAuBlF,EAAKE,KAAKM,SAASmD,GAAGzD,KACnCgF,EAAS,IAAMlF,EAAKE,KAAKM,SAASmD,GAAGzD,KAGvD,IAAK,IAAIyD,EAAI,EAAGA,EAAI3D,EAAKE,KAAKO,aAAamD,OAAQD,IAC/C,IAAK,IAAI0B,EAAI1B,EAAI,EAAG0B,EAAIrF,EAAKE,KAAKO,aAAamD,OAAQyB,IACnD,GAAIrF,EAAKE,KAAKO,aAAakD,GAAGzD,MAAQF,EAAKE,KAAKO,aAAa4E,GAAGnF,KAAM,CAClEkF,GAAO,EACP,MAIZ,GAAIA,EAEA,OADAtC,OAAUwC,QAAQ,aACX,EAEX,IAAK,IAAI3B,EAAI,EAAGA,EAAI3D,EAAKE,KAAKO,aAAamD,OAAQD,IACzBwB,EAAJ,IAAdA,EAA+BnF,EAAKE,KAAKO,aAAakD,GAAGzD,KAC3CiF,EAAa,IAAMnF,EAAKE,KAAKO,aAAakD,GAAGzD,KAEnEF,EAAKE,KAAKI,OAAS4E,EACnBlF,EAAKE,KAAKK,cAAgB4E,EAC1B,IAAII,EAA0B,KAAjBvF,EAAKE,KAAKC,GAAY,OAAS,MAC5CkB,eAAQ,CACJmE,IAAK,qBACLD,OAAQA,EACRrF,KAAM,CACFf,SAAUa,EAAKE,KAAKf,SACpBI,KAAMS,EAAKE,KAAKX,KAChBC,SAAUQ,EAAKE,KAAKV,SACpBC,UAAWO,EAAKE,KAAKT,UACrBC,SAAUM,EAAKE,KAAKR,SACpBC,MAAOK,EAAKE,KAAKP,MACjBC,MAAOI,EAAKE,KAAKN,MACjBC,WAAYG,EAAKE,KAAKL,WACtBC,UAAWE,EAAKE,KAAKJ,UACrBC,WAAYC,EAAKE,KAAKH,WACtBK,QAASJ,EAAKE,KAAKE,QACnBC,aAAcL,EAAKE,KAAKG,aACxBC,OAAQN,EAAKE,KAAKI,OAClBC,cAAeP,EAAKE,KAAKK,iBAE9BgB,KAAMC,IAELxB,EAAKE,KAAO,GACK,OAAbsB,EAAIiE,MACJ1D,IACAe,OAAUC,QAAQ,SAElBjB,EAAcb,OAAQ,IAEtBa,EAAcb,OAAQ,EACtB6B,OAAUG,MAAMzB,EAAIkE,WAQlCC,GAAS9E,mBACT+E,GAAW/E,iBAAI,IACfgF,GAAYC,IACd,MAAMC,EAAQ,CAACC,EAAGF,EAAKG,KAET,cAAVF,EACAxB,QAAQC,MAER0B,GAAUH,GAGdxB,QAAQC,IAAImB,IACZpB,QAAQC,IAAI2B,GAAMR,SAEhBO,GAAaH,IAEf,GADAxB,QAAQC,IAAIuB,GACRA,EAAMnC,QAAU,EAChB,OAAO,EACJ,IAAK,gBAAgBwC,KAAKL,EAAM,GAAGM,KAAKC,eAE3C,OADA/B,QAAQC,IAAI,2BACL,EAGX,MAAM+B,EAAa,IAAIC,WACvBD,EAAWE,OAAUC,IACjB,IACI,MAAMxG,EAAOwG,EAAGC,OAAOC,OACjBC,EAAWC,IAAKC,KAAK7G,EAAM,CAAC0C,KAAM,WAClCoE,EAASH,EAASI,WAAW,GAC7BC,EAAKJ,IAAKK,MAAMC,cAAcP,EAASQ,OAAOL,IACpDzC,QAAQC,IAAI0C,GACZ7F,eAAQ,CACJmE,IAAK,+BACLD,OAAQ,OACRrF,KAAMgH,IACP3F,KAAMC,IACY,OAAbA,EAAIiE,MACJ1D,IAEAD,EAAcb,OAAQ,EACtBsD,QAAQC,IAAIhD,EAAIkE,KACA,KAAZlE,EAAIkE,IACJhD,OAAa4E,MAAM9F,EAAIkE,IAAK,KAAM,CAG9B6B,kBAAmB,KACnBC,SAAWC,QAIf3E,OAAUC,QAAQ,WAItBjB,EAAcb,OAAQ,EACtB6B,OAAUG,MAAMzB,EAAIkE,QAI5BC,GAAO1E,MAAQ,GACjB,MAAOyG,GACL,OAAO,IAGfnB,EAAWoB,mBAAmB5B,EAAM,KAElCI,GAAQlG,sBAAS,CACnB0F,OAAQ,OAGNiC,GAAcA,KAChB9E,OAAUG,MAAM,UAChBkD,GAAMR,OAAO1E,MAAM4G,cAIjBC,GAAgBA,KAClBhF,OAAUC,QAAQ,UAClBoD,GAAMR,OAAO1E,MAAM4G,cAGjBpE,GAAU5C,iBAAI,GACdkH,GAAavF,IACf,GAAIxC,EAAKE,KAAKM,SAASoD,QAAU,EAE7B,OAAO,EAEXW,QAAQC,IAAIhC,GACZxC,EAAKE,KAAKM,SAASwC,OAAOR,EAAO,IAKnCwF,GAAoBA,EAAE3E,MAAK4E,eAE3BA,EAAW,GAAK,GAAK,GACnB1D,QAAQC,IAAIyD,GACX,YACGA,EAAW,GAAK,GAAK,GACzB1D,QAAQC,IAAIyD,GACX,iBAFF,EAMDC,GAAaA,EAAE7E,MAAK8E,SAAQF,WAASG,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,GAEDE,GAASA,KAEX,GADAhE,QAAQC,IAAIf,IACRzD,EAAKE,KAAKM,SAASoD,OAAS,EAG5B,OADAd,OAAUC,QAAQ,mBACX,EAEX/C,EAAKE,KAAKM,SAASG,KAEf,CACIR,GAAIsD,GAAQxC,QACZf,KAAM,MAKZwD,GAAiB7C,iBAAI,GACrB2H,GAAiBhG,IACnB,GAAIxC,EAAKE,KAAKO,aAAamD,QAAU,EAEjC,OAAO,EAEXW,QAAQC,IAAIhC,GACZxC,EAAKE,KAAKO,aAAauC,OAAOR,EAAO,IAGnCiG,GAAaA,KAEf,GADAlE,QAAQC,IAAId,IACR1D,EAAKE,KAAK0D,OAAS,EAGnB,OADAd,OAAUC,QAAQ,sBACX,EAEX/C,EAAKE,KAAKO,aAAaE,KAEnB,CACIR,GAAIuD,GAAezC,QACnBf,KAAM,M,itaC38BtB,MAAMwI,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB,uBCRfjK,EAAOC,QAAU,IAA0B,+B,yDCA3C", "file": "js/chunk-74845d32.95229162.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"img/del-carCode.3767d911.svg\";", "module.exports = __webpack_public_path__ + \"img/OwnerInfo.072d16cd.svg\";", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/OwnerInfo.svg\"></i> 业主管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form\r\n                        :inline=\"true\"\r\n                        :model=\"query\"\r\n                        class=\"demo-form-inline\"\r\n                        label-width=\"60px\"\r\n                >\r\n                    <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n                        <el-input\r\n                                v-model=\"query.community\"\r\n                                placeholder=\"小区名称\"\r\n                                class=\"handle-input mr10\"\r\n                                clearable\r\n                        ></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"业主姓名\">\r\n                        <el-input\r\n                                v-model=\"query.ownername\"\r\n                                placeholder=\"业主姓名\"\r\n                                class=\"handle-input mr10\"\r\n                                clearable\r\n                        ></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\"\r\n                    >搜索\r\n                    </el-button\r\n                    >\r\n                    <el-button\r\n                            type=\"primary\"\r\n                            class=\"addButton\"\r\n                            @click=\"handleAdd\"\r\n                    >新增\r\n                    </el-button>\r\n                    <el-upload\r\n                            ref=\"upload\"\r\n                            class=\"upload-demo\"\r\n                            action=\"\"\r\n                            accept=\".xls,.xlsx\"\r\n                            :on-change=\"onUpload\"\r\n                            :limit=\"1\"\r\n                            :on-exceed=\"handleExceed\"\r\n                            :on-error=\"onErrorFile\"\r\n                            :on-success=\"onSuccessFile\"\r\n                            :auto-upload=\"false\"\r\n                            :file-list=\"fileList\"\r\n                            :show-file-list=\"false\"\r\n                            name=\"file\"\r\n                    >\r\n                        <el-button class=\"uploadButton\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n\r\n                </el-form>\r\n            </div>\r\n            <el-table\r\n                    :data=\"tableData\"\r\n                    border\r\n                    class=\"table\"\r\n                    ref=\"multipleTable\"\r\n                    header-cell-class-name=\"table-header\"\r\n                    :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\"\r\n            >\r\n                <el-table-column\r\n                        :show-overflow-tooltip=\"true\"\r\n                        :prop=\"item.prop\"\r\n                        :label=\"item.label\"\r\n                        v-for=\"item in props\"\r\n                        :key=\"item.prop\"\r\n                        align=\"center\"\r\n                >\r\n                </el-table-column>\r\n\r\n\r\n                <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button\r\n                                type=\"text\"\r\n                                icon=\"el-icon-edit\"\r\n                                @click=\"handleEdit(scope.row)\"\r\n                        >编辑\r\n                        </el-button>\r\n                        <el-button\r\n                                type=\"text\"\r\n                                icon=\"el-icon-delete\"\r\n                                class=\"red\"\r\n                                @click=\"handleDelete(scope.$index, scope.row.id)\"\r\n                        >删除\r\n                        </el-button>\r\n\r\n                    </template>\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                        :currentPage=\"query.pageNum\"\r\n                        :page-sizes=\"[10, 20, 40]\"\r\n                        :page-size=\"query.pageSize\"\r\n                        layout=\"total, sizes, prev, pager, next, jumper\"\r\n                        :total=\"pageTotal\"\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handlePageChange\"\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"增加业主信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"省份\" prop=\"province\">\r\n                        <el-select v-model=\"form.data.province\" placeholder=\"请选择省份\">\r\n                            <el-option\r\n                                    v-for=\"item in provinceList\"\r\n                                    :key=\"item.province\"\r\n                                    :label=\"item.province\"\r\n                                    :value=\"item.province\"\r\n                                    @click=\"changeProvince\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"地市\" prop=\"city\">\r\n                        <el-select v-model=\"form.data.city\" placeholder=\"请选择地市\">\r\n                            <el-option\r\n                                    v-for=\"item in cityList\"\r\n                                    :key=\"item.city\"\r\n                                    :label=\"item.city\"\r\n                                    :value=\"item.city\"\r\n                                    @click=\"changeCity\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"区县\" prop=\"district\">\r\n                        <el-select v-model=\"form.data.district\" placeholder=\"请选择区县\">\r\n                            <el-option\r\n                                    v-for=\"item in districtList\"\r\n                                    :key=\"item.district\"\r\n                                    :label=\"item.district\"\r\n                                    :value=\"item.district\"\r\n                                    @click=\"changeDistrict\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"小区\" prop=\"community\">\r\n                        <el-select v-model=\"form.data.community\" placeholder=\"请选择小区\">\r\n                            <el-option\r\n                                    v-for=\"item in communityList\"\r\n                                    :key=\"item.community\"\r\n                                    :label=\"item.community\"\r\n                                    :value=\"item.community\"\r\n                                    @click=\"changeCommunity\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼栋\" prop=\"building\">\r\n                        <el-select v-model=\"form.data.building\" placeholder=\"请选择楼栋\">\r\n                            <el-option\r\n                                    v-for=\"item in buildingList\"\r\n                                    :key=\"item.building\"\r\n                                    :label=\"item.building\"\r\n                                    :value=\"item.building\"\r\n                                    @click=\"changeBuilding\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单元\" prop=\"units\">\r\n                        <el-select v-model=\"form.data.units\" placeholder=\"请选择单元\">\r\n                            <el-option\r\n                                    v-for=\"item in unitsList\"\r\n                                    :key=\"item.units\"\r\n                                    :label=\"item.units\"\r\n                                    :value=\"item.units\"\r\n                                    @click=\"changeUnits\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼层\" prop=\"floor\">\r\n                        <el-select v-model=\"form.data.floor\" placeholder=\"请选择楼层\">\r\n                            <el-option\r\n                                    v-for=\"item in floorList\"\r\n                                    :key=\"item.floor\"\r\n                                    :label=\"item.floor\"\r\n                                    :value=\"item.floor\"\r\n                                    @click=\"changeFloor\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"房号\" prop=\"roomnumber\">\r\n                        <el-select v-model=\"form.data.roomnumber\" placeholder=\"房号\">\r\n                            <el-option\r\n                                    v-for=\"item in roomnumberList\"\r\n                                    :key=\"item.roomnumber\"\r\n                                    :label=\"item.roomnumber\"\r\n                                    :value=\"item.roomnumber\"\r\n                            >\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主名称\" prop=\"ownername\">\r\n                        <el-input v-model=\"form.data.ownername\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主电话\" prop=\"ownerphone\">\r\n                        <el-input v-model=\"form.data.ownerphone\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <!-- label=\"车牌号\"    -->\r\n                    <el-form-item label=\"车牌号\">\r\n                        <el-form-item v-for=\"(item,index) in form.data.carDatas\" :key=\"index\"\r\n                                      style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'carDatas.'+index+'.data'\"\r\n                                          :rules=\"{ required: true, message: '请输入车牌号', trigger:'blur'}\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车牌号\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteCar(index)\" alt=\"\"\r\n                                     class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addCar\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车位号码\">\r\n                        <el-form-item v-for=\"(item,index) in form.data.parkingDatas\" :key=\"index\"\r\n                                      style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'parkingDatas.'+index+'.data'\"\r\n                                          :rules=\"{ required: true, message: '请输入车位号码', trigger:'blur'}\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车位号码\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteParking(index)\" alt=\"\"\r\n                                     class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addParking\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"是否审批\">\r\n                        <el-radio-group v-model=\"form.data.isaudit\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"允许访客验证\">\r\n                        <el-radio-group v-model=\"form.data.permitverify\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"查看审核原因\" v-model=\"viewShow\">\r\n                <span style=\"margin-left: 50px\">{{ content }}</span>\r\n                <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"viewShow = false\">取 消</el-button>\r\n          </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"查看图片\" v-model=\"viewShow\">\r\n                <span style=\"margin-left: 50px\">{{ content1 }}</span>\r\n                <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"viewShow = false\">取 消</el-button>\r\n          </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\n    import {useRoute, useRouter} from \"vue-router\";\r\n    import {reactive, ref} from \"vue\";\r\n    import request from \"@/utils/request\";\r\n    import {ElMessage, ElMessageBox} from \"element-plus\";\r\n    import {useStore} from \"vuex\";\r\n\r\n\r\n    import XLSX from \"xlsx\";\r\n\r\n    const root = \"/parking/ownerinfo/\";\r\n    const router = useRouter();\r\n    const route = useRoute();\r\n    const store = useStore();\r\n    const props = [\r\n        {label: \"省份\", prop: \"province\"},\r\n        {label: \"地市\", prop: \"city\"},\r\n        {label: \"县区\", prop: \"district\"},\r\n        {label: \"小区\", prop: \"community\"},\r\n        {label: \"栋号\", prop: \"building\"},\r\n        {label: \"单元\", prop: \"units\"},\r\n        {label: \"楼层\", prop: \"floor\"},\r\n        {label: \"房号\", prop: \"roomnumber\"},\r\n        {label: \"业主姓名\", prop: \"ownername\"},\r\n        {label: \"业主电话\", prop: \"ownerphone\"},\r\n        {label: \"是否审批\", prop: \"isaudit\"},\r\n        {label: \"允许验证\", prop: \"permitverify\"},\r\n        {label: \"车牌号码\", prop: \"plates\"},\r\n        {label: \"车位号码\", prop: \"parkingspaces\"},\r\n    ];\r\n\r\n    const rules = {\r\n        province: [\r\n            {\r\n                required: true,\r\n                message: \"请选择省份\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        city: [\r\n            {\r\n                required: true,\r\n                message: \"请选择地市\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        district: [\r\n            {\r\n                required: true,\r\n                message: \"请选择县区\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        community: [\r\n            {\r\n                required: true,\r\n                message: \"请选择校区\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        building: [\r\n            {\r\n                required: true,\r\n                message: \"请选择楼栋\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        units: [\r\n            {\r\n                required: true,\r\n                message: \"请选择单元\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        floor: [\r\n            {\r\n                required: true,\r\n                message: \"请选择楼层\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        roomnumber: [\r\n            {\r\n                required: true,\r\n                message: \"请选择房号\",\r\n                trigger: \"change\",\r\n            },\r\n        ],\r\n        ownername: [\r\n            {\r\n                required: true,\r\n                message: \"请输入业主姓名\",\r\n                trigger: \"blur\",\r\n            },\r\n        ],\r\n        ownerphone: [\r\n            {required: true, message: \"请输入业主电话\", trigger: \"blur\"},\r\n        ],\r\n    };\r\n    const form = reactive({\r\n        data: {\r\n            id: '',\r\n            province: '',\r\n            city: '',\r\n            district: '',\r\n            community: '',\r\n            building: '',\r\n            units: '',\r\n            floor: '',\r\n            roomnumber: '',\r\n            ownername: '',\r\n            ownerphone: '',\r\n            isaudit: '',\r\n            permitverify: '',\r\n            plates: '',\r\n            parkingspaces: '',\r\n            carDatas: [\r\n                {\r\n                    id: 0,\r\n                    data: ''\r\n                }\r\n            ],\r\n            parkingDatas: [\r\n                {\r\n                    id: 0,\r\n                    data: ''\r\n                }\r\n            ]\r\n        },\r\n\r\n    });\r\n\r\n    const handleExport = () => {\r\n        window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n    };\r\n    // 重置\r\n    const onReset = () => {\r\n        form.data.id = ''\r\n        form.data.province = ''\r\n        form.data.city = ''\r\n        form.data.district = ''\r\n        form.data.community = ''\r\n        form.data.building = ''\r\n        form.data.units = ''\r\n        form.data.floor = ''\r\n        form.data.roomnumber = ''\r\n        form.data.ownername = ''\r\n        form.data.ownerphone = ''\r\n        form.data.isaudit = ''\r\n        form.data.permitverify = ''\r\n        form.data.plates = ''\r\n        form.data.parkingspaces = ''\r\n        form.data.carDatas = []\r\n        form.data.carDatas.push({\r\n                id: 0,\r\n                data: ''\r\n            }\r\n        )\r\n        form.data.parkingDatas = []\r\n        form.data.parkingDatas.push(\r\n            {\r\n                id: 0,\r\n                data: ''\r\n            }\r\n        )\r\n    };\r\n    const viewShow = ref(false)\r\n    const content = ref(\"\");\r\n    const handleView = (row) => {\r\n        console.log(\"这批我\")\r\n        if (row.fileReason !== null) {\r\n            viewShow.value = true\r\n            content.value = row.fileReason\r\n        } else {\r\n            ElMessage.info('没有审核原因');\r\n        }\r\n    };\r\n    const viewShow1 = ref(false)\r\n    const content1 = ref(\"\");\r\n    const handleView1 = (row) => {\r\n        console.log(\"这批我\")\r\n        if (row.purchaseVoucher !== null) {\r\n            viewShow.value = true\r\n            content1.value = row.purchaseVoucher\r\n        } else {\r\n            ElMessage.info('没有审核原因');\r\n        }\r\n    };\r\n    const applicantUserId = ref(\"\");\r\n    applicantUserId.value = localStorage.getItem(\"userId\")\r\n    // alert(applicantUserId.value)\r\n    const departmentList = ref([]);\r\n    request.get(\"/parking/department/listDepartment\").then((res) => {\r\n        departmentList.value = res.data;\r\n    });\r\n    const query = reactive({\r\n        community: \"\",\r\n        ownername: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n    });\r\n    const tableData = ref([]);\r\n    const pageTotal = ref(0);\r\n    const userId = localStorage.getItem(\"userId\")\r\n    const dialogVisible = ref(false)\r\n\r\n\r\n    // 获取表格数据\r\n\r\n    const getData = () => {\r\n        request\r\n            .get(root + \"querypage\", {\r\n                params: query,\r\n            })\r\n            .then((res) => {\r\n                tableData.value = res.data.records;\r\n                pageTotal.value = res.data.total;\r\n            });\r\n    };\r\n    getData();\r\n    // 查询操作\r\n    const handleSearch = () => {\r\n        query.pageNum = 1;\r\n        getData();\r\n    };\r\n    // 分页大小\r\n    const handleSizeChange = (val) => {\r\n        query.pageSize = val;\r\n        getData();\r\n    };\r\n    // 分页导航\r\n    const handlePageChange = (val) => {\r\n        query.pageNum = val;\r\n        getData();\r\n    };\r\n    // 删除操作\r\n    const handleDelete = (index, sid) => {\r\n        // 二次确认删除\r\n        ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n            type: \"warning\",\r\n        })\r\n            .then(() => {\r\n                request.delete(root + sid).then((res) => {\r\n                    if (res.data) {\r\n                        ElMessage.success(\"删除成功\");\r\n                        tableData.value.splice(index, 1);\r\n                    } else {\r\n                        ElMessage.error(\"删除失败\");\r\n                    }\r\n                });\r\n            })\r\n            .catch(() => {\r\n            });\r\n    };\r\n\r\n    // 新增操作\r\n    const handleAdd = () => {\r\n        dialogVisible.value = true;\r\n        onReset();\r\n        form.data.isaudit = '是';\r\n        form.data.permitverify = '是';\r\n\r\n\r\n    };\r\n    // 表格编辑时弹窗和保存\r\n    const editVisible = ref(false);\r\n    const handleEdit = (row) => {\r\n        dialogVisible.value = true\r\n        form.data.id = row.id\r\n        form.data.province = row.province\r\n        form.data.city = row.city\r\n        form.data.district = row.district\r\n        form.data.community = row.community\r\n        form.data.building = row.building\r\n        form.data.units = row.units\r\n        form.data.floor = row.floor\r\n        form.data.roomnumber = row.roomnumber\r\n        form.data.ownername = row.ownername\r\n        form.data.ownerphone = row.ownerphone\r\n        form.data.isaudit = row.isaudit\r\n        form.data.permitverify = row.permitverify\r\n        var carArr = []\r\n        var parkingArr = []\r\n        carArr = row.plates.split(',')\r\n        parkingArr = row.parkingspaces.split(',')\r\n        form.data.carDatas = []\r\n        form.data.parkingDatas = []\r\n        dataNum.value = 0\r\n        dataParkingNum.value = 0\r\n        for (let i = 0; i < carArr.length; i++) {\r\n            form.data.carDatas.push(\r\n                // 增加就push进数组一个新值\r\n                {\r\n                    id: dataNum.value++,\r\n                    data: carArr[i]\r\n                }\r\n            )\r\n        }\r\n        for (let i = 0; i < parkingArr.length; i++) {\r\n            form.data.parkingDatas.push(\r\n                // 增加就push进数组一个新值\r\n                {\r\n                    id: dataParkingNum.value++,\r\n                    data: parkingArr[i]\r\n                }\r\n            )\r\n        }\r\n    };\r\n    const provinceList = ref([]);\r\n    const cityList = ref([]);\r\n    const districtList = ref([]);\r\n    const communityList = ref([]);\r\n    const buildingList = ref([]);\r\n    const unitsList = ref([]);\r\n    const floorList = ref([]);\r\n    const roomnumberList = ref([\r\n        {\r\n            roomnumber: 1,\r\n        },\r\n        {\r\n            roomnumber: 2,\r\n        },\r\n        {\r\n            roomnumber: 3,\r\n        },\r\n        {\r\n            roomnumber: 4,\r\n        },\r\n    ]);\r\n    request.get(\"/parking/community/province\").then((res) => {\r\n        provinceList.value = res.data;\r\n    });\r\n    const changeProvince = () => {\r\n        request\r\n            .get(\"/parking/community/city\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                cityList.value = res.data;\r\n                form.data.city = \"\";\r\n                form.data.district = \"\";\r\n                form.data.community = \"\";\r\n                form.data.building = \"\";\r\n                form.data.units = \"\";\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeCity = () => {\r\n        console.log(form.data.province);\r\n        request\r\n            .get(\"/parking/community/district\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                districtList.value = res.data;\r\n                form.data.district = \"\";\r\n                form.data.community = \"\";\r\n                form.data.building = \"\";\r\n                form.data.units = \"\";\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeDistrict = () => {\r\n        request\r\n            .get(\"/parking/community/community\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                        district: form.data.district,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                communityList.value = res.data;\r\n                form.data.community = \"\";\r\n                form.data.building = \"\";\r\n                form.data.units = \"\";\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeCommunity = () => {\r\n        request\r\n            .get(\"/parking/community/building\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                        district: form.data.district,\r\n                        community: form.data.community,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                buildingList.value = res.data;\r\n                form.data.building = \"\";\r\n                form.data.units = \"\";\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeBuilding = () => {\r\n        request\r\n            .get(\"/parking/community/units\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                        district: form.data.district,\r\n                        community: form.data.community,\r\n                        building: form.data.building,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                unitsList.value = res.data;\r\n                form.data.units = \"\";\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeUnits = () => {\r\n        request\r\n            .get(\"/parking/community/floor\",\r\n                {\r\n                    params: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                        district: form.data.district,\r\n                        community: form.data.community,\r\n                        building: form.data.building,\r\n                        units: form.data.units,\r\n                    },\r\n                })\r\n            .then((res) => {\r\n                floorList.value = res.data;\r\n                form.data.floor = \"\";\r\n                form.data.roomnumber = \"\";\r\n            });\r\n\r\n    };\r\n    const changeFloor = () => {\r\n\r\n        form.data.roomnumber = \"\";\r\n\r\n    };\r\n\r\n    const formRef = ref(null);\r\n    const save = () => {\r\n        // 表单校验\r\n        formRef.value.validate((valid) => {\r\n            if (valid) {\r\n                //车牌处理\r\n                var carstr = ''\r\n                var parkingstr = ''\r\n                var find = false;\r\n                for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                    for (let j = i + 1; j < form.data.carDatas.length; j++) {\r\n                        if (form.data.carDatas[i].data == form.data.carDatas[j].data) {\r\n                            find = true;\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n                if (find) {\r\n                    ElMessage.warning(\"车牌号有重复！\");\r\n                    return false;\r\n                }\r\n                for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                    if (carstr == '') carstr = form.data.carDatas[i].data\r\n                    else carstr = carstr + ',' + form.data.carDatas[i].data\r\n                }\r\n                //车位编号处理\r\n                for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                    for (let j = i + 1; j < form.data.parkingDatas.length; j++) {\r\n                        if (form.data.parkingDatas[i].data == form.data.parkingDatas[j].data) {\r\n                            find = true;\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n                if (find) {\r\n                    ElMessage.warning(\"车位编号有重复！\");\r\n                    return false;\r\n                }\r\n                for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                    if (parkingstr == '') parkingstr = form.data.parkingDatas[i].data\r\n                    else parkingstr = parkingstr + ',' + form.data.parkingDatas[i].data\r\n                }\r\n                form.data.plates = carstr\r\n                form.data.parkingspaces = parkingstr\r\n                var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n                request({\r\n                    url: \"/parking/ownerinfo\",\r\n                    method: method,\r\n                    data: {\r\n                        province: form.data.province,\r\n                        city: form.data.city,\r\n                        district: form.data.district,\r\n                        community: form.data.community,\r\n                        building: form.data.building,\r\n                        units: form.data.units,\r\n                        floor: form.data.floor,\r\n                        roomnumber: form.data.roomnumber,\r\n                        ownername: form.data.ownername,\r\n                        ownerphone: form.data.ownerphone,\r\n                        isaudit: form.data.isaudit,\r\n                        permitverify: form.data.permitverify,\r\n                        plates: form.data.plates,\r\n                        parkingspaces: form.data.parkingspaces,\r\n                    },\r\n                }).then((res) => {\r\n\r\n                    form.data = {}\r\n                    if (res.code === null) {\r\n                        getData()\r\n                        ElMessage.success(\"提交成功！\");\r\n                        // 关闭当前页面的标签页;\r\n                        dialogVisible.value = false\r\n                    } else {\r\n                        dialogVisible.value = false\r\n                        ElMessage.error(res.msg);\r\n                    }\r\n                });\r\n            } else {\r\n                return false;\r\n            }\r\n        });\r\n    };\r\n    const upload = ref();\r\n    const fileList = ref([]); // 图片列表\r\n    const onUpload = (file) => {\r\n        const files = {0: file.raw}// 取到File\r\n        // console.log(files)\r\n        if (files === 'undefined') {\r\n            console.log()\r\n        } else {\r\n            readExcel(files)\r\n        }\r\n        //state.upload.value.clearFiles(); //去掉文件列表\r\n        console.log(upload)\r\n        console.log(state.upload)\r\n    };\r\n    const readExcel = (files) => { // 表格导入\r\n        console.log(files)\r\n        if (files.length <= 0) { // 如果没有文件名\r\n            return false\r\n        } else if (!/\\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {\r\n            console.log('上传格式不正确，请上传xls或者xlsx格式')\r\n            return false\r\n        }\r\n\r\n        const fileReader = new FileReader();\r\n        fileReader.onload = (ev) => {\r\n            try {\r\n                const data = ev.target.result;\r\n                const workbook = XLSX.read(data, {type: 'binary'});\r\n                const wsname = workbook.SheetNames[0]// 取第一张表\r\n                const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsname])// 生成json表格内容\r\n                console.log(ws)\r\n                request({\r\n                    url: \"/parking/ownerinfo/batInsert\",\r\n                    method: \"POST\",\r\n                    data: ws,\r\n                }).then((res) => {\r\n                    if (res.code === null) {\r\n                        getData()\r\n                        // 关闭当前页面的标签页;\r\n                        dialogVisible.value = false\r\n                        console.log(res.msg)\r\n                        if (res.msg !== \"\") {\r\n                            ElMessageBox.alert(res.msg, '提示', {\r\n                                // if you want to disable its autofocus\r\n                                // autofocus: false,\r\n                                confirmButtonText: 'OK',\r\n                                callback: (action) => {\r\n                                },\r\n                            })\r\n                        } else {\r\n                            ElMessage.success(\"提交成功！\");\r\n                        }\r\n\r\n                    } else {\r\n                        dialogVisible.value = false\r\n                        ElMessage.error(res.msg);\r\n                    }\r\n                });\r\n                // 重写数据\r\n                upload.value = ''\r\n            } catch (e) {\r\n                return false\r\n            }\r\n        }\r\n        fileReader.readAsBinaryString(files[0])\r\n    };\r\n    const state = reactive({\r\n        upload: null\r\n    })\r\n    // 文件上传失败钩子\r\n    const onErrorFile = () => {\r\n        ElMessage.error('文件上传失败')\r\n        state.upload.value.clearFiles(); //去掉文件列表\r\n    }\r\n\r\n    // 文件上传成功钩子\r\n    const onSuccessFile = () => {\r\n        ElMessage.success('文件上传成功')\r\n        state.upload.value.clearFiles(); //去掉文件列表\r\n    }\r\n\r\n    const dataNum = ref(0);\r\n    const deleteCar = (index) => {\r\n        if (form.data.carDatas.length <= 1) {\r\n            // 如果只有一个输入框则不可以删除\r\n            return false\r\n        }\r\n        console.log(index);\r\n        form.data.carDatas.splice(index, 1)\r\n        // 删除了数组中对应的数据也就将这个位置的输入框删除\r\n    }\r\n\r\n    //指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\n    const addCar = () => {\r\n        console.log(dataNum)\r\n        if (form.data.carDatas.length > 9) {\r\n            // 如果只有一个输入框则不可以删除\r\n            ElMessage.success('业主所属车量不能超过10个！')\r\n            return false\r\n        }\r\n        form.data.carDatas.push(\r\n            // 增加就push进数组一个新值\r\n            {\r\n                id: dataNum.value++,\r\n                data: ''\r\n            }\r\n        )\r\n    }\r\n\r\n    const dataParkingNum = ref(0);\r\n    const deleteParking = (index) => {\r\n        if (form.data.parkingDatas.length <= 1) {\r\n            // 如果只有一个输入框则不可以删除\r\n            return false\r\n        }\r\n        console.log(index);\r\n        form.data.parkingDatas.splice(index, 1)\r\n        // 删除了数组中对应的数据也就将这个位置的输入框删除\r\n    }\r\n    const addParking = () => {\r\n        console.log(dataParkingNum)\r\n        if (form.data.length > 9) {\r\n            // 如果只有一个输入框则不可以删除\r\n            ElMessage.success('业主所属停车位数量不能超过10个！')\r\n            return false\r\n        }\r\n        form.data.parkingDatas.push(\r\n            // 增加就push进数组一个新值\r\n            {\r\n                id: dataParkingNum.value++,\r\n                data: ''\r\n            }\r\n        )\r\n    }\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n\tbackground-color: rgb(241, 242, 244) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n    // ::v-deep .el-upload--picture-card{\r\n    //   width: 100px;\r\n    //   height: 100px;\r\n    // }\r\n\r\n    .searchButton {\r\n        display: inline-block;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .addButton {\r\n        display: inline-block;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .uploadButton {\r\n        display: inline-block;\r\n        margin-right: 10px;\r\n    }\r\n\r\n    .el-upload {\r\n        // width: 100px;\r\n        // height: 40px;\r\n        // line-height: 100px;\r\n        display: inline-block;\r\n        outline: none;\r\n        border: none;\r\n    }\r\n\r\n</style>\r\n", "import script from \"./OwnerInfo.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./OwnerInfo.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./OwnerInfo.vue?vue&type=style&index=0&id=a64fe35a&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-a64fe35a\"]])\n\nexport default __exports__", "module.exports = __webpack_public_path__ + \"img/addCarCode.f9b0010a.svg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./OwnerInfo.vue?vue&type=style&index=0&id=a64fe35a&lang=scss&scoped=true\""], "sourceRoot": ""}