{"version": 3, "sources": ["webpack:///./src/views/admin/VehicleReservation.vue?2d3d", "webpack:///./src/icons/svg-black/VehicleReservation.svg", "webpack:///./src/views/admin/VehicleReservation.vue", "webpack:///./src/views/admin/VehicleReservation.vue?51e3"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "rules", "yardName", "required", "message", "trigger", "plateNumber", "merchantName", "notifierName", "appointmentTime", "remark", "form", "reactive", "data", "id", "yardCode", "channelName", "vehicleClassification", "releaseReason", "enterTime", "leaveTime", "appointmentFlag", "reserveFlag", "tableRowClassName", "row", "rowIndex", "console", "log", "cellStyle", "column", "columnIndex", "style", "padding", "onReset", "applicantUserId", "ref", "value", "localStorage", "getItem", "query", "pageNum", "pageSize", "tableData", "selectData", "pageTotal", "dialogVisible", "dialogVisibleUpdate", "getData", "request", "get", "params", "then", "res", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleReservation", "ElMessageBox", "confirm", "type", "post", "ElMessage", "error", "success", "catch", "selectChanged", "delBatch", "ids", "map", "item", "response", "code", "msg", "handleAdd", "handleEdit", "yardNameList", "channelNameList", "vehicleClassificationList", "merchantNameList", "releaseReasonList", "notifierNameList", "appointmentTimeList", "changeYardName", "changeMerchantName", "convertToUpperCase", "toUpperCase", "trim", "formRef", "save", "length", "alert", "test", "chineseCharacters", "match", "validate", "valid", "url", "method", "update", "__exports__"], "mappings": "2IAAA,W,qBCAAA,EAAOC,QAAU,IAA0B,uC,meC2MrCC,EAAO,+B,4CACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CAEV,CAAEC,MAAO,OAAQC,KAAM,YAEvB,CAAED,MAAO,OAAQC,KAAM,eAEvB,CAAED,MAAO,OAAQC,KAAM,gBACvB,CAAED,MAAO,QAASC,KAAM,gBACxB,CAAED,MAAO,OAAQC,KAAM,mBAGvB,CAAED,MAAO,KAAMC,KAAM,UAIrB,CAAED,MAAO,OAAQC,KAAM,eAYrBC,EAAQ,CACVC,SAAU,CACN,CACIC,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBC,YAAa,CACT,CACIH,UAAU,EACVC,QAAS,SACTC,QAAS,SAGjBE,aAAc,CACV,CACIJ,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBG,aAAc,CACV,CACIL,UAAU,EACVC,QAAS,WACTC,QAAS,WAGjBI,gBAAiB,CACb,CACIN,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBK,OAAQ,CACJ,CACIP,UAAU,EACVC,QAAS,UACTC,QAAS,UAIfM,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJC,SAAU,GACVb,SAAU,GACVc,YAAa,GACbV,YAAa,GACbW,sBAAuB,GACvBV,aAAc,GACdW,cAAe,GACfV,aAAc,GACdW,UAAW,GACXC,UAAW,GACXV,OAAQ,GACRW,iBAAkB,EAClBC,aAAc,KAShBC,EAAoBA,EAAGC,MAAKC,eAEzBA,EAAW,GAAK,GAAK,GACtBC,QAAQC,IAAIF,GACL,YACCA,EAAW,GAAK,GAAK,GAC7BC,QAAQC,IAAIF,GACL,iBAFJ,EAMLG,EAAYA,EAAGJ,MAAKK,SAAQJ,WAAUK,kBACxC,IAAIC,EAAQ,CAAEC,QAAS,WACvB,OAAOD,GAGLE,EAAUA,KACZtB,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKE,SAAW,GACrBJ,EAAKE,KAAKX,SAAW,GACrBS,EAAKE,KAAKG,YAAc,GACxBL,EAAKE,KAAKP,YAAc,GACxBK,EAAKE,KAAKI,sBAAwB,GAClCN,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKH,OAAS,IAKjBwB,GAHWC,kBAAI,GACLA,iBAAI,IAEIA,iBAAI,KAC5BD,EAAgBE,MAAQC,aAAaC,QAAQ,UAE7C,MAAMC,EAAQ3B,sBAAS,CACnBN,YAAa,GACbJ,SAAU,GACVsC,QAAS,EACTC,SAAU,KAERC,EAAYP,iBAAI,IAChBQ,EAAaR,iBAAI,IACjBS,EAAYT,iBAAI,GAEhBU,GADSR,aAAaC,QAAQ,UACdH,kBAAI,IACpBW,EAAsBX,kBAAI,GAI1BY,GAH2BZ,kBAAI,GAGrBY,KACZC,OACKC,IAAIvD,EAAO,OAAQ,CAChBwD,OAAQX,IAEXY,KAAMC,IACHV,EAAUN,MAAQgB,EAAIvC,KAAKwC,QAC3BT,EAAUR,MAAQgB,EAAIvC,KAAKyC,MAC3B5B,QAAQC,IAAIyB,EAAIvC,UAG5BkC,IAEA,MAAMQ,EAAeA,KACjBhB,EAAMC,QAAU,EAChBO,KAGES,EAAoBC,IACtBlB,EAAME,SAAWgB,EACjBV,KAGEW,EAAoBD,IACtBlB,EAAMC,QAAUiB,EAChBV,KAwBEY,EAAqBnC,IAEvBoC,OAAaC,QAAQ,iBAAkB,KAAM,CACzCC,KAAM,YAELX,KAAK,KACFH,OAAQe,KAAK,6CAA8CvC,GAAK2B,KAAMC,IAC7DA,EAAIvC,KAAKA,KAIVmD,OAAUC,MAAM,WAHhBD,OAAUE,QAAQ,UAClBnB,SAMXoB,MAAM,SAITC,EAAiBX,IACnBd,EAAWP,MAAQqB,EACnB/B,QAAQC,IAAIgB,EAAWP,QAGrBiC,EAAWA,KACb,MAAMC,EAAM3B,EAAWP,MAAMmC,IAAIC,GAAQA,EAAK1D,IAC9CY,QAAQC,IAAI2C,GAEZtB,OAAQe,KAAK,0CAA2CO,GACnDnB,KAAKsB,IACF/C,QAAQC,IAAI8C,GACS,GAAjBA,EAASC,MACTV,OAAUE,QAAQ,WAElBnB,KAEAiB,OAAUC,MAAMQ,EAASE,QAMnCC,EAAYA,KACd3C,IACAY,EAAcT,OAAQ,GAIpByC,GADc1C,kBAAI,GACJX,IAChBsB,EAAoBV,OAAQ,EAC5BzB,EAAKE,KAAKC,GAAKU,EAAIV,GACnBH,EAAKE,KAAKE,SAAWS,EAAIT,SACzBJ,EAAKE,KAAKX,SAAWsB,EAAItB,SACzBS,EAAKE,KAAKG,YAAcQ,EAAIR,YAC5BL,EAAKE,KAAKP,YAAckB,EAAIlB,YAC5BK,EAAKE,KAAKI,sBAAwBO,EAAIP,sBACtCN,EAAKE,KAAKN,aAAeiB,EAAIjB,aAC7BI,EAAKE,KAAKK,cAAgBM,EAAIN,cAC9BP,EAAKE,KAAKL,aAAegB,EAAIhB,aAC7BG,EAAKE,KAAKJ,gBAAkBe,EAAIf,gBAChCE,EAAKE,KAAKH,OAASc,EAAId,SAGrBoE,GADe3C,iBAAI,IACJA,iBAAI,KACnB4C,EAAkB5C,iBAAI,IACtB6C,EAA4B7C,iBAAI,IAChC8C,EAAmB9C,iBAAI,IACvB+C,EAAoB/C,iBAAI,IACxBgD,EAAmBhD,iBAAI,IACvBiD,EAAsBjD,iBAAI,IAChCa,OAAQC,IAAI,8BAA8BE,KAAMC,IAC5C0B,EAAa1C,MAAQgB,EAAIvC,OAE7BmC,OAAQC,IAAI,wDAAwDE,KAC/DC,IACG4B,EAA0B5C,MAAQgB,EAAIvC,OAE9CmC,OAAQC,IAAI,sCAAsCE,KAC7CC,IACG6B,EAAiB7C,MAAQgB,EAAIvC,OAErCmC,OAAQC,IAAI,wCAAwCE,KAC/CC,IACG8B,EAAkB9C,MAAQgB,EAAIvC,OAEtC,MAAMwE,EAAiBA,KACnB3D,QAAQC,IAAIhB,EAAKE,KAAKE,UACtBiC,OACKC,IAAI,6BACD,CACIC,OAAQ,CACJhD,SAAUS,EAAKE,KAAKX,YAG/BiD,KAAMC,IACHzC,EAAKE,KAAKG,YAAc,GACxBL,EAAKE,KAAKI,sBAAwB,GAClCN,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKE,SAAWqC,EAAIvC,KAAK,GAC9BmC,OACKC,IAAI,iDACD,CACIC,OAAQ,CACJnC,SAAUqC,EAAIvC,KAAK,MAG9BsC,KAAMC,IACH1B,QAAQC,IAAI,SAAUhB,EAAKE,KAAKE,UAChCJ,EAAKE,KAAKI,sBAAwB,GAClCN,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKK,cAAgB,GAC1B6D,EAAgB3C,MAAQgB,EAAIvC,UAM1CyE,EAAqBA,KACvBtC,OACKC,IAAI,qCACD,CACIC,OAAQ,CACJ3C,aAAcI,EAAKE,KAAKN,gBAGnC4C,KAAMC,IACHzC,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKK,cAAgB,GAC1BiE,EAAiB/C,MAAQgB,EAAIvC,QAGnC0E,EAAqBA,KACvB5E,EAAKE,KAAKP,YAAcK,EAAKE,KAAKP,YAAYkF,cAC9C7E,EAAKE,KAAKP,YAAcK,EAAKE,KAAKP,YAAYmF,QAE5CC,EAAUvD,iBAAI,MACdwD,EAAOA,KACT,GAAIhF,EAAKE,KAAKP,YAAYsF,OAAS,GAAKjF,EAAKE,KAAKP,YAAYsF,OAAS,EAGnE,OAFAC,MAAM,oBACNlF,EAAKE,KAAKP,YAAc,IAErB,GAAI,kBAAkBwF,KAAKnF,EAAKE,KAAKP,aAAc,CAEtD,MAAMyF,EAAoBpF,EAAKE,KAAKP,YAAY0F,MAAM,oBACtD,GAAID,GAAqBA,EAAkBH,OAAS,EAGhD,YADAjF,EAAKE,KAAKP,YAAc,IAKhCoF,EAAQtD,MAAM6D,SAAUC,IACpB,IAAIA,EAiCA,OAAO,EAhCPlD,eAAQ,CACJmD,IAAK,qCACLC,OAAQ,OACRvF,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBb,SAAUS,EAAKE,KAAKX,SACpBc,YAAaL,EAAKE,KAAKG,YACvBV,YAAaK,EAAKE,KAAKP,YACvBW,sBAAuBN,EAAKE,KAAKI,sBACjCV,aAAcI,EAAKE,KAAKN,aACxBW,cAAeP,EAAKE,KAAKK,cACzBV,aAAcG,EAAKE,KAAKL,aACxBC,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvByC,KAAMC,IACL1B,QAAQC,IAAI,QACZD,QAAQC,IAAIyB,GACZ1B,QAAQC,IAAIyB,EAAIvC,MAChBF,EAAKE,KAAO,GACS,MAAjBuC,EAAIvC,KAAK6D,MACT3B,IACAiB,OAAUE,QAAQ,SAElBrB,EAAcT,OAAQ,IAEtBS,EAAcT,OAAQ,EACtB4B,OAAUC,MAAMb,EAAIuB,WAQlC0B,EAASA,KACX,GAAI1F,EAAKE,KAAKP,YAAYsF,OAAS,GAAKjF,EAAKE,KAAKP,YAAYsF,OAAS,EAGnE,OAFAC,MAAM,oBACNlF,EAAKE,KAAKP,YAAc,IAErB,GAAI,kBAAkBwF,KAAKnF,EAAKE,KAAKP,aAAc,CAEtD,MAAMyF,EAAoBpF,EAAKE,KAAKP,YAAY0F,MAAM,oBACtD,GAAID,GAAqBA,EAAkBH,OAAS,EAGhD,YADAjF,EAAKE,KAAKP,YAAc,IAKhCoF,EAAQtD,MAAM6D,SAAUC,IACpB,IAAIA,EAiCA,OAAO,EAhCPlD,eAAQ,CACJmD,IAAK,qCACLC,OAAQ,OACRvF,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBb,SAAUS,EAAKE,KAAKX,SACpBc,YAAaL,EAAKE,KAAKG,YACvBV,YAAaK,EAAKE,KAAKP,YACvBW,sBAAuBN,EAAKE,KAAKI,sBACjCV,aAAcI,EAAKE,KAAKN,aACxBW,cAAeP,EAAKE,KAAKK,cACzBV,aAAcG,EAAKE,KAAKL,aACxBC,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvByC,KAAMC,IACL1B,QAAQC,IAAI,QACZD,QAAQC,IAAIyB,GACZ1B,QAAQC,IAAIyB,EAAIvC,MAChBF,EAAKE,KAAO,GACS,MAAjBuC,EAAIvC,KAAK6D,MACT3B,IACAiB,OAAUE,QAAQ,SAElBpB,EAAoBV,OAAQ,IAE5BU,EAAoBV,OAAQ,EAC5B4B,OAAUC,MAAMb,EAAIuB,W,u2XCvnBxC,MAAM2B,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD", "file": "js/chunk-b805e758.2995109b.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./VehicleReservation.vue?vue&type=style&index=0&id=7e70eef6&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/VehicleReservation.63e19717.svg\";", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/VehicleReservation.svg\"></i> 外来车辆信息管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                        <el-input v-model=\"query.yardName\" placeholder=\"车场名称\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"车牌号码\">\r\n                        <el-input v-model=\"query.plateNumber\" placeholder=\"车牌号码\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\">搜索\r\n                    </el-button>\r\n                    <el-button type=\"primary\" class=\"addButton\" @click=\"handleAdd\">新增预约车辆\r\n                    </el-button>\r\n                    <el-button type=\"danger\" class=\"addButton\" @click=\"delBatch()\">批量删除\r\n                    </el-button>\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\" @selection-change=\"selectChanged\">\r\n                <el-table-column type=\"selection\" width=\"55px\"> </el-table-column>\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in props\" :key=\"item.prop\" align=\"center\" width=\"200px\" height=\"10px\">\r\n                </el-table-column>\r\n                <!-- <el-table-column label=\"预约状态\" prop=\"appointmentFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 0\" effect=\"dark\" size=\"large\">已预约\r\n                        </el-tag>\r\n                    </template>\r\n</el-table-column> -->\r\n                <el-table-column label=\"入场状态\" prop=\"reserveFlag\" align=\"center\" width=\"200px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"danger\" v-if=\"scope.row.reserveFlag === 0\" effect=\"dark\" size=\"large\">未入场\r\n                        </el-tag>\r\n                        <el-tag type=\"success\" v-else-if=\"scope.row.reserveFlag === 1\" effect=\"dark\" size=\"large\">已入场\r\n                        </el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"280px\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\">编辑\r\n                        </el-button>\r\n                        <!--<el-button type=\"text\"  icon=\"el-icon-delete\"\r\n                            @click=\"handleDelete(scope.$index, scope.row.id)\">删除\r\n                        </el-button>-->\r\n                        <el-button type=\"text\" icon=\"el-icon-position\" @click=\"handleReservation(scope.row)\">添加入场\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"添加外来车辆预约信息\" v-model=\"dialogVisible\" width=\"48%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"form.data.yardCode\" disabled></el-input>\r\n                    </el-form-item>\r\n                    <!-- <el-form-item label=\"入口通道\" prop=\"channelName\">\r\n                        <el-select v-model=\"form.data.channelName\" placeholder=\"请选择入口通道\">\r\n                            <el-option v-for=\"item in channelNameList\" :key=\"item.channelName\" :label=\"item.channelName\"\r\n                                :value=\"item.channelName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>-->\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 30%\"\r\n                            @input=\"convertToUpperCase\"></el-input>\r\n                    </el-form-item>\r\n                    <!-- <el-form-item label=\"车辆分类\" prop=\"vehicleClassification\">\r\n                        <el-select v-model=\"form.data.vehicleClassification\" placeholder=\"请选择车辆分类\">\r\n                            <el-option v-for=\"item in vehicleClassificationList\" :key=\"item.vehicleClassification\"\r\n                                :label=\"item.vehicleClassification\" :value=\"item.vehicleClassification\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>-->\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\"\r\n                                :label=\"item.merchantName\" :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\"\r\n                                :label=\"item.notifierName\" :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <!--<el-form-item label=\"放行原因\" prop=\"releaseReason\">\r\n                        <el-select v-model=\"form.data.releaseReason\" placeholder=\"请选择放行原因\">\r\n                            <el-option v-for=\"item in releaseReasonList\" :key=\"item.releaseReason\"\r\n                                :label=\"item.releaseReason\" :value=\"item.releaseReason\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>-->\r\n                    <el-form-item label=\"预约时间\" prop=\"appointmentTime\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 70%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"修改外来车辆预约信息\" v-model=\"dialogVisibleUpdate\" width=\"48%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"form.data.yardCode\" disabled></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 30%\"\r\n                            @input=\"convertToUpperCase\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\"\r\n                                :label=\"item.merchantName\" :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\"\r\n                                :label=\"item.notifierName\" :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"预约时间\" prop=\"appointmentTime\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 70%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"update\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\n\r\nconst root = \"/parking/vehicleReservation/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    // { label: \"车场编码\", prop: \"yardCode\" },\r\n    { label: \"车场名称\", prop: \"yardName\" },\r\n    // { label: \"入场通道\", prop: \"channelName\" },\r\n    { label: \"车牌号码\", prop: \"plateNumber\" },\r\n    // { label: \"车辆分类\", prop: \"vehicleClassification\" },\r\n    { label: \"商户名称\", prop: \"merchantName\" },\r\n    { label: \"通知人姓名\", prop: \"notifierName\" },\r\n    { label: \"预约时间\", prop: \"appointmentTime\" },\r\n    // { label: \"进场时间\", prop: \"enterTime\" },\r\n    // { label: \"离场时间\", prop: \"leaveTime\" },\r\n    { label: \"备注\", prop: \"remark\" },\r\n    // { label: \"进场车辆类型\", prop: \"enterVipType\" },\r\n    // { label: \"进场类型\", prop: \"enterType\" },\r\n    // { label: \"创建时间\", prop: \"createTime\" },\r\n    { label: \"修改时间\", prop: \"updateTime\" },\r\n];\r\nconst button1 = [\r\n    { type: 'success', text: 'success' }\r\n]\r\nconst button2 = [\r\n    { type: 'primary', text: 'primary' },\r\n    { type: 'success', text: 'success' },\r\n    { type: 'info', text: 'info' },\r\n    { type: 'warning', text: 'warning' },\r\n    { type: 'danger', text: 'danger' },\r\n]\r\nconst rules = {\r\n    yardName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择车场名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    plateNumber: [\r\n        {\r\n            required: true,\r\n            message: \"请输入车牌号\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    merchantName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择商户名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    notifierName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择通知人姓名\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    appointmentTime: [\r\n        {\r\n            required: true,\r\n            message: \"请选择预约时间\",\r\n            trigger: \"change\"\r\n        },\r\n    ],\r\n    remark: [\r\n        {\r\n            required: true,\r\n            message: \"请输入备注信息\",\r\n            trigger: \"blur\"\r\n        },\r\n    ],\r\n};\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        yardCode: '',\r\n        yardName: '',\r\n        channelName: '',\r\n        plateNumber: '',\r\n        vehicleClassification: '',\r\n        merchantName: '',\r\n        releaseReason: '',\r\n        notifierName: '',\r\n        enterTime: '',\r\n        leaveTime: '',\r\n        remark: '',\r\n        appointmentFlag: -1,\r\n        reserveFlag: -1\r\n    },\r\n\r\n});\r\n\r\n// const handleExport = () => {\r\n//     window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n// };\r\n//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n    let style = { padding: '0px 3px' }\r\n    return style\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.yardCode = ''\r\n    form.data.yardName = ''\r\n    form.data.channelName = ''\r\n    form.data.plateNumber = ''\r\n    form.data.vehicleClassification = ''\r\n    form.data.merchantName = ''\r\n    form.data.releaseReason = ''\r\n    form.data.notifierName = ''\r\n    form.data.remark = ''\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\n\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n\r\nconst query = reactive({\r\n    plateNumber: \"\",\r\n    yardName: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10\r\n});\r\nconst tableData = ref([]);\r\nconst selectData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\nconst dialogVisibleUpdate = ref(false)\r\nconst dialogVisibleReservation = ref(false);\r\n\r\n// 获取表格数据\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"page\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n            console.log(res.data);\r\n        });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n        type: \"warning\",\r\n    })\r\n        .then(() => {\r\n            request.delete(root + sid).then((res) => {\r\n                if (res.data) {\r\n                    ElMessage.success(\"删除成功\");\r\n                    query.pageNum = 1;\r\n                    getData();\r\n                    tableData.value.splice(index, 1);\r\n                } else {\r\n                    ElMessage.error(\"删除失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n//添加入场操作 添加入场弹窗打开\r\nconst handleReservation = (row) => {\r\n    // 二次确认添加入场\r\n    ElMessageBox.confirm(\"确定要将此条数据添加入场吗？\", \"提示\", {\r\n        type: \"success\",\r\n    })\r\n        .then(() => {\r\n            request.post(\"/parking/vehicleReservation/addReservation\", row).then((res) => {\r\n                if (!res.data.data) {\r\n                    ElMessage.success(\"添加入场成功\");\r\n                    getData();\r\n                } else {\r\n                    ElMessage.error(\"添加入场失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n\r\nconst selectChanged = (val) => {\r\n    selectData.value = val;\r\n    console.log(selectData.value)\r\n    // 将selectData.value添加到数组中\r\n};\r\nconst delBatch = () => {\r\n    const ids = selectData.value.map(item => item.id);\r\n    console.log(ids)\r\n    //     const ids = selectData.value.map(item => item.id);\r\n    request.post('/parking/vehicleReservation/batchDelete', ids)\r\n        .then(response => {\r\n            console.log(response)\r\n            if (response.code == 0) {\r\n                ElMessage.success('批量删除成功!');\r\n                // 重新加载数据\r\n                getData();\r\n            } else {\r\n                ElMessage.error(response.msg);\r\n            }\r\n        })\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n    onReset();\r\n    dialogVisible.value = true;\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n    dialogVisibleUpdate.value = true\r\n    form.data.id = row.id\r\n    form.data.yardCode = row.yardCode\r\n    form.data.yardName = row.yardName\r\n    form.data.channelName = row.channelName\r\n    form.data.plateNumber = row.plateNumber\r\n    form.data.vehicleClassification = row.vehicleClassification\r\n    form.data.merchantName = row.merchantName\r\n    form.data.releaseReason = row.releaseReason\r\n    form.data.notifierName = row.notifierName\r\n    form.data.appointmentTime = row.appointmentTime\r\n    form.data.remark = row.remark\r\n};\r\nconst yardCodeList = ref([]);\r\nconst yardNameList = ref([]);\r\nconst channelNameList = ref([]);\r\nconst vehicleClassificationList = ref([]);\r\nconst merchantNameList = ref([]);\r\nconst releaseReasonList = ref([]);\r\nconst notifierNameList = ref([]);\r\nconst appointmentTimeList = ref([]);\r\nrequest.get(\"/parking/yardInfo/yardName\").then((res) => {\r\n    yardNameList.value = res.data;\r\n});\r\nrequest.get(\"/parking/vehicleClassification/vehicleClassification\").then(\r\n    (res) => {\r\n        vehicleClassificationList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/notifierInfo/merchantName\").then(\r\n    (res) => {\r\n        merchantNameList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/releaseReason/releaseReason\").then(\r\n    (res) => {\r\n        releaseReasonList.value = res.data;\r\n    });\r\nconst changeYardName = () => {\r\n    console.log(form.data.yardCode);\r\n    request\r\n        .get(\"/parking/yardInfo/yardCode\",\r\n            {\r\n                params: {\r\n                    yardName: form.data.yardName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.channelName = \"\";\r\n            form.data.vehicleClassification = \"\";\r\n            form.data.notifierName = \"\";\r\n            form.data.merchantName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            form.data.yardCode = res.data[0]\r\n            request\r\n                .get(\"/parking/vehicleReservation/aikeGetChannelInfo\",\r\n                    {\r\n                        params: {\r\n                            yardCode: res.data[0]\r\n                        },\r\n                    })\r\n                .then((res) => {\r\n                    console.log(\"传递的参数为\", form.data.yardCode)\r\n                    form.data.vehicleClassification = \"\";\r\n                    form.data.notifierName = \"\";\r\n                    form.data.merchantName = \"\";\r\n                    form.data.releaseReason = \"\";\r\n                    channelNameList.value = res.data\r\n                });\r\n        });\r\n\r\n\r\n};\r\nconst changeMerchantName = () => {\r\n    request\r\n        .get(\"/parking/notifierInfo/notifierName\",\r\n            {\r\n                params: {\r\n                    merchantName: form.data.merchantName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.notifierName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            notifierNameList.value = res.data;\r\n        });\r\n};\r\nconst convertToUpperCase = () => {\r\n    form.data.plateNumber = form.data.plateNumber.toUpperCase();\r\n    form.data.plateNumber = form.data.plateNumber.trim();\r\n};\r\nconst formRef = ref(null);\r\nconst save = () => {\r\n    if (form.data.plateNumber.length < 7 || form.data.plateNumber.length > 8) {\r\n        alert('输入长度必须为7-8位');\r\n        form.data.plateNumber = \"\";\r\n        return;\r\n    } else if (/[\\u4e00-\\u9fa5]/.test(form.data.plateNumber)) {\r\n        // 检查输入值是否包含多个汉字\r\n        const chineseCharacters = form.data.plateNumber.match(/[\\u4e00-\\u9fa5]/g);\r\n        if (chineseCharacters && chineseCharacters.length > 2) {\r\n            ('除第一个和最后一个外不允许输入多个汉字');\r\n            form.data.plateNumber = \"\";\r\n            return;\r\n        }\r\n    }\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            request({\r\n                url: \"/parking/vehicleReservation/insert\",\r\n                method: \"POST\",\r\n                data: {\r\n                    id: form.data.id,\r\n                    yardCode: form.data.yardCode,\r\n                    yardName: form.data.yardName,\r\n                    channelName: form.data.channelName,\r\n                    plateNumber: form.data.plateNumber,\r\n                    vehicleClassification: form.data.vehicleClassification,\r\n                    merchantName: form.data.merchantName,\r\n                    releaseReason: form.data.releaseReason,\r\n                    notifierName: form.data.notifierName,\r\n                    appointmentTime: form.data.appointmentTime,\r\n                    remark: form.data.remark\r\n                },\r\n            }).then((res) => {\r\n                console.log(\"测试页面\")\r\n                console.log(res)\r\n                console.log(res.data)\r\n                form.data = {}\r\n                if (res.data.code != null) {\r\n                    getData()\r\n                    ElMessage.success(\"添加成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisible.value = false\r\n                } else {\r\n                    dialogVisible.value = false\r\n                    ElMessage.error(res.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\nconst update = () => {\r\n    if (form.data.plateNumber.length < 7 || form.data.plateNumber.length > 8) {\r\n        alert('输入长度必须为7-8位');\r\n        form.data.plateNumber = \"\";\r\n        return;\r\n    } else if (/[\\u4e00-\\u9fa5]/.test(form.data.plateNumber)) {\r\n        // 检查输入值是否包含多个汉字\r\n        const chineseCharacters = form.data.plateNumber.match(/[\\u4e00-\\u9fa5]/g);\r\n        if (chineseCharacters && chineseCharacters.length > 2) {\r\n            ('除第一个和最后一个外不允许输入多个汉字');\r\n            form.data.plateNumber = \"\";\r\n            return;\r\n        }\r\n    }\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            request({\r\n                url: \"/parking/vehicleReservation/update\",\r\n                method: \"POST\",\r\n                data: {\r\n                    id: form.data.id,\r\n                    yardCode: form.data.yardCode,\r\n                    yardName: form.data.yardName,\r\n                    channelName: form.data.channelName,\r\n                    plateNumber: form.data.plateNumber,\r\n                    vehicleClassification: form.data.vehicleClassification,\r\n                    merchantName: form.data.merchantName,\r\n                    releaseReason: form.data.releaseReason,\r\n                    notifierName: form.data.notifierName,\r\n                    appointmentTime: form.data.appointmentTime,\r\n                    remark: form.data.remark\r\n                },\r\n            }).then((res) => {\r\n                console.log(\"修改页面\")\r\n                console.log(res)\r\n                console.log(res.data)\r\n                form.data = {}\r\n                if (res.data.code != null) {\r\n                    getData()\r\n                    ElMessage.success(\"修改成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisibleUpdate.value = false\r\n                } else {\r\n                    dialogVisibleUpdate.value = false\r\n                    ElMessage.error(res.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n    background-color: rgb(245, 247, 250) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>", "import script from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VehicleReservation.vue?vue&type=style&index=0&id=7e70eef6&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7e70eef6\"]])\n\nexport default __exports__"], "sourceRoot": ""}