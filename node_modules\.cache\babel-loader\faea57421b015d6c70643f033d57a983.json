{"remainingRequest": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\DeviceInfo.vue?vue&type=script&setup=true&lang=js", "dependencies": [{"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\DeviceInfo.vue", "mtime": 1700016961707}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "useRouter", "reactive", "ref", "request", "ElMessage", "ElMessageBox", "useStore", "root", "router", "route", "store", "props", "label", "prop", "handleClose", "done", "confirm", "then", "form", "data", "catch", "Typeoptions", "value", "deviceId", "purchaseId", "deviceCode", "model", "deviceType", "deviceStatus", "purchaseTime", "handleEdit", "row", "dialogVisible", "viewShow", "viewShowtable", "handleExport", "window", "location", "href", "applicantUserId", "localStorage", "getItem", "departmentList", "get", "res", "query", "deviceName", "departmentId", "pageNum", "pageSize", "tableData", "pageTotal", "userId", "getData", "params", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "type", "delete", "success", "splice", "error", "handleAdd", "editVisible", "purchaseList", "formRef", "save", "validate", "valid", "method", "url", "code", "msg"], "sources": ["F:/ParkingDemoAKEHRBU/ParkingManageDemo/manage-front/src/views/admin/DeviceInfo.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i class=\"el-icon-location\"></i> 设备信息\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n            :inline=\"true\"\r\n            :model=\"query\"\r\n            class=\"demo-form-inline\"\r\n            label-width=\"80px\"\r\n        >\r\n          <el-form-item label=\"设备名\">\r\n            <el-input\r\n                v-model=\"query.deviceName\"\r\n                placeholder=\"设备名称\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          \r\n          <el-form-item label=\"部门\" prop=\"departmentId\">\r\n            <el-select\r\n                v-model=\"query.departmentId\"\r\n                placeholder=\"请选择部门\"\r\n                clearable\r\n            >\r\n              <el-option\r\n                  v-for=\"item in departmentList\"\r\n                  :key=\"item.departmentId\"\r\n                  :label=\"item.departmentName\"\r\n                  :value=\"item.departmentId\"\r\n                  clearable\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <!--          <el-form-item prop=\"applicationTime\" label=\"申请时间\">-->\r\n          <!--         -->\r\n          <!--          </el-form-item>-->\r\n          <el-form-item label=\"设备类型\">\r\n            <el-select v-model=\"query.deviceType\" placeholder=\"请选择类型\">\r\n              <el-option label=\"生产设备\" value=\"1\"/>\r\n              <el-option label=\"电气设备\" value=\"2\"/>\r\n              <el-option label=\"特种设备\" value=\"3\"/>\r\n              <el-option label=\"精密设备\" value=\"4\"/>\r\n              <el-option label=\"动力设备\" value=\"5\"/>\r\n              <el-option label=\"压力设备\" value=\"6\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <!--          <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              @click=\"handleAdd\"-->\r\n          <!--          >新增-->\r\n<!--          </el-button>-->\r\n          <el-button\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n          >导出\r\n          </el-button\r\n          >\r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n          :data=\"tableData\"\r\n          border\r\n          class=\"table\"\r\n          ref=\"multipleTable\"\r\n          header-cell-class-name=\"table-header\"\r\n      >\r\n        <el-table-column\r\n            :show-overflow-tooltip=\"true\"\r\n            :prop=\"item.prop\"\r\n            :label=\"item.label\"\r\n            v-for=\"item in props\"\r\n            :key=\"item.prop\"\r\n            align=\"center\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column label=\"设备状态\" prop=\"deviceStatus\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-tag type=\"success\" v-if=\"scope.row.deviceStatus === 1 \">空闲</el-tag>\r\n            <el-tag type=\"success\" v-else-if=\"scope.row.deviceStatus === 2 \">使用中</el-tag>\r\n            <el-tag type=\"success\" v-else-if=\"scope.row.deviceStatus === 3 \">待维修</el-tag>\r\n            <el-tag type=\"success\" v-else-if=\"scope.row.deviceStatus === 4 \">租赁</el-tag>\r\n            <el-tag type=\"success\" v-else-if=\"scope.row.deviceStatus === 5 \">调拨</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.deviceStatus === 6 \">报废</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.deviceStatus === 7 \">维修失败</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.deviceStatus === 8 \">租赁申请中</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.deviceStatus === 9 \">报废中</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column>\r\n\r\n          <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleEdit(scope.row)\"\r\n                  v-if=\"scope.row.deviceStatus==1 ||scope.row.deviceStatus==2 \"\r\n              >编辑\r\n              </el-button>\r\n              <el-button\r\n\r\n                  v-if=\"!(scope.row.deviceStatus==1 ||scope.row.deviceStatus==2) \"\r\n              >无操作\r\n              </el-button>\r\n              <el-button\r\n                  type=\"text\"\r\n                  icon=\"el-icon-delete\"\r\n                  class=\"red\"\r\n                  @click=\"handleDelete(scope.$index, scope.row.deviceId)\"\r\n                  v-if=\"scope.row.deviceStatus==1 ||scope.row.deviceStatus==2 \"\r\n              >删除\r\n              </el-button>\r\n\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            :currentPage=\"query.pageNum\"\r\n            :page-sizes=\"[10, 20, 40]\"\r\n            :page-size=\"query.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"pageTotal\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"设备信息维护\" v-model=\"dialogVisible\" width=\"25% \" :before-close=\"handleClose\">\r\n        <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n          <el-form-item label=\"设备名称\" v-if=\"viewShowtable\">\r\n            <el-select v-model=\"form.data.purchaseId\" placeholder=\"请选择审批设备名\">\r\n              <el-option\r\n                  v-for=\"item in purchaseList\"\r\n                  :key=\"item.purchaseId\"\r\n                  :label=\"item.deviceName\"\r\n                  :value=\"item.purchaseId\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"规格型号\">\r\n            <el-input v-model=\"form.data.model\" style=\"width: 40%\" disabled></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"设备类型\">\r\n            <el-select v-model=\"form.data.deviceType\" placeholder=\"请选择类型\" disabled>\r\n              <el-option\r\n                  v-for=\"item in Typeoptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"设备状态\" v-if=\"viewShow\">\r\n            <el-radio-group v-model=\"form.data.deviceStatus\">\r\n              <el-radio :label=1>空闲</el-radio>\r\n              <el-radio :label=2>使用</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n        </el-form>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport {useRoute, useRouter} from \"vue-router\";\r\nimport {reactive, ref} from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport {ElMessage, ElMessageBox} from \"element-plus\";\r\nimport {useStore} from \"vuex\";\r\n\r\nconst root = \"/parking/device/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  {label: \"设备编码\", prop: \"deviceCode\"},\r\n  {label: \"设备名称\", prop: \"deviceName\"},\r\n  {label: \"设备价格\", prop: \"devicePrice\"},\r\n  {label: \"规格型号\", prop: \"model\"},\r\n  {label: \"设备类型\", prop: \"deviceTypeName\"},\r\n  // {label: \"技术文档\", prop: \"technicalDocumentation\"},\r\n  {label: \"部门名称\", prop: \"departmentName\"},\r\n  // {label: \"使用情况\", prop: \"deviceStatus\"},\r\n  {label: \"登记日期\", prop: \"purchaseTime\"},\r\n];\r\nconst handleClose = (done) => {\r\n  ElMessageBox.confirm(\"确定放弃选择或者关闭吗?\")\r\n      .then(() => {\r\n        form.data = {}\r\n        done();\r\n      })\r\n      .catch(() => {\r\n        // catch error\r\n      });\r\n};\r\nconst Typeoptions = [\r\n  {\r\n    value: 1,\r\n    label: '生产设备',\r\n  },\r\n  {\r\n    value: 2,\r\n    label: '电气设备',\r\n  },\r\n  {\r\n    value: 3,\r\n    label: '特种设备',\r\n  },\r\n  {\r\n    value: 4,\r\n    label: '精密设备',\r\n  },\r\n  {\r\n    value: 5,\r\n    label: '动力设备',\r\n  },\r\n  {\r\n    value: 6,\r\n    label: '压力设备',\r\n  },\r\n]\r\nconst form = reactive({\r\n  data: {\r\n    deviceId: \"\",\r\n    purchaseId: \"\",\r\n    deviceCode: \"\",\r\n    model: \"\",\r\n    deviceType: \"\",\r\n    deviceStatus: \"\",\r\n    purchaseTime: \"\",\r\n  },\r\n});\r\nconst handleEdit = (row) => {\r\n  dialogVisible.value = true\r\n  viewShow.value = true\r\n  viewShowtable.value = false\r\n  form.data.deviceId = row.deviceId\r\n  form.data.deviceCode = row.deviceCode\r\n  form.data.model = row.model\r\n  form.data.deviceType = row.deviceType\r\n  form.data.deviceStatus = row.deviceStatus\r\n  form.data.purchaseTime = row.purchaseTime\r\n\r\n};\r\nconst handleExport = () => {\r\n  window.location.href = \"http://localhost:9999/device/exportDevice\";\r\n};\r\n\r\nconst viewShow = ref(false)\r\nconst viewShowtable = ref(false)\r\n// const content = ref(\"\");\r\n// const handleView = (row) => {\r\n//   console.log(\"\")\r\n//   if (row.fileReason !== null) {\r\n//     viewShow.value = true\r\n//     content.value = row.fileReason\r\n//   } else {\r\n//     ElMessage.info('没有审核原因');\r\n//   }\r\n// };\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n// alert(applicantUserId.value)\r\nconst departmentList = ref([]);\r\nrequest.get(\"/parking/department/listDepartment\").then((res) => {\r\n  departmentList.value = res.data;\r\n});\r\nconst query = reactive({\r\n  deviceName: \"\",\r\n  deviceCode: \"\",\r\n  departmentId: '',\r\n  deviceType: \"\",\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\n// 获取表格数据\r\nconst getData = () => {\r\n  request\r\n      .get(root + \"page\", {\r\n        params: query,\r\n      })\r\n      .then((res) => {\r\n        tableData.value = res.data.records;\r\n        pageTotal.value = res.data.total;\r\n      });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n  // 二次确认删除\r\n  ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n    type: \"warning\",\r\n  })\r\n      .then(() => {\r\n        request.delete(root + sid).then((res) => {\r\n          if (res.data) {\r\n            ElMessage.success(\"删除成功\");\r\n            tableData.value.splice(index, 1);\r\n          } else {\r\n            ElMessage.error(\"删除失败\");\r\n          }\r\n        });\r\n      })\r\n      .catch(() => {\r\n      });\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n  dialogVisible.value = true\r\n  viewShow.value = false\r\n  viewShowtable.value = true\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\n\r\n\r\nconst purchaseList = ref([]);\r\nrequest.get(\"/parking/purchase/listByType\").then((res) => {\r\n  purchaseList.value = res.data\r\n});\r\nconst formRef = ref(null);\r\nconst save = () => {\r\n  // 表单校验\r\n  formRef.value.validate((valid) => {\r\n    if (valid) {\r\n      var method = form.data.deviceId === \"\" ? \"POST\" : \"PUT\";\r\n      request({\r\n        url: \"/parking/device\",\r\n        method: method,\r\n        data: form.data,\r\n      }).then((res) => {\r\n\r\n        form.data = {}\r\n        if (res.code === null) {\r\n          getData()\r\n          ElMessage.success(\"提交成功！\");\r\n          // 关闭当前页面的标签页;\r\n          dialogVisible.value = false\r\n        } else {\r\n          dialogVisible.value = false\r\n          ElMessage.error(res.msg);\r\n        }\r\n      });\r\n    } else {\r\n      return false;\r\n    }\r\n  });\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;AAkMA,SAAQA,QAAQ,EAAEC,SAAS,QAAO,YAAY;AAC9C,SAAQC,QAAQ,EAAEC,GAAG,QAAO,KAAK;AACjC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAAQC,SAAS,EAAEC,YAAY,QAAO,cAAc;AACpD,SAAQC,QAAQ,QAAO,MAAM;AAE7B,MAAMC,IAAI,GAAG,kBAAkB;;;;IAC/B,MAAMC,MAAM,GAAGR,SAAS,CAAC,CAAC;IAC1B,MAAMS,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,MAAMW,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,MAAMK,KAAK,GAAG,CACZ;MAACC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAY,CAAC,EACnC;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAY,CAAC,EACnC;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAa,CAAC,EACpC;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO,CAAC,EAC9B;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB,CAAC;IACvC;IACA;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB,CAAC;IACvC;IACA;MAACD,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAc,CAAC,CACtC;IACD,MAAMC,WAAW,GAAIC,IAAI,IAAK;MAC5BV,YAAY,CAACW,OAAO,CAAC,cAAc,CAAC,CAC/BC,IAAI,CAAC,MAAM;QACVC,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;QACdJ,IAAI,CAAC,CAAC;MACR,CAAC,CAAC,CACDK,KAAK,CAAC,MAAM;QACX;MAAA,CACD,CAAC;IACR,CAAC;IACD,MAAMC,WAAW,GAAG,CAClB;MACEC,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,EACD;MACEU,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,EACD;MACEU,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,EACD;MACEU,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,EACD;MACEU,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,EACD;MACEU,KAAK,EAAE,CAAC;MACRV,KAAK,EAAE;IACT,CAAC,CACF;IACD,MAAMM,IAAI,GAAGjB,QAAQ,CAAC;MACpBkB,IAAI,EAAE;QACJI,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,EAAE;QACdC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IACF,MAAMC,UAAU,GAAIC,GAAG,IAAK;MAC1BC,aAAa,CAACV,KAAK,GAAG,IAAI;MAC1BW,QAAQ,CAACX,KAAK,GAAG,IAAI;MACrBY,aAAa,CAACZ,KAAK,GAAG,KAAK;MAC3BJ,IAAI,CAACC,IAAI,CAACI,QAAQ,GAAGQ,GAAG,CAACR,QAAQ;MACjCL,IAAI,CAACC,IAAI,CAACM,UAAU,GAAGM,GAAG,CAACN,UAAU;MACrCP,IAAI,CAACC,IAAI,CAACO,KAAK,GAAGK,GAAG,CAACL,KAAK;MAC3BR,IAAI,CAACC,IAAI,CAACQ,UAAU,GAAGI,GAAG,CAACJ,UAAU;MACrCT,IAAI,CAACC,IAAI,CAACS,YAAY,GAAGG,GAAG,CAACH,YAAY;MACzCV,IAAI,CAACC,IAAI,CAACU,YAAY,GAAGE,GAAG,CAACF,YAAY;IAE3C,CAAC;IACD,MAAMM,YAAY,GAAGA,CAAA,KAAM;MACzBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,2CAA2C;IACpE,CAAC;IAED,MAAML,QAAQ,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAMgC,aAAa,GAAGhC,GAAG,CAAC,KAAK,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMqC,eAAe,GAAGrC,GAAG,CAAC,EAAE,CAAC;IAC/BqC,eAAe,CAACjB,KAAK,GAAGkB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACtD;IACA,MAAMC,cAAc,GAAGxC,GAAG,CAAC,EAAE,CAAC;IAC9BC,OAAO,CAACwC,GAAG,CAAC,oCAAoC,CAAC,CAAC1B,IAAI,CAAE2B,GAAG,IAAK;MAC9DF,cAAc,CAACpB,KAAK,GAAGsB,GAAG,CAACzB,IAAI;IACjC,CAAC,CAAC;IACF,MAAM0B,KAAK,GAAG5C,QAAQ,CAAC;MACrB6C,UAAU,EAAE,EAAE;MACdrB,UAAU,EAAE,EAAE;MACdsB,YAAY,EAAE,EAAE;MAChBpB,UAAU,EAAE,EAAE;MACdqB,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,SAAS,GAAGhD,GAAG,CAAC,EAAE,CAAC;IACzB,MAAMiD,SAAS,GAAGjD,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMkD,MAAM,GAAGZ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,MAAMT,aAAa,GAAG9B,GAAG,CAAC,KAAK,CAAC;IAChC;IACA,MAAMmD,OAAO,GAAGA,CAAA,KAAM;MACpBlD,OAAO,CACFwC,GAAG,CAACpC,IAAI,GAAG,MAAM,EAAE;QAClB+C,MAAM,EAAET;MACV,CAAC,CAAC,CACD5B,IAAI,CAAE2B,GAAG,IAAK;QACbM,SAAS,CAAC5B,KAAK,GAAGsB,GAAG,CAACzB,IAAI,CAACoC,OAAO;QAClCJ,SAAS,CAAC7B,KAAK,GAAGsB,GAAG,CAACzB,IAAI,CAACqC,KAAK;MAClC,CAAC,CAAC;IACR,CAAC;IACDH,OAAO,CAAC,CAAC;IACT;IACA,MAAMI,YAAY,GAAGA,CAAA,KAAM;MACzBZ,KAAK,CAACG,OAAO,GAAG,CAAC;MACjBK,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMK,gBAAgB,GAAIC,GAAG,IAAK;MAChCd,KAAK,CAACI,QAAQ,GAAGU,GAAG;MACpBN,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMO,gBAAgB,GAAID,GAAG,IAAK;MAChCd,KAAK,CAACG,OAAO,GAAGW,GAAG;MACnBN,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMQ,YAAY,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;MACnC;MACA1D,YAAY,CAACW,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE;QACpCgD,IAAI,EAAE;MACR,CAAC,CAAC,CACG/C,IAAI,CAAC,MAAM;QACVd,OAAO,CAAC8D,MAAM,CAAC1D,IAAI,GAAGwD,GAAG,CAAC,CAAC9C,IAAI,CAAE2B,GAAG,IAAK;UACvC,IAAIA,GAAG,CAACzB,IAAI,EAAE;YACZf,SAAS,CAAC8D,OAAO,CAAC,MAAM,CAAC;YACzBhB,SAAS,CAAC5B,KAAK,CAAC6C,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;UAClC,CAAC,MAAM;YACL1D,SAAS,CAACgE,KAAK,CAAC,MAAM,CAAC;UACzB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,CACDhD,KAAK,CAAC,MAAM,CACb,CAAC,CAAC;IACR,CAAC;;IAED;IACA,MAAMiD,SAAS,GAAGA,CAAA,KAAM;MACtBrC,aAAa,CAACV,KAAK,GAAG,IAAI;MAC1BW,QAAQ,CAACX,KAAK,GAAG,KAAK;MACtBY,aAAa,CAACZ,KAAK,GAAG,IAAI;IAC5B,CAAC;IACD;IACA,MAAMgD,WAAW,GAAGpE,GAAG,CAAC,KAAK,CAAC;IAG9B,MAAMqE,YAAY,GAAGrE,GAAG,CAAC,EAAE,CAAC;IAC5BC,OAAO,CAACwC,GAAG,CAAC,8BAA8B,CAAC,CAAC1B,IAAI,CAAE2B,GAAG,IAAK;MACxD2B,YAAY,CAACjD,KAAK,GAAGsB,GAAG,CAACzB,IAAI;IAC/B,CAAC,CAAC;IACF,MAAMqD,OAAO,GAAGtE,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMuE,IAAI,GAAGA,CAAA,KAAM;MACjB;MACAD,OAAO,CAAClD,KAAK,CAACoD,QAAQ,CAAEC,KAAK,IAAK;QAChC,IAAIA,KAAK,EAAE;UACT,IAAIC,MAAM,GAAG1D,IAAI,CAACC,IAAI,CAACI,QAAQ,KAAK,EAAE,GAAG,MAAM,GAAG,KAAK;UACvDpB,OAAO,CAAC;YACN0E,GAAG,EAAE,iBAAiB;YACtBD,MAAM,EAAEA,MAAM;YACdzD,IAAI,EAAED,IAAI,CAACC;UACb,CAAC,CAAC,CAACF,IAAI,CAAE2B,GAAG,IAAK;YAEf1B,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;YACd,IAAIyB,GAAG,CAACkC,IAAI,KAAK,IAAI,EAAE;cACrBzB,OAAO,CAAC,CAAC;cACTjD,SAAS,CAAC8D,OAAO,CAAC,OAAO,CAAC;cAC1B;cACAlC,aAAa,CAACV,KAAK,GAAG,KAAK;YAC7B,CAAC,MAAM;cACLU,aAAa,CAACV,KAAK,GAAG,KAAK;cAC3BlB,SAAS,CAACgE,KAAK,CAACxB,GAAG,CAACmC,GAAG,CAAC;YAC1B;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC"}]}