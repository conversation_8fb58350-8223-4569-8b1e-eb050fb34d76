{"remainingRequest": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\AddVisitPurpose.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\AddVisitPurpose.vue", "mtime": 1692756218460}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["reactive", "ref", "ElMessage", "request", "useRoute", "useRouter", "useStore", "name", "setup", "root", "router", "route", "store", "rules", "reason", "required", "message", "trigger", "formRef", "form", "id", "sortno", "query", "get", "then", "res", "data", "onSubmit", "value", "validate", "valid", "method", "url", "code", "success", "commit", "$router", "$route", "push", "error", "msg", "onReset", "resetFields"], "sources": ["F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\AddVisitPurpose.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i class=\"el-icon-lx-calendar\"></i> 来访目的管理\r\n        </el-breadcrumb-item>\r\n        <el-breadcrumb-item>来访目的编辑</el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"form-box\">\r\n        <el-form ref=\"formRef\" :rules=\"rules\" :model=\"form\" label-width=\"80px\">\r\n          <el-form-item label=\"来访目的\" prop=\"reason\">\r\n            <el-input v-model=\"form.reason\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"序号\" prop=\"sortno\" :rules=\"[\r\n        { required: true, message: '联系电话 is required' },  ]\">\r\n            <el-input v-model=\"form.sortno\" maxlength=\"11\" type=\"number\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            <el-button @click=\"onReset\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {reactive, ref} from \"vue\";\r\nimport {ElMessage} from \"element-plus\";\r\nimport request from \"../../utils/request\";\r\nimport {useRoute, useRouter} from \"vue-router\";\r\nimport {useStore} from \"vuex\";\r\n\r\nexport default {\r\n  name: \"AddVisitPurpose\",\r\n  setup() {\r\n    const root = \"/parking/visitreason/\";\r\n    const router = useRouter();\r\n    const route = useRoute();\r\n    const store = useStore();\r\n    const rules = {\r\n      reason: [\r\n        {required: true, message: \"请输入来访目的\", trigger: \"blur\"},\r\n      ],\r\n    };\r\n    const formRef = ref(null);\r\n    var form = reactive({\r\n      id: \"\",\r\n      reason: \"\",\r\n      sortno: \"\",\r\n    });\r\n    if (route.query.id) {\r\n      request.get(root + route.query.id).then((res) => {\r\n        form.id = res.data.id\r\n        form.reason = res.data.reason\r\n        form.sortno =res.data.sortno\r\n      });\r\n    }\r\n    // 提交\r\n    const onSubmit = () => {\r\n      // 表单校验\r\n      formRef.value.validate((valid) => {\r\n        if (valid) {\r\n          var method = form.id === \"\" ? \"POST\" : \"PUT\";\r\n          request({\r\n            url: \"/parking/visitreason\",\r\n            method: method,\r\n            data: form,\r\n          }).then((res) => {\r\n            if (res.code === null) {\r\n              ElMessage.success(\"提交成功！\");\r\n              // 关闭当前页面的标签页;\r\n              store.commit(\"closeCurrentTag\", {\r\n                $router: router,\r\n                $route: route,\r\n              });\r\n              router.push(\"/admin/parking/visitPurpose\");\r\n            } else {\r\n              ElMessage.error(res.msg);\r\n            }\r\n\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    };\r\n    // 重置\r\n    const onReset = () => {\r\n      formRef.value.resetFields();\r\n    };\r\n\r\n    return {\r\n      rules,\r\n      formRef,\r\n      form,\r\n      onSubmit,\r\n      onReset,\r\n    };\r\n  },\r\n};\r\n</script>"], "mappings": ";AA+BA,SAAQA,QAAQ,EAAEC,GAAG,QAAO,KAAK;AACjC,SAAQC,SAAS,QAAO,cAAc;AACtC,OAAOC,OAAM,MAAO,qBAAqB;AACzC,SAAQC,QAAQ,EAAEC,SAAS,QAAO,YAAY;AAC9C,SAAQC,QAAQ,QAAO,MAAM;AAE7B,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAKA,CAAA,EAAG;IACN,MAAMC,IAAG,GAAI,uBAAuB;IACpC,MAAMC,MAAK,GAAIL,SAAS,CAAC,CAAC;IAC1B,MAAMM,KAAI,GAAIP,QAAQ,CAAC,CAAC;IACxB,MAAMQ,KAAI,GAAIN,QAAQ,CAAC,CAAC;IACxB,MAAMO,KAAI,GAAI;MACZC,MAAM,EAAE,CACN;QAACC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC;IAEzD,CAAC;IACD,MAAMC,OAAM,GAAIjB,GAAG,CAAC,IAAI,CAAC;IACzB,IAAIkB,IAAG,GAAInB,QAAQ,CAAC;MAClBoB,EAAE,EAAE,EAAE;MACNN,MAAM,EAAE,EAAE;MACVO,MAAM,EAAE;IACV,CAAC,CAAC;IACF,IAAIV,KAAK,CAACW,KAAK,CAACF,EAAE,EAAE;MAClBjB,OAAO,CAACoB,GAAG,CAACd,IAAG,GAAIE,KAAK,CAACW,KAAK,CAACF,EAAE,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAC/CN,IAAI,CAACC,EAAC,GAAIK,GAAG,CAACC,IAAI,CAACN,EAAC;QACpBD,IAAI,CAACL,MAAK,GAAIW,GAAG,CAACC,IAAI,CAACZ,MAAK;QAC5BK,IAAI,CAACE,MAAK,GAAGI,GAAG,CAACC,IAAI,CAACL,MAAK;MAC7B,CAAC,CAAC;IACJ;IACA;IACA,MAAMM,QAAO,GAAIA,CAAA,KAAM;MACrB;MACAT,OAAO,CAACU,KAAK,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAChC,IAAIA,KAAK,EAAE;UACT,IAAIC,MAAK,GAAIZ,IAAI,CAACC,EAAC,KAAM,EAAC,GAAI,MAAK,GAAI,KAAK;UAC5CjB,OAAO,CAAC;YACN6B,GAAG,EAAE,sBAAsB;YAC3BD,MAAM,EAAEA,MAAM;YACdL,IAAI,EAAEP;UACR,CAAC,CAAC,CAACK,IAAI,CAAEC,GAAG,IAAK;YACf,IAAIA,GAAG,CAACQ,IAAG,KAAM,IAAI,EAAE;cACrB/B,SAAS,CAACgC,OAAO,CAAC,OAAO,CAAC;cAC1B;cACAtB,KAAK,CAACuB,MAAM,CAAC,iBAAiB,EAAE;gBAC9BC,OAAO,EAAE1B,MAAM;gBACf2B,MAAM,EAAE1B;cACV,CAAC,CAAC;cACFD,MAAM,CAAC4B,IAAI,CAAC,6BAA6B,CAAC;YAC5C,OAAO;cACLpC,SAAS,CAACqC,KAAK,CAACd,GAAG,CAACe,GAAG,CAAC;YAC1B;UAEF,CAAC,CAAC;QACJ,OAAO;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;IACD;IACA,MAAMC,OAAM,GAAIA,CAAA,KAAM;MACpBvB,OAAO,CAACU,KAAK,CAACc,WAAW,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO;MACL7B,KAAK;MACLK,OAAO;MACPC,IAAI;MACJQ,QAAQ;MACRc;IACF,CAAC;EACH;AACF,CAAC"}]}