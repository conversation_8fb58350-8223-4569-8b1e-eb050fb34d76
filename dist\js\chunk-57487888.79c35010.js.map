{"version": 3, "sources": ["webpack:///./src/icons/svg-black/Patroller.svg", "webpack:///./src/views/admin/Patrol.vue", "webpack:///./src/views/admin/Patrol.vue?56e8", "webpack:///./src/views/admin/Patrol.vue?8c3a"], "names": ["module", "exports", "root", "baseURL", "useRouter", "useRoute", "useStore", "props", "label", "prop", "qrData", "reactive", "payUrl", "size", "name", "form", "data", "id", "province", "city", "district", "community", "usercode", "username", "phone", "status", "<PERSON>man", "tableRowClassName", "row", "rowIndex", "console", "log", "cellStyle", "column", "columnIndex", "style", "padding", "onReset", "viewShow", "arrayId", "ref", "content", "content1", "query", "departmentId", "deviceName", "applicationTime", "pageNum", "pageSize", "tableData", "pageTotal", "loginName", "localStorage", "getItem", "dialogVisible", "getData", "request", "get", "params", "then", "res", "value", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleAdd", "qrCodeVisible", "handleDownloadLocal", "myCanvas", "document", "getElementById", "a", "createElement", "href", "toDataURL", "download", "click", "createQrCode", "date", "Date", "time", "getTime", "timeunix", "parseInt", "handleEdit", "provinceList", "cityList", "districtList", "communityList", "changeProvince", "changeCity", "changeDistrict", "changeCommunity", "formRef", "save", "validate", "valid", "method", "url", "code", "msg", "__exports__"], "mappings": "mGAAAA,EAAOC,QAAU,IAA0B,8B,gnBCiPrCC,EAAO,mBAgMPC,EAAS,yC,gCA/LAC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACZ,CAACC,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,QACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,aACpB,CAACD,MAAO,QAASC,KAAM,YACvB,CAACD,MAAO,QAASC,KAAM,YACvB,CAACD,MAAO,QAASC,KAAM,SACvB,CAACD,MAAO,KAAMC,KAAM,WAGhBC,EAASC,sBAAS,CACtBC,OAAQ,WACRC,KAAM,IACNC,KAAK,KAEDC,EAAOJ,sBAAS,CACpBK,KAAM,CACJC,GAAI,GACJC,SAAU,GACVC,KAAM,GACNC,SAAU,GACVC,UAAW,GACXC,SAAU,GACVC,SAAU,GACVC,MAAO,GACPC,OAAQ,GACRC,UAAW,MAMTC,EAAoBA,EAAEC,MAAKC,eAE3BA,EAAW,GAAK,GAAK,GACnBC,QAAQC,IAAIF,GACX,YACGA,EAAW,GAAK,GAAK,GACzBC,QAAQC,IAAIF,GACX,iBAFF,EAMDG,EAAaA,EAAEJ,MAAKK,SAAQJ,WAASK,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,GAMLE,EAAUA,KACdtB,EAAKC,KAAKC,GAAK,GACfF,EAAKC,KAAKE,SAAW,GACrBH,EAAKC,KAAKG,KAAO,GACjBJ,EAAKC,KAAKI,SAAW,GACrBL,EAAKC,KAAKK,UAAY,GACtBN,EAAKC,KAAKM,SAAW,GACrBP,EAAKC,KAAKO,SAAW,GACrBR,EAAKC,KAAKQ,MAAM,GAChBT,EAAKC,KAAKS,OAAO,GACjBV,EAAKC,KAAKU,UAAW,IAkBjBY,GAhBY3B,sBAAS,CACzBO,SAAS,GACTC,KAAK,GACLC,SAAS,GACTC,UAAU,KAGSV,sBAAS,CAC5BO,SAAS,GACTC,KAAK,GACLC,SAAS,GACTC,UAAU,GACVC,SAAS,GACTC,SAAU,GACVgB,QAAQ,KAEOC,kBAAI,IAEfC,GADOD,mBACGA,iBAAI,KAWdE,GADYF,kBAAI,GACLA,iBAAI,KAUfG,EAAQhC,sBAAS,CACrBiC,aAAc,GACdC,WAAY,GACZC,gBAAiB,GACjBC,QAAS,EACTC,SAAU,KAGNC,EAAYT,iBAAI,IAGhBU,GAFWV,iBACf,IACgBA,iBAAI,IAChBW,EAAYC,aAAaC,QAAQ,aACjCC,EAAgBd,kBAAI,GAKpBe,GAJcf,kBAAI,GAIRe,KACdC,OACKC,IAAIvD,EAAO,YAAa,CACvBwD,OAAQf,IAETgB,KAAMC,IACLX,EAAUY,MAAQD,EAAI5C,KAAK8C,QAC3BZ,EAAUW,MAAQD,EAAI5C,KAAK+C,UAGnCR,IAEA,MAAMS,EAAeA,KACnBrB,EAAMI,QAAU,EAChBQ,KAIIU,EAAoBC,IACxBvB,EAAMK,SAAWkB,EACjBX,KAGIY,EAAoBD,IACxBvB,EAAMI,QAAUmB,EAChBX,KAGIa,EAAeA,CAACC,EAAOC,KAE3BC,OAAaC,QAAQ,UAAW,KAAM,CACpCC,KAAM,YAEHd,KAAK,KACJH,OAAQkB,OAAOxE,EAAOoE,GAAKX,KAAMC,IAC3BA,EAAI5C,MACN2D,OAAUC,QAAQ,QAClB3B,EAAUY,MAAMgB,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAIrBC,MAAM,SAKPC,EAAYA,KAChB1B,EAAcO,OAAQ,EACtBxB,IACAtB,EAAKC,KAAKU,UAAUyB,GAIhB8B,EAAezC,kBAAI,GAEd0C,EAAsBA,KAC5B,MAAMC,EAAWC,SAASC,eAAe,WACnCC,EAAIF,SAASG,cAAc,KACjCD,EAAEE,KAAOL,EAASM,UAAU,aAC5BH,EAAEI,SAAWhF,EAAOI,KACpBwE,EAAEK,QACFV,EAAcpB,OAAM,EACpBc,OAAUC,QAAQ,aAIjBgB,EAAgBhE,IACpBqD,EAAcpB,OAAM,EACpBnD,EAAOI,KAAKc,EAAIX,GAChB,IAAI4E,EAAM,IAAIC,KACVC,EAAKF,EAAKG,UACVC,EAASC,SAASH,EAAM,KACxBrC,EAAOvD,EAAQ,yBAAyByB,EAAIX,GAAG,SAASgF,EAC5DvF,EAAOE,OAAO8C,GAIVyC,GADc3D,kBAAI,GACJZ,IAClB0B,EAAcO,OAAQ,EACtB9C,EAAKC,KAAKC,GAAKW,EAAIX,GACnBF,EAAKC,KAAKE,SAAWU,EAAIV,SACzBH,EAAKC,KAAKG,KAAOS,EAAIT,KACrBJ,EAAKC,KAAKI,SAAWQ,EAAIR,SACzBL,EAAKC,KAAKK,UAAYO,EAAIP,UAC1BN,EAAKC,KAAKM,SAAWM,EAAIN,SACzBP,EAAKC,KAAKO,SAAWK,EAAIL,SACzBR,EAAKC,KAAKQ,MAAMI,EAAIJ,MACpBT,EAAKC,KAAKS,OAAOG,EAAIH,OACrBV,EAAKC,KAAKU,UAAUE,EAAIF,YAEpB0E,EAAe5D,iBAAI,IACnB6D,EAAW7D,iBAAI,IACf8D,EAAe9D,iBAAI,IACnB+D,EAAgB/D,iBAAI,IAG1BgB,OAAQC,IAAI,+BAA+BE,KAAMC,IAC/CwC,EAAavC,MAAQD,EAAI5C,OAE3B,MAAMwF,EAAiBA,KACrBhD,OACKC,IAAI,0BACL,CACEC,OAAQ,CACNxC,SAASH,EAAKC,KAAKE,YAGtByC,KAAMC,IACLyC,EAASxC,MAAQD,EAAI5C,KACrBD,EAAKC,KAAKG,KAAK,GACfJ,EAAKC,KAAKI,SAAS,GACnBL,EAAKC,KAAKK,UAAU,MAItBoF,EAAaA,KACjB3E,QAAQC,IAAIhB,EAAKC,KAAKE,UACtBsC,OACKC,IAAI,8BACL,CACEC,OAAQ,CACNxC,SAASH,EAAKC,KAAKE,SACnBC,KAAKJ,EAAKC,KAAKG,QAGlBwC,KAAMC,IACL0C,EAAazC,MAAQD,EAAI5C,KACzBD,EAAKC,KAAKI,SAAS,GACnBL,EAAKC,KAAKK,UAAU,MAItBqF,EAAiBA,KACrBlD,OACKC,IAAI,+BACL,CACEC,OAAQ,CACNxC,SAASH,EAAKC,KAAKE,SACnBC,KAAKJ,EAAKC,KAAKG,KACfC,SAASL,EAAKC,KAAKI,YAGtBuC,KAAMC,IACL2C,EAAc1C,MAAQD,EAAI5C,KAC1BD,EAAKC,KAAKK,UAAU,MAItBsF,EAAkBA,KACtBnD,OACKC,IAAI,8BACL,CACEC,OAAQ,CACNxC,SAASH,EAAKC,KAAKE,SACnBC,KAAKJ,EAAKC,KAAKG,KACfC,SAASL,EAAKC,KAAKI,SACnBC,UAAUN,EAAKC,KAAKK,aAGvBsC,KAAMC,QAMPgD,EAAUpE,iBAAI,MACdqE,GAAOA,KAEXD,EAAQ/C,MAAMiD,SAAUC,IACtB,IAAIA,EAqBF,OAAO,EApBP,IAAIC,EAA0B,KAAjBjG,EAAKC,KAAKC,GAAY,OAAS,MAE5CuC,eAAQ,CACNyD,IAAK,kBACLD,OAAQA,EACRhG,KAAMD,EAAKC,OACV2C,KAAMC,IAEP7C,EAAKC,KAAO,GACK,OAAb4C,EAAIsD,MACN3D,IACAoB,OAAUC,QAAQ,SAElBtB,EAAcO,OAAQ,IAEtBP,EAAcO,OAAQ,EACtBc,OAAUG,MAAMlB,EAAIuD,W,4uRCpiB9B,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB,kCCRf,W", "file": "js/chunk-57487888.79c35010.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"img/Patroller.c9116578.svg\";", "<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/Patroller.svg\"></i> 车场巡逻员管理          \r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n            :inline=\"true\"\r\n            :model=\"query\"\r\n            class=\"demo-form-inline\"\r\n            label-width=\"60px\"\r\n        >\r\n          <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n            <el-input\r\n                v-model=\"query.community\"\r\n                placeholder=\"小区名称\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"80px\" label=\"业主姓名\">\r\n            <el-input\r\n                v-model=\"query.ownername\"\r\n                placeholder=\"业主姓名\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <el-button\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              @click=\"handleAuth\"\r\n          >权限\r\n          </el-button>           -->\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n          >导出\r\n          </el-button> -->\r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n          :data=\"tableData\"\r\n          border\r\n          class=\"table\"\r\n          ref=\"multipleTable\"\r\n          header-cell-class-name=\"table-header\"\r\n          :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n            :show-overflow-tooltip=\"true\"\r\n            :prop=\"item.prop\"\r\n            :label=\"item.label\"\r\n            v-for=\"item in props\"\r\n            :key=\"item.prop\"\r\n            align=\"center\"\r\n        >\r\n        </el-table-column>\r\n\r\n\r\n        <el-table-column label=\"操作\" width=\"210\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleEdit(scope.row)\"\r\n                    >编辑\r\n                    </el-button>\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        class=\"red\"\r\n                        @click=\"handleDelete(scope.$index, scope.row.id)\"\r\n                    >删除\r\n                    </el-button>\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-KJ_016\"\r\n                        class=\"red\"\r\n                        @click=\"createQrCode(scope.row)\"\r\n                    >二维码\r\n                    </el-button>\r\n              \r\n            </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            :currentPage=\"query.pageNum\"\r\n            :page-sizes=\"[10, 20, 40]\"\r\n            :page-size=\"query.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"pageTotal\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <div>\r\n  \r\n  <el-dialog\r\n    v-model=\"qrCodeVisible\"\r\n    title=\"二维码\"\r\n    width=\"15%\"\r\n    :before-close=\"handleClose\"\r\n  >\r\n    <qrcode-vue \r\n    id=\"picture\" \r\n    render-as=\"canvas\"\r\n    margin=\"5\" \r\n    level=\"H\" \r\n    background=\"#ffffff\" \r\n    :value=\"qrData.payUrl\" \r\n    size:qrData.size  ></qrcode-vue>\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"qrCodeVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownloadLocal\"\r\n          >保存</el-button\r\n        >\r\n      </span>\r\n    </template>\r\n  </el-dialog>        \r\n</div>    \r\n    <div>\r\n      <el-dialog title=\"增加车场巡逻员信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n        <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n          <el-form-item label=\"省份\">\r\n            <el-select v-model=\"form.data.province\" placeholder=\"请选择省份\">\r\n              <el-option\r\n                  v-for=\"item in provinceList\"\r\n                  :key=\"item.province\"\r\n                  :label=\"item.province\"\r\n                  :value=\"item.province\"\r\n                  @click=\"changeProvince\"   \r\n                            \r\n                >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地市\">\r\n            <el-select v-model=\"form.data.city\" placeholder=\"请选择地市\">\r\n              <el-option\r\n                  v-for=\"item in cityList\"\r\n                  :key=\"item.city\"\r\n                  :label=\"item.city\"\r\n                  :value=\"item.city\"\r\n                  @click=\"changeCity\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"区县\">\r\n            <el-select v-model=\"form.data.district\" placeholder=\"请选择区县\">\r\n              <el-option\r\n                  v-for=\"item in districtList\"\r\n                  :key=\"item.district\"\r\n                  :label=\"item.district\"\r\n                  :value=\"item.district\"\r\n                  @click=\"changeDistrict\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>    \r\n          <el-form-item label=\"小区\">\r\n            <el-select v-model=\"form.data.community\" placeholder=\"请选择小区\">\r\n              <el-option\r\n                  v-for=\"item in communityList\"\r\n                  :key=\"item.community\"\r\n                  :label=\"item.community\"\r\n                  :value=\"item.community\"\r\n                  @click=\"changeCommunity\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>   \r\n          <el-form-item label=\"巡逻员代码\">\r\n            <el-input v-model=\"form.data.usercode\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"巡逻员名称\">\r\n            <el-input v-model=\"form.data.username\"  style=\"width: 80%\"></el-input>\r\n          </el-form-item>     \r\n          <el-form-item label=\"巡逻员电话\">\r\n            <el-input v-model=\"form.data.phone\"  style=\"width: 80%\"></el-input>\r\n          </el-form-item>                                                                         \r\n        </el-form>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"查看审核原因\" v-model=\"viewShow\">\r\n        <span style=\"margin-left: 50px\">{{ content }}</span>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"viewShow = false\">取 消</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"查看图片\" v-model=\"viewShow\">\r\n        <span style=\"margin-left: 50px\">{{ content1 }}</span>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"viewShow = false\">取 消</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport {useRoute, useRouter} from \"vue-router\";\r\nimport {reactive, ref} from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport {ElMessage, ElMessageBox} from \"element-plus\";\r\nimport '@/assets/font_3904303_u891dmfllhm/iconfont.css'\r\nimport {useStore} from \"vuex\";\r\nimport QrcodeVue from 'qrcode.vue'\r\nconst root = \"/parking/patrol/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  {label: \"省份\", prop: \"province\"},\r\n  {label: \"地市\", prop: \"city\"},\r\n  {label: \"县区\", prop: \"district\"},\r\n  {label: \"小区\", prop: \"community\"},\r\n  {label: \"巡逻员代码\", prop: \"usercode\"},\r\n  {label: \"巡逻员姓名\", prop: \"username\"},\r\n  {label: \"巡逻员电话\", prop: \"phone\"},\r\n  {label: \"状态\", prop: \"status\"},\r\n  \r\n];\r\nconst qrData = reactive({\r\n  payUrl: '这只是一个测试!',\r\n  size: 300,\r\n  name:\"\",\r\n});\r\nconst form = reactive({\r\n  data: {\r\n    id: '',\r\n    province: '',\r\n    city: '',\r\n    district: '',\r\n    community: '',\r\n    usercode: '',\r\n    username: '',\r\n    phone: '',  \r\n    status: '',  \r\n    createman: '', \r\n     \r\n  },\r\n\r\n});\r\n//指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\nconst handleExport = () => {\r\n  window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n  form.data.id = ''\r\n  form.data.province = ''\r\n  form.data.city = ''\r\n  form.data.district = ''\r\n  form.data.community = ''\r\n  form.data.usercode = ''\r\n  form.data.username = ''\r\n  form.data.phone=''\r\n  form.data.status=''\r\n  form.data.createman= ''\r\n};\r\nconst queryTree = reactive({\r\n  province:'',\r\n  city:'',\r\n  district:'',\r\n  community:'',\r\n\r\n});\r\nconst saveTreeInfo = reactive({\r\n  province:'',\r\n  city:'',\r\n  district:'',\r\n  community:'',\r\n  usercode:'',\r\n  username :'',\r\n  arrayId:[],\r\n});\r\nconst viewShow = ref(false)\r\nconst tree = ref();\r\nconst content = ref(\"\");\r\nconst handleView = (row) => {\r\n  console.log(\"这批我\")\r\n  if (row.fileReason !== null) {\r\n    viewShow.value = true\r\n    content.value = row.fileReason\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\nconst viewShow1 = ref(false)\r\nconst content1 = ref(\"\");\r\nconst handleView1 = (row) => {\r\n  console.log(\"这批我\")\r\n  if (row.purchaseVoucher !== null) {\r\n    viewShow.value = true\r\n    content1.value = row.purchaseVoucher\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\nconst query = reactive({\r\n  departmentId: \"\",\r\n  deviceName: \"\",\r\n  applicationTime: \"\",\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\n\r\nconst tableData = ref([]);\r\nconst treeData = ref(\r\n  []);\r\nconst pageTotal = ref(0);\r\nconst loginName = localStorage.getItem(\"loginname\");\r\nconst dialogVisible = ref(false)\r\nconst treeVisible = ref(false)\r\n\r\n// 获取表格数据\r\n\r\nconst getData = () => {\r\n  request\r\n      .get(root + \"querypage\", {\r\n        params: query,\r\n      })\r\n      .then((res) => {\r\n        tableData.value = res.data.records;\r\n        pageTotal.value = res.data.total;\r\n      });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n  // 二次确认删除\r\n  ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n    type: \"warning\",\r\n  })\r\n      .then(() => {\r\n        request.delete(root + sid).then((res) => {\r\n          if (res.data) {\r\n            ElMessage.success(\"删除成功\");\r\n            tableData.value.splice(index, 1);\r\n          } else {\r\n            ElMessage.error(\"删除失败\");\r\n          }\r\n        });\r\n      })\r\n      .catch(() => {\r\n      });\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n  dialogVisible.value = true;\r\n  onReset();\r\n  form.data.createman=loginName;\r\n};\r\n\r\n// 表格编辑时弹窗和保存\r\nconst qrCodeVisible= ref(false)\r\n     // 生成二维码\r\n     const handleDownloadLocal = () => {\r\n     const myCanvas = document.getElementById('picture')\r\n     const a = document.createElement('a')\r\n     a.href = myCanvas.toDataURL('image/png')\r\n     a.download = qrData.name\r\n     a.click()\r\n     qrCodeVisible.value=false\r\n     ElMessage.success('正在下载 请稍后')\r\n \r\n }\r\nconst baseURL= 'https://contest.picp.vip/verify/share/';\r\nconst createQrCode = (row) => {\r\n  qrCodeVisible.value=true;\r\n  qrData.name=row.id;\r\n  let date =new Date();\r\n  let time=date.getTime(); \r\n  let timeunix=parseInt(time/ 1000);   \r\n  let params=baseURL+'&applyKind=4&patrolId='+row.id+'&time='+timeunix;\r\n  qrData.payUrl=params;\r\n}\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n  dialogVisible.value = true\r\n  form.data.id = row.id\r\n  form.data.province = row.province\r\n  form.data.city = row.city\r\n  form.data.district = row.district\r\n  form.data.community = row.community\r\n  form.data.usercode = row.usercode\r\n  form.data.username = row.username\r\n  form.data.phone=row.phone\r\n  form.data.status=row.status\r\n  form.data.createman=row.createman;  \r\n};\r\nconst provinceList = ref([]);\r\nconst cityList = ref([]);\r\nconst districtList = ref([]);\r\nconst communityList = ref([]);\r\n\r\n\r\nrequest.get(\"/parking/community/province\").then((res) => {\r\n  provinceList.value = res.data;\r\n});\r\nconst changeProvince = () => {\r\n  request\r\n      .get(\"/parking/community/city\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        cityList.value = res.data;\r\n        form.data.city=\"\";\r\n        form.data.district=\"\";\r\n        form.data.community=\"\";                   \r\n      });\r\n\r\n};\r\nconst changeCity = () => {\r\n  console.log(form.data.province);\r\n  request\r\n      .get(\"/parking/community/district\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n          city:form.data.city,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        districtList.value = res.data;\r\n        form.data.district=\"\";\r\n        form.data.community=\"\";\r\n      });\r\n\r\n};\r\nconst changeDistrict = () => {\r\n  request\r\n      .get(\"/parking/community/community\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n          city:form.data.city,\r\n          district:form.data.district,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        communityList.value = res.data;\r\n        form.data.community=\"\";\r\n      });\r\n\r\n};\r\nconst changeCommunity = () => {\r\n  request\r\n      .get(\"/parking/community/building\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n          city:form.data.city,\r\n          district:form.data.district,\r\n          community:form.data.community,\r\n        },\r\n      })\r\n      .then((res) => {\r\n\r\n      });\r\n\r\n};\r\n\r\nconst formRef = ref(null);\r\nconst save = () => {\r\n  // 表单校验\r\n  formRef.value.validate((valid) => {\r\n    if (valid) {\r\n      var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n\r\n      request({\r\n        url: \"/parking/patrol\",\r\n        method: method,\r\n        data: form.data,\r\n      }).then((res) => {\r\n\r\n        form.data = {}\r\n        if (res.code === null) {\r\n          getData()\r\n          ElMessage.success(\"提交成功！\");\r\n          // 关闭当前页面的标签页;\r\n          dialogVisible.value = false\r\n        } else {\r\n          dialogVisible.value = false\r\n          ElMessage.error(res.msg);\r\n        }\r\n      });\r\n    } else {\r\n      return false;\r\n    }\r\n  });\r\n};\r\n\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n\tbackground-color: rgb(241, 242, 244) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>", "import script from \"./Patrol.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./Patrol.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./Patrol.vue?vue&type=style&index=0&id=189ee31f&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-189ee31f\"]])\n\nexport default __exports__", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Patrol.vue?vue&type=style&index=0&id=189ee31f&lang=scss&scoped=true\""], "sourceRoot": ""}