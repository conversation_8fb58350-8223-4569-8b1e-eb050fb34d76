{"version": 3, "sources": ["webpack:///./src/views/admin/Book.vue?c4b0", "webpack:///./src/views/admin/Book.vue", "webpack:///./src/views/admin/Book.vue?9dc7"], "names": ["root", "ref", "useRouter", "useRoute", "useStore", "props", "label", "prop", "form", "reactive", "data", "id", "name", "time", "date", "img", "file", "price", "pages", "ticketsData", "ticketList", "ticketName", "ticketCode", "treeData", "arrayId", "onReset", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "getData", "request", "get", "params", "then", "res", "value", "records", "total", "console", "log", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleAdd", "handleEdit", "row", "formRef", "save", "validate", "valid", "method", "url", "code", "msg", "__exports__"], "mappings": "2IAAA,W,oZCmIQA,EAAO,iB,8BAFOC,kBAAI,GACHA,mBAENC,iBACDC,iBACAC,iBALd,MAMMC,EAAQ,CACZ,CAACC,MAAO,OAAQC,KAAM,QACtB,CAACD,MAAO,OAAQC,KAAM,QACtB,CAACD,MAAO,OAAQC,KAAM,QACtB,CAACD,MAAO,KAAMC,KAAM,OACpB,CAACD,MAAO,KAAMC,KAAM,QACpB,CAACD,MAAO,KAAMC,KAAM,SACpB,CAACD,MAAO,KAAMC,KAAM,UAGhBC,EAAOC,sBAAS,CACpBC,KAAM,CACJC,GAAI,GACJC,KAAM,GACNC,KAAM,GACNC,KAAM,GACNC,IAAK,GACLC,KAAM,GACNC,MAAO,GACPC,MAAO,IAETC,YAAY,CACVR,GAAI,GACJK,KAAM,GACNC,MAAO,GACPG,WAAW,GACXC,WAAW,GACXC,WAAW,GACXV,KAAM,GACNC,KAAM,GACNC,KAAM,GACNC,IAAK,GACLQ,SAAS,GACTC,QAAQ,MAONC,EAAUA,KACdjB,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKE,KAAO,GACjBJ,EAAKE,KAAKG,KAAO,GACjBL,EAAKE,KAAKI,KAAO,GACjBN,EAAKE,KAAKK,IAAM,GAChBP,EAAKE,KAAKM,KAAO,GACjBR,EAAKE,KAAKO,MAAQ,GAClBT,EAAKE,KAAKQ,MAAQ,GAElBV,EAAKW,YAAYR,GAAI,GACrBH,EAAKW,YAAYP,KAAO,GACxBJ,EAAKW,YAAYN,KAAM,GACvBL,EAAKW,YAAYL,KAAO,GACxBN,EAAKW,YAAYJ,IAAM,GACvBP,EAAKW,YAAYH,KAAO,GACxBR,EAAKW,YAAYF,MAAQ,GACzBT,EAAKW,YAAYD,MAAQ,GACzBV,EAAKW,YAAYG,WAAa,GAC9Bd,EAAKW,YAAYE,WAAa,GAC9Bb,EAAKW,YAAYI,SAAS,GAClBf,EAAKW,YAAYK,QAAQ,IAa7BE,GAXWzB,kBAAI,GACLA,iBAAI,IAUNQ,sBAAS,CACrBG,KAAM,GACNe,QAAS,EACTC,SAAU,MAENC,EAAY5B,iBAAI,IAChB6B,EAAY7B,iBAAI,GAChB8B,EAAgB9B,kBAAI,GAIpB+B,EAAUA,KACdC,OACSC,IAAIlC,EAAO,OAAQ,CAClBmC,OAAQT,IAETU,KAAMC,IACLR,EAAUS,MAAQD,EAAI3B,KAAK6B,QAC3BT,EAAUQ,MAAQD,EAAI3B,KAAK8B,MAC3BC,QAAQC,IAAIL,EAAI3B,SAI5BsB,IAEA,MAAMW,EAAeA,KACnBjB,EAAMC,QAAU,EAChBK,KAGIY,EAAoBC,IACxBnB,EAAME,SAAWiB,EACjBb,KAGIc,EAAoBD,IACxBnB,EAAMC,QAAUkB,EAChBb,KAGIe,EAAeA,CAACC,EAAOC,KAE3BC,OAAaC,QAAQ,UAAW,KAAM,CACpCC,KAAM,YAEChB,KAAK,KACJH,OAAQoB,OAAOrD,EAAOiD,GAAKb,KAAMC,IAC3BA,EAAI3B,MACN4C,OAAUC,QAAQ,QAClB1B,EAAUS,MAAMkB,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAIrBC,MAAM,SAKXC,EAAYA,KAChB5B,EAAcO,OAAQ,EACtBb,KAKImC,GADc3D,kBAAI,GACJ4D,IAClB9B,EAAcO,OAAQ,EACtB9B,EAAKE,KAAKC,GAAKkD,EAAIlD,GACnBH,EAAKE,KAAKE,KAAOiD,EAAIjD,KACrBJ,EAAKE,KAAKG,KAAOgD,EAAIhD,KACrBL,EAAKE,KAAKI,KAAO+C,EAAI/C,KACrBN,EAAKE,KAAKK,IAAM8C,EAAI9C,IACpBP,EAAKE,KAAKM,KAAO6C,EAAI7C,KACrBR,EAAKE,KAAKO,MAAQ4C,EAAI5C,MACtBT,EAAKE,KAAKQ,MAAQ2C,EAAI3C,QAqDlB4C,EAAU7D,iBAAI,MAEd8D,GADgB9D,iBAAI,MACb8D,KAEXD,EAAQxB,MAAM0B,SAAUC,IAEtB,GADAxB,QAAQC,IAAIlC,EAAKE,KAAKC,KAClBsD,EAoBF,OAAO,EAnBP,IAAIC,EAA0B,KAAjB1D,EAAKE,KAAKC,GAAY,OAAS,MAC5C8B,QAAQC,IAAIwB,GACZjC,eAAQ,CACNkC,IAAKnE,EACLkE,OAAQA,EACRxD,KAAMF,EAAKE,OACV0B,KAAMC,IACP7B,EAAKE,KAAO,GACK,OAAb2B,EAAI+B,MACNpC,IACAsB,OAAUC,QAAQ,SAElBxB,EAAcO,OAAQ,IAEtBP,EAAcO,OAAQ,EACtBgB,OAAUG,MAAMpB,EAAIgC,Y,0vKClWhC,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD", "file": "js/chunk-f8faaf3a.f8af493f.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Book.vue?vue&type=style&index=0&id=4d2d832e&lang=scss&scoped=true\"", "<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i class=\"el-icon-location\"></i> 书籍管理\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n                :inline=\"true\"\r\n                :model=\"query\"\r\n                class=\"demo-form-inline\"\r\n                label-width=\"60px\"\r\n        >\r\n          <el-form-item label-width=\"80px\" label=\"书籍名称\">\r\n            <el-input\r\n                    v-model=\"query.community\"\r\n                    placeholder=\"书籍名称\"\r\n                    class=\"handle-input mr10\"\r\n                    clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" class=\"searchButton\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <el-button\r\n                  type=\"primary\"\r\n                  class=\"addButton\"\r\n                  @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n              :data=\"tableData\"\r\n              border\r\n              class=\"table\"\r\n              ref=\"multipleTable\"\r\n              header-cell-class-name=\"table-header\"\r\n      >\r\n        <el-table-column\r\n                :show-overflow-tooltip=\"true\"\r\n                :prop=\"item.prop\"\r\n                :label=\"item.label\"\r\n                v-for=\"item in props\"\r\n                :key=\"item.prop\"\r\n                align=\"center\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleEdit(scope.row)\"\r\n            >编辑\r\n            </el-button>\r\n            <el-button\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    class=\"red\"\r\n                    @click=\"handleDelete(scope.$index, scope.row.id)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n                :currentPage=\"query.pageNum\"\r\n                :page-sizes=\"[10, 20, 40]\"\r\n                :page-size=\"query.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"pageTotal\"\r\n                @size-change=\"handleSizeChange\"\r\n                @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"书籍信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n        <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"gateRules\" label-width=\"110px\">\r\n          <el-form-item label=\"书籍名称\" prop=\"name\">\r\n              <el-input v-model=\"form.data.name\" style=\"width: 80%\"></el-input>\r\n            </el-form-item>\r\n          <el-form-item label=\"生产时间\" prop=\"time\">\r\n            <el-input v-model=\"form.data.time\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"出版日期\" prop=\"date\">\r\n            <el-input v-model=\"form.data.date\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"图片\" prop=\"img\">\r\n            <el-input v-model=\"form.data.img\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件\" prop=\"file\">\r\n            <el-input v-model=\"form.data.file\"  style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"书籍价格\" prop=\"price\">\r\n            <el-input v-model=\"form.data.price\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"书籍页数\" prop=\"pages\">\r\n            <el-input v-model=\"form.data.pages\"  style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\n  import {useRoute, useRouter} from \"vue-router\";\r\n  import {reactive, ref} from \"vue\";\r\n  import request from \"@/utils/request\";\r\n\r\n  import {ElMessage, ElMessageBox} from \"element-plus\";\r\n  import {useStore} from \"vuex\";\r\n\r\n  const treeVisible = ref(false)\r\n  const treeBuilding = ref();\r\n  const root = \"/parking/book/\";\r\n  const router = useRouter();\r\n  const route = useRoute();\r\n  const store = useStore();\r\n  const props = [\r\n    {label: \"书籍名称\", prop: \"name\"},\r\n    {label: \"生产时间\", prop: \"time\"},\r\n    {label: \"出版日期\", prop: \"date\"},\r\n    {label: \"图片\", prop: \"img\"},\r\n    {label: \"文件\", prop: \"file\"},\r\n    {label: \"价格\", prop: \"price\"},\r\n    {label: \"页数\", prop: \"pages\"}\r\n  ];\r\n\r\n  const form = reactive({\r\n    data: {\r\n      id: '',\r\n      name: '',\r\n      time: '',\r\n      date: '',\r\n      img: '',\r\n      file: '',\r\n      price: '',\r\n      pages: ''\r\n    },\r\n    ticketsData:{\r\n      id: '',\r\n      file: '',\r\n      price: '',\r\n      ticketList:[],\r\n      ticketName:'',\r\n      ticketCode:'',\r\n      name: '',\r\n      time: '',\r\n      date: '',\r\n      img: '',\r\n      treeData:[],\r\n      arrayId:[],\r\n    },\r\n  });\r\n  const handleExport = () => {\r\n    window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n  };\r\n  // 重置\r\n  const onReset = () => {\r\n    form.data.id = ''\r\n    form.data.name = ''\r\n    form.data.time = ''\r\n    form.data.date = ''\r\n    form.data.img = ''\r\n    form.data.file = ''\r\n    form.data.price = ''\r\n    form.data.pages = ''\r\n\r\n    form.ticketsData.id =''\r\n    form.ticketsData.name = ''\r\n    form.ticketsData.time =''\r\n    form.ticketsData.date = ''\r\n    form.ticketsData.img = ''\r\n    form.ticketsData.file = ''\r\n    form.ticketsData.price = ''\r\n    form.ticketsData.pages = ''\r\n    form.ticketsData.ticketCode = ''\r\n    form.ticketsData.ticketName = ''\r\n    form.ticketsData.treeData=[],\r\n            form.ticketsData.arrayId=[]\r\n  };\r\n  const viewShow = ref(false)\r\n  const content = ref(\"\");\r\n  // const handleView = (row) => {\r\n  //   console.log(\"这批我\")\r\n  //   if (row.fileReason !== null) {\r\n  //     viewShow.value = true\r\n  //     content.value = row.fileReason\r\n  //   } else {\r\n  //     ElMessage.info('没有审核原因');\r\n  //   }\r\n  // };\r\n  const query = reactive({\r\n    name: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10,\r\n  });\r\n  const tableData = ref([]);\r\n  const pageTotal = ref(0);\r\n  const dialogVisible = ref(false)\r\n\r\n\r\n  // 获取表格数据\r\n  const getData = () => {\r\n    request\r\n            .get(root + \"page\", {\r\n              params: query,\r\n            })\r\n            .then((res) => {\r\n              tableData.value = res.data.records;\r\n              pageTotal.value = res.data.total;\r\n              console.log(res.data);\r\n            });\r\n\r\n  };\r\n  getData();\r\n  // 查询操作\r\n  const handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n  };\r\n  // 分页大小\r\n  const handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n  };\r\n  // 分页导航\r\n  const handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n  };\r\n  // 删除操作\r\n  const handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n      type: \"warning\",\r\n    })\r\n            .then(() => {\r\n              request.delete(root + sid).then((res) => {\r\n                if (res.data) {\r\n                  ElMessage.success(\"删除成功\");\r\n                  tableData.value.splice(index, 1);\r\n                } else {\r\n                  ElMessage.error(\"删除失败\");\r\n                }\r\n              });\r\n            })\r\n            .catch(() => {\r\n            });\r\n  };\r\n\r\n  // 新增操作\r\n  const handleAdd = () => {\r\n    dialogVisible.value = true;\r\n    onReset();\r\n\r\n  };\r\n  // 表格编辑时弹窗和保存\r\n  const editVisible = ref(false);\r\n  const handleEdit = (row) => {\r\n    dialogVisible.value = true\r\n    form.data.id = row.id\r\n    form.data.name = row.name\r\n    form.data.time = row.time\r\n    form.data.date = row.date\r\n    form.data.img = row.img\r\n    form.data.file = row.file\r\n    form.data.price = row.price\r\n    form.data.pages = row.pages\r\n  };\r\n  const  bookRules= {\r\n    name: [\r\n      {\r\n        required: true,\r\n        message: \"请填入书籍名称\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n    time: [\r\n      {\r\n        required: true,\r\n        message: \"请填入生产时间\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n    date: [\r\n      {\r\n        required: true,\r\n        message: \"请填入出版日期\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n    img: [\r\n      {\r\n        required: true,\r\n        message: \"请修改图片\",\r\n        trigger: 'change'\r\n      },\r\n    ],\r\n    file: [\r\n      {\r\n        required: true,\r\n        message: \"请修改文件\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n    price: [\r\n      {\r\n        required: true,\r\n        message: \"请填写书籍价格\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n    pages: [\r\n      {\r\n        required: true,\r\n        message: \"请填写书籍页数\",\r\n        trigger: 'blur'\r\n      },\r\n    ],\r\n  };\r\n  const formRef = ref(null);\r\n  const ticketFormRef = ref(null);\r\n  const save = () => {\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n      console.log(form.data.id);\r\n      if (valid) {\r\n        var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n        console.log(method);\r\n        request({\r\n          url: root,\r\n          method: method,\r\n          data: form.data,\r\n        }).then((res) => {\r\n          form.data = {}\r\n          if (res.code === null) {\r\n            getData()\r\n            ElMessage.success(\"提交成功！\");\r\n            // 关闭当前页面的标签页;\r\n            dialogVisible.value = false\r\n          } else {\r\n            dialogVisible.value = false\r\n            ElMessage.error(res.msg);\r\n          }\r\n        });\r\n      } else {\r\n        return false;\r\n      }\r\n    });\r\n  };\r\n  const treeProps =  {\r\n    children: 'children',\r\n    label: 'building'\r\n  };\r\n\r\n  const ticketRules = {\r\n    ticketName: [\r\n      {\r\n        required: true,\r\n        message: \"请选择月票类型\",\r\n\r\n      },\r\n    ],\r\n    arrayId:[ { required: true, message: '请选择', trigger: 'change' },\r\n    ],\r\n  };\r\n  const changeTickets = () => {\r\n    console.log(form.ticketsData.ticketName)\r\n    for(let i=0;i<form.ticketsData.ticketList.length;i++){\r\n      if(form.ticketsData.ticketList[i].ticketName==form.ticketsData.ticketName)\r\n      {\r\n        form.ticketsData.ticketCode=form.ticketsData.ticketList[i].ticketCode\r\n        console.log(form.ticketsData.ticketCode)\r\n        return\r\n\r\n      }\r\n    }\r\n  };\r\n  const handleCheckChange=() =>{\r\n    form.ticketsData.arrayId= treeBuilding.value.getCheckedKeys();\r\n  }\r\n\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\r\n  // :deep(.el-form-item__label) {\r\n  //     color: red;\r\n  //   }\r\n\r\n\r\n</style>\r\n", "import script from \"./Book.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./Book.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./Book.vue?vue&type=style&index=0&id=4d2d832e&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-4d2d832e\"]])\n\nexport default __exports__"], "sourceRoot": ""}