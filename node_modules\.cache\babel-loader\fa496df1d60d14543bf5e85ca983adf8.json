{"remainingRequest": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\MaintenanceAudit.vue?vue&type=script&setup=true&lang=js", "dependencies": [{"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\MaintenanceAudit.vue", "mtime": 1734307799266}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdXNlUm91dGUsIHVzZVJvdXRlciB9IGZyb20gInZ1ZS1yb3V0ZXIiOwppbXBvcnQgeyByZWFjdGl2ZSwgcmVmIH0gZnJvbSAidnVlIjsKaW1wb3J0IHJlcXVlc3QgZnJvbSAiQC91dGlscy9yZXF1ZXN0IjsKaW1wb3J0IHsgRWxNZXNzYWdlLCBFbE1lc3NhZ2VCb3ggfSBmcm9tICJlbGVtZW50LXBsdXMiOwppbXBvcnQgeyB1c2VTdG9yZSB9IGZyb20gInZ1ZXgiOwpjb25zdCByb290ID0gIi9wYXJraW5nL21haW50ZW5hbmNlLyI7CmV4cG9ydCBkZWZhdWx0IHsKICBfX25hbWU6ICdNYWludGVuYW5jZUF1ZGl0JywKICBzZXR1cChfX3Byb3BzLCB7CiAgICBleHBvc2U6IF9fZXhwb3NlCiAgfSkgewogICAgX19leHBvc2UoKTsKICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpOwogICAgY29uc3Qgcm91dGUgPSB1c2VSb3V0ZSgpOwogICAgY29uc3Qgc3RvcmUgPSB1c2VTdG9yZSgpOwogICAgY29uc3QgcHJvcHMgPSBbewogICAgICBsYWJlbDogIuiuvuWkh+W<PERSON>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"}, {"version": 3, "names": ["useRoute", "useRouter", "reactive", "ref", "request", "ElMessage", "ElMessageBox", "useStore", "root", "router", "route", "store", "props", "label", "prop", "form", "data", "maintenanceId", "deviceId", "maintenanceUserId", "repairmanUserId", "faultDescription", "remarks", "editVis", "handleAdd", "dialogVisible", "value", "title", "deCode", "handleEdit", "row", "deviceName", "deviceCode", "entity", "maintOpinions", "auditStatus", "save", "post", "then", "res", "code", "getData", "success", "error", "msg", "handleExport", "window", "location", "href", "viewShow", "content", "handleView", "console", "log", "fileReason", "info", "applicantUserId", "localStorage", "getItem", "departmentList", "get", "query", "departmentId", "applicationTime", "pageNum", "pageSize", "tableData", "pageTotal", "userId", "params", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "confirm", "type", "delete", "splice", "catch", "editVisible", "userList", "deviceList", "getDevice", "formRef"], "sources": ["F:/ParkingDemoAKEHRBU/ParkingManageDemo/manage-front/src/views/admin/MaintenanceAudit.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i class=\"el-icon-lx-cascades\"></i> 报修审批\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n          <el-form-item label-width=\"60px\" label=\"设备名\">\r\n            <el-input v-model=\"query.deviceName\" placeholder=\"设备名\" class=\"handle-input mr10\" maxlength=\"13\"\r\n              clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"80px\" label=\"设备编码\">\r\n            <el-input v-model=\"query.deviceCode\" placeholder=\"设备编码\" class=\"handle-input mr10\" maxlength=\"13\"\r\n              clearable></el-input>\r\n          </el-form-item>\r\n          <!--          <el-form-item label=\"部门\" prop=\"departmentId\">-->\r\n          <!--            <el-select-->\r\n          <!--                v-model=\"query.departmentId\"-->\r\n          <!--                placeholder=\"请选择部门\"-->\r\n          <!--                clearable-->\r\n          <!--            >-->\r\n          <!--              <el-option-->\r\n          <!--                  v-for=\"item in departmentList\"-->\r\n          <!--                  :key=\"item.departmentId\"-->\r\n          <!--                  :label=\"item.departmentName\"-->\r\n          <!--                  :value=\"item.departmentId\"-->\r\n          <!--                  clearable-->\r\n          <!--              >-->\r\n          <!--              </el-option>-->\r\n          <!--            </el-select>-->\r\n          <!--          </el-form-item>-->\r\n          <!--          <el-form-item prop=\"applicationTime\" label=\"申请时间\">-->\r\n          <!--            <el-date-picker-->\r\n          <!--                v-model=\"query.applicationTime\"-->\r\n          <!--                type=\"date\"-->\r\n          <!--                placeholder=\"选择一个日期\"-->\r\n          <!--                format=\"YYYY-MM-DD\"-->\r\n          <!--                value-format=\"YYYY-MM-DD\"-->\r\n          <!--                clearable-->\r\n          <!--            >-->\r\n          <!--            </el-date-picker>-->\r\n          <!--          </el-form-item>-->\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索\r\n          </el-button>\r\n          <!--          <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              @click=\"handleAdd\"-->\r\n          <!--          >新增-->\r\n          <!--          </el-button>-->\r\n          <!--          <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              @click=\"handleExport\"-->\r\n          <!--          >导出-->\r\n          <!--          </el-button-->\r\n        </el-form>\r\n      </div>\r\n      <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\">\r\n        <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\" v-for=\"item in props\"\r\n          :key=\"item.prop\" align=\"center\">\r\n        </el-table-column>\r\n\r\n        <el-table-column label=\"审批状态\" prop=\"auditStatus\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-tag type=\"success\" v-if=\"scope.row.auditStatus === 1\">待维修</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.auditStatus === 3\">维修失败</el-tag>\r\n            <el-tag type=\"warning\" v-else-if=\"scope.row.auditStatus === 2\">已维修</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column>\r\n          <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\"\r\n                :disabled=\"scope.row.auditStatus === 1 ? false : true\">维修情况\r\n              </el-button>\r\n              <!--              <el-button-->\r\n              <!--                  type=\"text\"-->\r\n              <!--                  icon=\"el-icon-delete\"-->\r\n              <!--                  class=\"red\"-->\r\n              <!--                  @click=\"handleDelete(scope.$index, scope.row.maintenanceId)\"-->\r\n              <!--                  :disabled=\"scope.row.auditStatus === 1 ? false:true\"-->\r\n              <!--              >删除-->\r\n              <!--              </el-button>-->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n          @current-change=\"handlePageChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n\r\n    <div>\r\n      <div>\r\n        <el-dialog title=\"申请\" v-model=\"editVis\" width=\"50%\">\r\n          <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n            <el-form-item label=\"设备名\">\r\n              <el-input v-model=\"title\" style=\"width: 80%\" disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"设备编码\">\r\n              <el-input v-model=\"deCode\" style=\"width: 80%\" disabled></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"维修情况\">\r\n              <el-radio-group v-model=\"entity.data.auditStatus\">\r\n                <el-radio :label=2>已修好</el-radio>\r\n                <el-radio :label=3>申请报废</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"维修意见\">\r\n              <el-input type=\"textarea\" :rows=\"2\" v-model=\"entity.data.maintOpinions\" style=\"width: 80%\"></el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n          <template #footer>\r\n            <span class=\"dialog-footer\">\r\n              <el-button @click=\"editVis = false\">取 消</el-button>\r\n              <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n            </span>\r\n          </template>\r\n        </el-dialog>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\nconst root = \"/parking/maintenance/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  { label: \"设备名称\", prop: \"deviceName\" },\r\n  { label: \"故障描述\", prop: \"faultDescription\" },\r\n  { label: \"部门地址\", prop: \"departmentAddress\" },\r\n  { label: \"备注\", prop: \"remarks\" },\r\n  { label: \"设备编码\", prop: \"deviceCode\" },\r\n  { label: \"申请人\", prop: \"repairmanUserName\" },\r\n  { label: \"维修人\", prop: \"maintenanceUserName\" },\r\n];\r\nconst form = reactive({\r\n  data: {\r\n    maintenanceId: '',\r\n    deviceId: \"\",\r\n    maintenanceUserId: \"\",//维修人\r\n    repairmanUserId: \"\",\r\n    faultDescription: \"\",\r\n    remarks: \"\",\r\n  },\r\n});\r\nconst editVis = ref(false);\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n  dialogVisible.value = true\r\n  // form.data.applicantUserId = localStorage.getItem(\"userId\")\r\n  // form.data.departmentId = localStorage.getItem(\"departmentId\")\r\n};\r\nconst title = ref(\"\")\r\nconst deCode = ref(\"\")\r\nconst handleEdit = (row) => {\r\n  editVis.value = true\r\n  title.value = row.deviceName\r\n  deCode.value = row.deviceCode\r\n  entity.data.maintenanceId = row.maintenanceId\r\n  entity.data.deviceId = row.deviceId\r\n};\r\nconst entity = reactive({\r\n  data: {\r\n    maintenanceId: '',\r\n    deviceId: '',\r\n    maintOpinions: '',\r\n    auditStatus: '',\r\n    maintenanceUserId: '',\r\n  },\r\n});\r\nconst save = () => {\r\n  request.post(\"/parking/maintenance/updateManage\", entity.data).then((res) => {\r\n    if (res.code === null) {\r\n      getData()\r\n      ElMessage.success(\"提交成功！\");\r\n      editVis.value = false\r\n    } else {\r\n      dialogVisible.value = false\r\n      editVis.value = false\r\n      ElMessage.error(res.msg);\r\n    }\r\n    entity.data = {}\r\n  }\r\n  )\r\n};\r\nconst handleExport = () => {\r\n  window.location.href = \"http://localhost:9999/maintenance/exportMaintenance\";\r\n};\r\n\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\nconst handleView = (row) => {\r\n  console.log(\"这批我\")\r\n  if (row.fileReason !== null) {\r\n    viewShow.value = true\r\n    content.value = row.fileReason\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n// alert(applicantUserId.value)\r\nconst departmentList = ref([]);\r\nrequest.get(\"/parking/department/listDepartment\").then((res) => {\r\n  departmentList.value = res.data;\r\n});\r\nconst query = reactive({\r\n  departmentId: \"\",\r\n  deviceName: \"\",\r\n  deviceCode: \"\",\r\n  applicationTime: \"\",\r\n  maintenanceUserId: localStorage.getItem(\"userId\"),\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\n// 获取表格数据\r\n\r\nconst getData = () => {\r\n  query.maintenanceUserId = localStorage.getItem(\"userId\")\r\n  request\r\n    .get(root + \"pageBymaintenanceUserId\", {\r\n      params: query,\r\n    })\r\n    .then((res) => {\r\n      tableData.value = res.data.records;\r\n      pageTotal.value = res.data.total;\r\n    });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n  // 二次确认删除\r\n  ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n    type: \"warning\",\r\n  })\r\n    .then(() => {\r\n      request.delete(root + sid).then((res) => {\r\n        if (res.data) {\r\n          ElMessage.success(\"删除成功\");\r\n          tableData.value.splice(index, 1);\r\n        } else {\r\n          ElMessage.error(\"删除失败\");\r\n        }\r\n      });\r\n    })\r\n    .catch(() => {\r\n    });\r\n};\r\n\r\n\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\n\r\nconst userList = ref([]);\r\nrequest.get(\"/parking/user/listAll\").then((res) => {\r\n  userList.value = res.data;\r\n});\r\nconst deviceList = ref([]);\r\nconst getDevice = () => {\r\n  request.get(\"/parking/device/listByType\").then((res) => {\r\n    deviceList.value = res.data\r\n  });\r\n}\r\ngetDevice();\r\nconst formRef = ref(null);\r\n\r\n</script>\r\n\r\n<style scoped></style>"], "mappings": "AAqIA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SAASC,QAAQ,EAAEC,GAAG,QAAQ,KAAK;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,QAAQ,QAAQ,MAAM;AAE/B,MAAMC,IAAI,GAAG,uBAAuB;;;;;;;IACpC,MAAMC,MAAM,GAAGR,SAAS,CAAC,CAAC;IAC1B,MAAMS,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,MAAMW,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,MAAMK,KAAK,GAAG,CACZ;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAa,CAAC,EACrC;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAmB,CAAC,EAC3C;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAoB,CAAC,EAC5C;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAU,CAAC,EAChC;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAa,CAAC,EACrC;MAAED,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAoB,CAAC,EAC3C;MAAED,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAsB,CAAC,CAC9C;IACD,MAAMC,IAAI,GAAGb,QAAQ,CAAC;MACpBc,IAAI,EAAE;QACJC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QAAC;QACtBC,eAAe,EAAE,EAAE;QACnBC,gBAAgB,EAAE,EAAE;QACpBC,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,MAAMC,OAAO,GAAGpB,GAAG,CAAC,KAAK,CAAC;;IAE1B;IACA,MAAMqB,SAAS,GAAGA,CAAA,KAAM;MACtBC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC1B;MACA;IACF,CAAC;IACD,MAAMC,KAAK,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMyB,MAAM,GAAGzB,GAAG,CAAC,EAAE,CAAC;IACtB,MAAM0B,UAAU,GAAIC,GAAG,IAAK;MAC1BP,OAAO,CAACG,KAAK,GAAG,IAAI;MACpBC,KAAK,CAACD,KAAK,GAAGI,GAAG,CAACC,UAAU;MAC5BH,MAAM,CAACF,KAAK,GAAGI,GAAG,CAACE,UAAU;MAC7BC,MAAM,CAACjB,IAAI,CAACC,aAAa,GAAGa,GAAG,CAACb,aAAa;MAC7CgB,MAAM,CAACjB,IAAI,CAACE,QAAQ,GAAGY,GAAG,CAACZ,QAAQ;IACrC,CAAC;IACD,MAAMe,MAAM,GAAG/B,QAAQ,CAAC;MACtBc,IAAI,EAAE;QACJC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZgB,aAAa,EAAE,EAAE;QACjBC,WAAW,EAAE,EAAE;QACfhB,iBAAiB,EAAE;MACrB;IACF,CAAC,CAAC;IACF,MAAMiB,IAAI,GAAGA,CAAA,KAAM;MACjBhC,OAAO,CAACiC,IAAI,CAAC,mCAAmC,EAAEJ,MAAM,CAACjB,IAAI,CAAC,CAACsB,IAAI,CAAEC,GAAG,IAAK;QAC3E,IAAIA,GAAG,CAACC,IAAI,KAAK,IAAI,EAAE;UACrBC,OAAO,CAAC,CAAC;UACTpC,SAAS,CAACqC,OAAO,CAAC,OAAO,CAAC;UAC1BnB,OAAO,CAACG,KAAK,GAAG,KAAK;QACvB,CAAC,MAAM;UACLD,aAAa,CAACC,KAAK,GAAG,KAAK;UAC3BH,OAAO,CAACG,KAAK,GAAG,KAAK;UACrBrB,SAAS,CAACsC,KAAK,CAACJ,GAAG,CAACK,GAAG,CAAC;QAC1B;QACAX,MAAM,CAACjB,IAAI,GAAG,CAAC,CAAC;MAClB,CACA,CAAC;IACH,CAAC;IACD,MAAM6B,YAAY,GAAGA,CAAA,KAAM;MACzBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,qDAAqD;IAC9E,CAAC;IAED,MAAMC,QAAQ,GAAG9C,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAM+C,OAAO,GAAG/C,GAAG,CAAC,EAAE,CAAC;IACvB,MAAMgD,UAAU,GAAIrB,GAAG,IAAK;MAC1BsB,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;MAClB,IAAIvB,GAAG,CAACwB,UAAU,KAAK,IAAI,EAAE;QAC3BL,QAAQ,CAACvB,KAAK,GAAG,IAAI;QACrBwB,OAAO,CAACxB,KAAK,GAAGI,GAAG,CAACwB,UAAU;MAChC,CAAC,MAAM;QACLjD,SAAS,CAACkD,IAAI,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC;IACD,MAAMC,eAAe,GAAGrD,GAAG,CAAC,EAAE,CAAC;IAC/BqD,eAAe,CAAC9B,KAAK,GAAG+B,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACtD;IACA,MAAMC,cAAc,GAAGxD,GAAG,CAAC,EAAE,CAAC;IAC9BC,OAAO,CAACwD,GAAG,CAAC,oCAAoC,CAAC,CAACtB,IAAI,CAAEC,GAAG,IAAK;MAC9DoB,cAAc,CAACjC,KAAK,GAAGa,GAAG,CAACvB,IAAI;IACjC,CAAC,CAAC;IACF,MAAM6C,KAAK,GAAG3D,QAAQ,CAAC;MACrB4D,YAAY,EAAE,EAAE;MAChB/B,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACd+B,eAAe,EAAE,EAAE;MACnB5C,iBAAiB,EAAEsC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MACjDM,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,SAAS,GAAG/D,GAAG,CAAC,EAAE,CAAC;IACzB,MAAMgE,SAAS,GAAGhE,GAAG,CAAC,CAAC,CAAC;IACxB,MAAMiE,MAAM,GAAGX,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,MAAMjC,aAAa,GAAGtB,GAAG,CAAC,KAAK,CAAC;IAChC;;IAEA,MAAMsC,OAAO,GAAGA,CAAA,KAAM;MACpBoB,KAAK,CAAC1C,iBAAiB,GAAGsC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MACxDtD,OAAO,CACJwD,GAAG,CAACpD,IAAI,GAAG,yBAAyB,EAAE;QACrC6D,MAAM,EAAER;MACV,CAAC,CAAC,CACDvB,IAAI,CAAEC,GAAG,IAAK;QACb2B,SAAS,CAACxC,KAAK,GAAGa,GAAG,CAACvB,IAAI,CAACsD,OAAO;QAClCH,SAAS,CAACzC,KAAK,GAAGa,GAAG,CAACvB,IAAI,CAACuD,KAAK;MAClC,CAAC,CAAC;IACN,CAAC;IACD9B,OAAO,CAAC,CAAC;IACT;IACA,MAAM+B,YAAY,GAAGA,CAAA,KAAM;MACzBX,KAAK,CAACG,OAAO,GAAG,CAAC;MACjBvB,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMgC,gBAAgB,GAAIC,GAAG,IAAK;MAChCb,KAAK,CAACI,QAAQ,GAAGS,GAAG;MACpBjC,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMkC,gBAAgB,GAAID,GAAG,IAAK;MAChCb,KAAK,CAACG,OAAO,GAAGU,GAAG;MACnBjC,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMmC,YAAY,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;MACnC;MACAxE,YAAY,CAACyE,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE;QACpCC,IAAI,EAAE;MACR,CAAC,CAAC,CACC1C,IAAI,CAAC,MAAM;QACVlC,OAAO,CAAC6E,MAAM,CAACzE,IAAI,GAAGsE,GAAG,CAAC,CAACxC,IAAI,CAAEC,GAAG,IAAK;UACvC,IAAIA,GAAG,CAACvB,IAAI,EAAE;YACZX,SAAS,CAACqC,OAAO,CAAC,MAAM,CAAC;YACzBwB,SAAS,CAACxC,KAAK,CAACwD,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;UAClC,CAAC,MAAM;YACLxE,SAAS,CAACsC,KAAK,CAAC,MAAM,CAAC;UACzB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,CACDwC,KAAK,CAAC,MAAM,CACb,CAAC,CAAC;IACN,CAAC;;IAGD;IACA,MAAMC,WAAW,GAAGjF,GAAG,CAAC,KAAK,CAAC;IAE9B,MAAMkF,QAAQ,GAAGlF,GAAG,CAAC,EAAE,CAAC;IACxBC,OAAO,CAACwD,GAAG,CAAC,uBAAuB,CAAC,CAACtB,IAAI,CAAEC,GAAG,IAAK;MACjD8C,QAAQ,CAAC3D,KAAK,GAAGa,GAAG,CAACvB,IAAI;IAC3B,CAAC,CAAC;IACF,MAAMsE,UAAU,GAAGnF,GAAG,CAAC,EAAE,CAAC;IAC1B,MAAMoF,SAAS,GAAGA,CAAA,KAAM;MACtBnF,OAAO,CAACwD,GAAG,CAAC,4BAA4B,CAAC,CAACtB,IAAI,CAAEC,GAAG,IAAK;QACtD+C,UAAU,CAAC5D,KAAK,GAAGa,GAAG,CAACvB,IAAI;MAC7B,CAAC,CAAC;IACJ,CAAC;IACDuE,SAAS,CAAC,CAAC;IACX,MAAMC,OAAO,GAAGrF,GAAG,CAAC,IAAI,CAAC"}]}