{"version": 3, "sources": ["webpack:///./src/views/admin/ReleaseReason.vue", "webpack:///./src/views/admin/ReleaseReason.vue?367b", "webpack:///./src/views/admin/ReleaseReason.vue?a37e", "webpack:///./src/icons/svg-black/ReleaseReason.svg"], "names": ["root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "rules", "releaseReason", "required", "message", "trigger", "releaseNo", "form", "reactive", "data", "id", "onReset", "applicantUserId", "ref", "value", "localStorage", "getItem", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "tableRowClassName", "row", "rowIndex", "console", "log", "cellStyle", "column", "columnIndex", "style", "padding", "getData", "request", "get", "params", "then", "res", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleAdd", "handleEdit", "formRef", "save", "validate", "valid", "method", "url", "code", "msg", "__exports__", "module", "exports"], "mappings": "uhBA0HUA,EAAO,0B,uCACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACV,CAACC,MAAO,OAAQC,KAAM,iBACtB,CAACD,MAAO,SAAUC,KAAM,aACxB,CAACD,MAAO,OAAQC,KAAM,aACtB,CAACD,MAAO,OAAQC,KAAM,gBAGpBC,EAAQ,CACVC,cAAe,CACX,CACIC,UAAU,EACVC,QAAS,UACTC,QAAS,SAGjBC,UAAW,CACP,CACIH,UAAU,EACVC,QAAS,YACTC,QAAS,UAIfE,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJR,cAAe,GACfI,UAAW,MAQbK,EAAUA,KACZJ,EAAKE,KAAKP,cAAgB,GAC1BK,EAAKE,KAAKH,UAAU,IAKlBM,GAHWC,kBAAI,GACLA,iBAAI,IAEIA,iBAAI,KAC5BD,EAAgBE,MAAQC,aAAaC,QAAQ,UAE7C,MAAMC,EAAQT,sBAAS,CACnBN,cAAe,GACfgB,QAAS,EACTC,SAAU,KAERC,EAAYP,iBAAI,IAChBQ,EAAYR,iBAAI,GAEhBS,GADSP,aAAaC,QAAQ,UACdH,kBAAI,IAExBU,EAAoBA,EAAEC,MAAKC,eAE3BA,EAAW,GAAK,GAAK,GACnBC,QAAQC,IAAIF,GACX,YACGA,EAAW,GAAK,GAAK,GACzBC,QAAQC,IAAIF,GACX,iBAFF,EAMDG,EAAaA,EAAEJ,MAAKK,SAAQJ,WAASK,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,GAGDE,EAAUA,KACZC,OACKC,IAAIzC,EAAO,OAAQ,CAChB0C,OAAQnB,IAEXoB,KAAMC,IACHlB,EAAUN,MAAQwB,EAAI7B,KAAK8B,QAC3BlB,EAAUP,MAAQwB,EAAI7B,KAAK+B,SAGvCP,IAEA,MAAMQ,EAAeA,KACjBxB,EAAMC,QAAU,EAChBe,KAGES,EAAoBC,IACtB1B,EAAME,SAAWwB,EACjBV,KAGEW,EAAoBD,IACtB1B,EAAMC,QAAUyB,EAChBV,KAGEY,EAAeA,CAACC,EAAOC,KAEzBC,OAAaC,QAAQ,UAAW,KAAM,CAClCC,KAAM,YAELb,KAAK,KACFH,OAAQiB,OAAOzD,EAAOqD,GAAKV,KAAMC,IACzBA,EAAI7B,MACJ2C,OAAUC,QAAQ,QAClBjC,EAAUN,MAAMwC,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAI3BC,MAAM,SAKTC,EAAYA,KACdnC,EAAcR,OAAQ,EACtBH,KAIE+C,GADc7C,kBAAI,GACJW,IAChBF,EAAcR,OAAQ,EACtBP,EAAKE,KAAKC,GAAKc,EAAId,GACnBH,EAAKE,KAAKP,cAAgBsB,EAAItB,cAC9BK,EAAKE,KAAKH,UAAYkB,EAAIlB,YAExBqD,EAAU9C,iBAAI,MACd+C,EAAOA,KAETD,EAAQ7C,MAAM+C,SAAUC,IACpB,IAAIA,EAuBA,OAAO,EAtBP,IAAIC,EAA0B,KAAjBxD,EAAKE,KAAKC,GAAY,OAAS,MAC5CwB,eAAQ,CACJ8B,IAAK,yBACLD,OAAQA,EACRtD,KAAM,CACFC,GAAGH,EAAKE,KAAKC,GACbR,cAAeK,EAAKE,KAAKP,cACzBI,UAAWC,EAAKE,KAAKH,aAE1B+B,KAAMC,IACL/B,EAAKE,KAAO,GACK,OAAb6B,EAAI2B,MACJhC,IACAmB,OAAUC,QAAQ,SAElB/B,EAAcR,OAAQ,IAEtBQ,EAAcR,OAAQ,EACtBsC,OAAUG,MAAMjB,EAAI4B,W,0oICjR5C,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB,kCCRf,W,4CCAAC,EAAOC,QAAU,IAA0B", "file": "js/chunk-93f6e626.762cf651.js", "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/ReleaseReason.svg\"></i>&nbsp; 放行原因\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form\r\n                        :inline=\"true\"\r\n                        :model=\"query\"\r\n                        class=\"demo-form-inline\"\r\n                        label-width=\"60px\"\r\n                >\r\n                    <el-form-item label-width=\"80px\" label=放行原因>\r\n                        <el-input\r\n                                v-model=\"query.releaseReason\"\r\n                                placeholder=\"放行原因\"\r\n                                class=\"handle-input mr10\"\r\n                                clearable\r\n                        ></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\"\r\n                    >搜索\r\n                    </el-button\r\n                    >\r\n                    <el-button\r\n                            type=\"primary\"\r\n                            class=\"addButton\"\r\n                            @click=\"handleAdd\"\r\n                    >新增\r\n                    </el-button>\r\n                </el-form>\r\n            </div>\r\n            <el-table\r\n                    :data=\"tableData\"\r\n                    border\r\n                    class=\"table\"\r\n                    ref=\"multipleTable\"\r\n                    header-cell-class-name=\"table-header\"\r\n                    :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\"\r\n            >\r\n                <el-table-column\r\n                        :show-overflow-tooltip=\"true\"\r\n                        :prop=\"item.prop\"\r\n                        :label=\"item.label\"\r\n                        v-for=\"item in props\"\r\n                        :key=\"item.prop\"\r\n                        align=\"center\"\r\n                >\r\n                </el-table-column>\r\n\r\n\r\n                <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button\r\n                                type=\"text\"\r\n                                icon=\"el-icon-edit\"\r\n                                @click=\"handleEdit(scope.row)\"\r\n\r\n                        >编辑\r\n                        </el-button>\r\n                        <el-button\r\n                                type=\"text\"\r\n                                icon=\"el-icon-delete\"\r\n                                class=\"red\"\r\n                                @click=\"handleDelete(scope.$index, scope.row.id)\"\r\n\r\n                        >删除\r\n                        </el-button>\r\n\r\n                    </template>\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination\r\n                        :currentPage=\"query.pageNum\"\r\n                        :page-sizes=\"[10, 20, 40]\"\r\n                        :page-size=\"query.pageSize\"\r\n                        layout=\"total, sizes, prev, pager, next, jumper\"\r\n                        :total=\"pageTotal\"\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handlePageChange\"\r\n                >\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"放行原因\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"放行原因\" prop=\"releaseReason\">\r\n                        <el-input v-model=\"form.data.releaseReason\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"放行原因序号\" prop=\"releaseNo\" label-width=\"120px\">\r\n                        <el-input v-model=\"form.data.releaseNo\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\n    import {useRoute, useRouter} from \"vue-router\";\r\n    import {reactive, ref} from \"vue\";\r\n    import request from \"@/utils/request\";\r\n    import {ElMessage, ElMessageBox} from \"element-plus\";\r\n    import {useStore} from \"vuex\";\r\n\r\n\r\n    import XLSX from \"xlsx\";\r\n    const root = \"/parking/releaseReason/\";\r\n    const router = useRouter();\r\n    const route = useRoute();\r\n    const store = useStore();\r\n    const props = [\r\n        {label: \"放行原因\", prop: \"releaseReason\"},\r\n        {label: \"放行原因序号\", prop: \"releaseNo\"},\r\n        {label: \"创建时间\", prop: \"gmtCreate\"},\r\n        {label: \"修改时间\", prop: \"gmtModified\"},\r\n    ];\r\n\r\n    const rules = {\r\n        releaseReason: [\r\n            {\r\n                required: true,\r\n                message: \"请输入放行原因\",\r\n                trigger: \"blur\",\r\n            },\r\n        ],\r\n        releaseNo: [\r\n            {\r\n                required: true,\r\n                message: \"请输入放行原因序号\",\r\n                trigger: \"blur\",\r\n            },\r\n        ],\r\n    };\r\n    const form = reactive({\r\n        data: {\r\n            id: '',\r\n            releaseReason: '',\r\n            releaseNo: ''\r\n        },\r\n    })\r\n\r\n    const handleExport = () => {\r\n        window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n    };\r\n    // 重置\r\n    const onReset = () => {\r\n        form.data.releaseReason = ''\r\n        form.data.releaseNo=''\r\n    };\r\n    const viewShow = ref(false)\r\n    const content = ref(\"\");\r\n\r\n    const applicantUserId = ref(\"\");\r\n    applicantUserId.value = localStorage.getItem(\"userId\")\r\n\r\n    const query = reactive({\r\n        releaseReason: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n    });\r\n    const tableData = ref([]);\r\n    const pageTotal = ref(0);\r\n    const userId = localStorage.getItem(\"userId\")\r\n    const dialogVisible = ref(false)\r\n//指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\n    // 获取表格数据\r\n    const getData = () => {\r\n        request\r\n            .get(root + \"page\", {\r\n                params: query,\r\n            })\r\n            .then((res) => {\r\n                tableData.value = res.data.records;\r\n                pageTotal.value = res.data.total;\r\n            });\r\n    };\r\n    getData();\r\n    // 查询操作\r\n    const handleSearch = () => {\r\n        query.pageNum = 1;\r\n        getData();\r\n    };\r\n    // 分页大小\r\n    const handleSizeChange = (val) => {\r\n        query.pageSize = val;\r\n        getData();\r\n    };\r\n    // 分页导航\r\n    const handlePageChange = (val) => {\r\n        query.pageNum = val;\r\n        getData();\r\n    };\r\n    // 删除操作\r\n    const handleDelete = (index, sid) => {\r\n        // 二次确认删除\r\n        ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n            type: \"warning\",\r\n        })\r\n            .then(() => {\r\n                request.delete(root + sid).then((res) => {\r\n                    if (res.data) {\r\n                        ElMessage.success(\"删除成功\");\r\n                        tableData.value.splice(index, 1);\r\n                    } else {\r\n                        ElMessage.error(\"删除失败\");\r\n                    }\r\n                });\r\n            })\r\n            .catch(() => {\r\n            });\r\n    };\r\n\r\n    // 新增操作\r\n    const handleAdd = () => {\r\n        dialogVisible.value = true;\r\n        onReset();\r\n    };\r\n    // 表格编辑时弹窗和保存\r\n    const editVisible = ref(false);\r\n    const handleEdit = (row) => {\r\n        dialogVisible.value = true\r\n        form.data.id = row.id\r\n        form.data.releaseReason = row.releaseReason\r\n        form.data.releaseNo = row.releaseNo\r\n    };\r\n    const formRef = ref(null);\r\n    const save = () => {\r\n        // 表单校验\r\n        formRef.value.validate((valid) => {\r\n            if (valid) {\r\n                var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n                request({\r\n                    url: \"/parking/releaseReason\",\r\n                    method: method,\r\n                    data: {\r\n                        id:form.data.id,\r\n                        releaseReason: form.data.releaseReason,\r\n                        releaseNo: form.data.releaseNo,\r\n                    },\r\n                }).then((res) => {\r\n                    form.data = {}\r\n                    if (res.code === null) {\r\n                        getData()\r\n                        ElMessage.success(\"提交成功！\");\r\n                        // 关闭当前页面的标签页;\r\n                        dialogVisible.value = false\r\n                    } else {\r\n                        dialogVisible.value = false\r\n                        ElMessage.error(res.msg);\r\n                    }\r\n                });\r\n            } else {\r\n                return false;\r\n            }\r\n        });\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n\tbackground-color: rgb(241, 242, 244) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>", "import script from \"./ReleaseReason.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./ReleaseReason.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./ReleaseReason.vue?vue&type=style&index=0&id=346c99ec&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-346c99ec\"]])\n\nexport default __exports__", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./ReleaseReason.vue?vue&type=style&index=0&id=346c99ec&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/ReleaseReason.31dc9c95.svg\";"], "sourceRoot": ""}