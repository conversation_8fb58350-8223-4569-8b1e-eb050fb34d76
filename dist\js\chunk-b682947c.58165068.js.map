{"version": 3, "sources": ["webpack:///./src/views/admin/Gate.vue", "webpack:///./src/views/admin/Gate.vue?eb40", "webpack:///./src/views/admin/Gate.vue?9b2d", "webpack:///./src/icons/svg-black/Gate.svg"], "names": ["root", "treeVisible", "ref", "treeBuilding", "props", "useRouter", "useRoute", "useStore", "label", "prop", "form", "reactive", "data", "id", "province", "city", "district", "community", "gatename", "parkingcode", "parkingsecret", "parkingkey", "ticketsData", "ticketList", "ticketName", "ticketCode", "treeData", "arrayId", "onReset", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "tableRowClassName", "row", "rowIndex", "console", "log", "cellStyle", "column", "columnIndex", "style", "padding", "getData", "request", "get", "params", "then", "res", "value", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleAdd", "handleEdit", "gateRules", "required", "message", "trigger", "provinceList", "cityList", "districtList", "communityList", "changeProvince", "changeCity", "changeDistrict", "formRef", "ticketFormRef", "save", "validate", "valid", "method", "url", "code", "msg", "treeProps", "children", "ticketRules", "handleTickets", "getTickets", "gateid", "length", "ticketcode", "ticketname", "arr", "i", "push", "building", "set<PERSON><PERSON><PERSON><PERSON>eys", "changeTickets", "handleCheckChange", "getChe<PERSON><PERSON>eys", "saveTree", "<PERSON>man", "localStorage", "getItem", "parkingKey", "parkingSecret", "parkingCode", "JSON", "parse", "recordList", "__exports__", "module", "exports"], "mappings": "mjBA6NMA,EAAO,iB,8BAFb,MAAMC,EAAcC,kBAAI,GAClBC,EAAeD,mBAKfE,GAHSC,iBACDC,iBACAC,iBACA,CACZ,CAACC,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,QACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,aACpB,CAACD,MAAO,QAASC,KAAM,YACvB,CAACD,MAAO,QAASC,KAAM,eACvB,CAACD,MAAO,SAAUC,KAAM,iBACxB,CAACD,MAAO,MAAOC,KAAM,gBAIjBC,EAAOC,sBAAS,CACpBC,KAAM,CACJC,GAAI,GACJC,SAAU,GACVC,KAAM,GACNC,SAAU,GACVC,UAAW,GACXC,SAAU,GACVC,YAAa,GACbC,cAAe,GACfC,WAAY,IAEdC,YAAY,CACVT,GAAI,GACJM,YAAa,GACbC,cAAe,GACfC,WAAY,GACZE,WAAW,GACXC,WAAW,GACXC,WAAW,GACXX,SAAU,GACVC,KAAM,GACNC,SAAU,GACVC,UAAW,GACXS,SAAS,GACTC,QAAQ,MAONC,EAAUA,KACdlB,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKE,SAAW,GACrBJ,EAAKE,KAAKG,KAAO,GACjBL,EAAKE,KAAKI,SAAW,GACrBN,EAAKE,KAAKK,UAAY,GACtBP,EAAKE,KAAKM,SAAW,GACrBR,EAAKE,KAAKO,YAAc,GACxBT,EAAKE,KAAKQ,cAAgB,GAC1BV,EAAKE,KAAKS,WAAY,GAEtBX,EAAKY,YAAYT,GAAI,GACrBH,EAAKY,YAAYH,YAAc,GAC/BT,EAAKY,YAAYD,WAAY,GAC7BX,EAAKY,YAAYF,cAAgB,GACjCV,EAAKY,YAAYR,SAAW,GAC5BJ,EAAKY,YAAYP,KAAO,GACxBL,EAAKY,YAAYN,SAAW,GAC5BN,EAAKY,YAAYL,UAAY,GAC7BP,EAAKY,YAAYG,WAAa,GAC9Bf,EAAKY,YAAYE,WAAa,GAC9Bd,EAAKY,YAAYI,SAAS,GAC1BhB,EAAKY,YAAYK,QAAQ,IAyBrBE,GAvBW3B,kBAAI,GACLA,iBAAI,IAUFA,kBAAI,GACLA,iBAAI,IAWPS,sBAAS,CACrBM,UAAW,GACXC,SAAU,GACVY,QAAS,EACTC,SAAU,MAENC,EAAY9B,iBAAI,IAChB+B,EAAY/B,iBAAI,GAChBgC,EAAgBhC,kBAAI,GAEpBiC,EAAoBA,EAAEC,MAAKC,eAE3BA,EAAW,GAAK,GAAK,GACnBC,QAAQC,IAAIF,GACX,YACGA,EAAW,GAAK,GAAK,GACzBC,QAAQC,IAAIF,GACX,iBAFF,EAMDG,EAAaA,EAAEJ,MAAKK,SAAQJ,WAASK,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,GAKLE,EAAUA,KACdC,OACKC,IAAI/C,EAAO,YAAa,CACvBgD,OAAQnB,IAEToB,KAAMC,IACLlB,EAAUmB,MAAQD,EAAItC,KAAKwC,QAC3BnB,EAAUkB,MAAQD,EAAItC,KAAKyC,SAGnCR,IAEA,MAAMS,EAAeA,KACnBzB,EAAMC,QAAU,EAChBe,KAGIU,EAAoBC,IACxB3B,EAAME,SAAWyB,EACjBX,KAGIY,EAAoBD,IACxB3B,EAAMC,QAAU0B,EAChBX,KAGIa,EAAeA,CAACC,EAAOC,KAE3BC,OAAaC,QAAQ,UAAW,KAAM,CACpCC,KAAM,YAEHd,KAAK,KACJH,OAAQkB,OAAOhE,EAAO4D,GAAKX,KAAMC,IAC3BA,EAAItC,MACNqD,OAAUC,QAAQ,QAClBlC,EAAUmB,MAAMgB,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAIrBC,MAAM,SAKPC,EAAYA,KAChBpC,EAAciB,OAAQ,EACtBvB,KAKI2C,GADcrE,kBAAI,GACJkC,IAClBF,EAAciB,OAAQ,EACtBzC,EAAKE,KAAKC,GAAKuB,EAAIvB,GACnBH,EAAKE,KAAKE,SAAWsB,EAAItB,SACzBJ,EAAKE,KAAKG,KAAOqB,EAAIrB,KACrBL,EAAKE,KAAKI,SAAWoB,EAAIpB,SACzBN,EAAKE,KAAKK,UAAYmB,EAAInB,UAC1BP,EAAKE,KAAKM,SAAWkB,EAAIlB,SACzBR,EAAKE,KAAKO,YAAciB,EAAIjB,YAC5BT,EAAKE,KAAKQ,cAAgBgB,EAAIhB,cAC9BV,EAAKE,KAAKS,WAAWe,EAAIf,aAEpBmD,EAAW,CAChB1D,SAAU,CACJ,CACE2D,UAAU,EACVC,QAAS,QACTC,QAAS,WAGb5D,KAAM,CACJ,CACE0D,UAAU,EACVC,QAAS,QACTC,QAAS,WAGb3D,SAAU,CACR,CACEyD,UAAU,EACVC,QAAS,QACTC,QAAS,WAGb1D,UAAW,CACT,CACEwD,UAAU,EACVC,QAAS,QACTC,QAAS,WAGbzD,SAAU,CACR,CACEuD,UAAU,EACVC,QAAS,WACTC,QAAS,SAGbxD,YAAa,CACX,CACEsD,UAAU,EACVC,QAAS,WACTC,QAAS,SAGbvD,cAAe,CACb,CACEqD,UAAU,EACVC,QAAS,YACTC,QAAS,SAGbtD,WAAY,CACV,CACEoD,UAAU,EACVC,QAAS,SACTC,QAAS,UAIbC,EAAe1E,iBAAI,IACnB2E,EAAW3E,iBAAI,IACf4E,EAAe5E,iBAAI,IACnB6E,EAAgB7E,iBAAI,IAG1B4C,OAAQC,IAAI,+BAA+BE,KAAMC,IAC/C0B,EAAazB,MAAQD,EAAItC,OAE3B,MAAMoE,EAAiBA,KACrBlC,OACKC,IAAI,0BACL,CACEC,OAAQ,CACNlC,SAASJ,EAAKE,KAAKE,YAGtBmC,KAAMC,IACL2B,EAAS1B,MAAQD,EAAItC,KACrBF,EAAKE,KAAKG,KAAK,GACfL,EAAKE,KAAKI,SAAS,GACnBN,EAAKE,KAAKK,UAAU,MAItBgE,EAAaA,KACjB3C,QAAQC,IAAI7B,EAAKE,KAAKE,UACtBgC,OACKC,IAAI,8BACL,CACEC,OAAQ,CACNlC,SAASJ,EAAKE,KAAKE,SACnBC,KAAKL,EAAKE,KAAKG,QAGlBkC,KAAMC,IACL4B,EAAa3B,MAAQD,EAAItC,KACzBF,EAAKE,KAAKI,SAAS,GACnBN,EAAKE,KAAKK,UAAU,MAKtBiE,EAAiBA,KACrBpC,OACKC,IAAI,+BACL,CACEC,OAAQ,CACNlC,SAASJ,EAAKE,KAAKE,SACnBC,KAAKL,EAAKE,KAAKG,KACfC,SAASN,EAAKE,KAAKI,YAGtBiC,KAAMC,IACL6B,EAAc5B,MAAQD,EAAItC,KAC1BF,EAAKE,KAAKK,UAAU,MAQtBkE,EAAUjF,iBAAI,MACdkF,EAAgBlF,iBAAI,MACpBmF,EAAOA,KAEXF,EAAQhC,MAAMmC,SAAUC,IAEtB,GADAjD,QAAQC,IAAI7B,EAAKE,KAAKC,KAClB0E,EAqBF,OAAO,EApBP,IAAIC,EAA0B,KAAjB9E,EAAKE,KAAKC,GAAY,OAAS,MAC5CyB,QAAQC,IAAIiD,GACZ1C,eAAQ,CACN2C,IAAKzF,EACLwF,OAAQA,EACR5E,KAAMF,EAAKE,OACVqC,KAAMC,IAEPxC,EAAKE,KAAO,GACK,OAAbsC,EAAIwC,MACN7C,IACAoB,OAAUC,QAAQ,SAElBhC,EAAciB,OAAQ,IAEtBjB,EAAciB,OAAQ,EACtBc,OAAUG,MAAMlB,EAAIyC,WAQxBC,EAAa,CACTC,SAAU,WACVrF,MAAO,YAGXsF,EAAc,CAClBtE,WAAY,CACN,CACEiD,UAAU,EACVC,QAAS,YAIjB/C,QAAQ,CAAE,CAAE8C,UAAU,EAAMC,QAAS,MAAOC,QAAS,YAIjDoB,EAAiB3D,IACrBR,IACA3B,EAAYkD,OAAQ,EACpBzC,EAAKY,YAAYT,GAAKuB,EAAIvB,GAC1BH,EAAKY,YAAYH,YAAciB,EAAIjB,YACnCT,EAAKY,YAAYD,WAAae,EAAIf,WAClCX,EAAKY,YAAYF,cAAgBgB,EAAIhB,cACrCV,EAAKY,YAAYR,SAAWsB,EAAItB,SAChCJ,EAAKY,YAAYP,KAAOqB,EAAIrB,KAC5BL,EAAKY,YAAYN,SAAWoB,EAAIpB,SAChCN,EAAKY,YAAYL,UAAYmB,EAAInB,UACjCqB,QAAQC,IAAI7B,EAAKY,aAEjB0E,IAEAlD,eAAQ,CACN2C,IAAK,iCACLD,OAAQ,MACRxC,OAAQ,CACNlC,SAASJ,EAAKY,YAAYR,SAC1BC,KAAKL,EAAKY,YAAYP,KACtBC,SAASN,EAAKY,YAAYN,SAC1BC,UAAUP,EAAKY,YAAYL,aAE5BgC,KAAMC,IACPZ,QAAQC,IAAIW,GACZxC,EAAKY,YAAYI,SAASwB,EAAItC,KAE9BkC,eAAQ,CACN2C,IAAK,qCACLD,OAAQ,MACRxC,OAAQ,CACNiD,OAAOvF,EAAKY,YAAYT,MAEzBoC,KAAMC,IAEP,GADAZ,QAAQC,IAAIW,EAAItC,MACbsC,EAAItC,KAAKsF,OAAO,EAAE,CACnBxF,EAAKY,YAAYG,WAAWyB,EAAItC,KAAK,GAAGuF,WACxCzF,EAAKY,YAAYE,WAAW0B,EAAItC,KAAK,GAAGwF,WACxC,IAAIC,EAAI,GACR,IAAI,IAAIC,EAAE,EAAEA,EAAEpD,EAAItC,KAAKsF,OAAOI,IAC5BD,EAAIE,KAAKrD,EAAItC,KAAK0F,GAAGE,UAEvBlE,QAAQC,IAAI8D,GACZlG,EAAagD,MAAMsD,eAAeJ,QAElClG,EAAagD,MAAMsD,eAAe,SAKpCC,EAAgBA,KACtBpE,QAAQC,IAAI7B,EAAKY,YAAYE,YAC7B,IAAI,IAAI8E,EAAE,EAAEA,EAAE5F,EAAKY,YAAYC,WAAW2E,OAAOI,IAC/C,GAAG5F,EAAKY,YAAYC,WAAW+E,GAAG9E,YAAYd,EAAKY,YAAYE,WAI7D,OAFAd,EAAKY,YAAYG,WAAWf,EAAKY,YAAYC,WAAW+E,GAAG7E,gBAC3Da,QAAQC,IAAI7B,EAAKY,YAAYG,aAM3BkF,EAAkBA,KAClBjG,EAAKY,YAAYK,QAASxB,EAAagD,MAAMyD,kBAE7CC,EAAWA,KACfzB,EAAcjC,MAAMmC,SAAUC,IAE5B,GADAjD,QAAQC,IAAI7B,EAAKY,YAAYE,aACzB+D,EA8BF,OAAO,EA9BE,CACT,IAAI5D,EACJA,EAAQxB,EAAagD,MAAMyD,iBAC3BlG,EAAKY,YAAYK,QAAQA,EACzBW,QAAQC,IAAIZ,GACZW,QAAQC,IAAI7B,EAAKY,aACbwB,eAAQ,CACN2C,IAAK,iCACLD,OAAQ,OACRxC,OAAQ,CACNiD,OAAOvF,EAAKY,YAAYT,GACxBiG,UAAUC,aAAaC,QAAQ,aAC/Bb,WAAWzF,EAAKY,YAAYG,WAC5B2E,WAAW1F,EAAKY,YAAYE,WAC5BG,QAAQjB,EAAKY,YAAYK,WAE1BsB,KAAMC,IAEPxC,EAAKE,KAAO,GACK,OAAbsC,EAAIwC,MACN7C,IACAoB,OAAUC,QAAQ,SAElBjE,EAAYkD,OAAQ,IAEpBlD,EAAYkD,OAAQ,EACpBc,OAAUG,MAAMlB,EAAIyC,YAQ5BK,EAAaA,KACjB1D,QAAQC,IAAI,YACZO,OACKC,IAAI,2BACL,CACEC,OAAQ,CACNiE,WAAWvG,EAAKY,YAAYD,WAC5B6F,cAAcxG,EAAKY,YAAYF,cAC/B+F,YAAazG,EAAKY,YAAYH,eAGjC8B,KAAMC,IACL,IAAItC,EAAKwG,KAAKC,MAAMnE,EAAItC,MACxB0B,QAAQC,IAAI3B,GACZ0B,QAAQC,IAAI3B,EAAKA,KAAK0G,YACtB5G,EAAKY,YAAYC,WAAcX,EAAKA,KAAK0G,c,2gSCnrBjD,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB,yDCRf,W,qBCAAC,EAAOC,QAAU,IAA0B", "file": "js/chunk-b682947c.58165068.js", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/Gate.svg\"></i>&nbsp;出入口系统绑定\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n            :inline=\"true\"\r\n            :model=\"query\"\r\n            class=\"demo-form-inline\"\r\n            label-width=\"60px\"\r\n        >\r\n          <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n            <el-input\r\n                v-model=\"query.community\"\r\n                placeholder=\"小区名称\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"90px\" label=\"出入口名称\">\r\n            <el-input\r\n                v-model=\"query.gatename\"\r\n                placeholder=\"出入口名称\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" class=\"searchButton\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <el-button\r\n              type=\"primary\"\r\n              class=\"addButton\" \r\n              @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>  \r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n          :data=\"tableData\"\r\n          border\r\n          class=\"table\"\r\n          ref=\"multipleTable\"\r\n          header-cell-class-name=\"table-header\"\r\n          :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n            :show-overflow-tooltip=\"true\"\r\n            :prop=\"item.prop\"\r\n            :label=\"item.label\"\r\n            v-for=\"item in props\"\r\n            :key=\"item.prop\"\r\n            align=\"center\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleEdit(scope.row)\"\r\n                    >编辑\r\n                    </el-button>\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-delete\"\r\n                        class=\"red\"\r\n                        @click=\"handleDelete(scope.$index, scope.row.id)\"\r\n                    >删除\r\n                    </el-button>\r\n                    <el-button\r\n                        type=\"text\"\r\n                        icon=\"el-icon-user\"\r\n                        class=\"red\"\r\n                        @click=\"handleTickets(scope.row)\"\r\n                    >权限\r\n                    </el-button>  \r\n            </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            :currentPage=\"query.pageNum\"\r\n            :page-sizes=\"[10, 20, 40]\"\r\n            :page-size=\"query.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"pageTotal\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"出入口系统绑定\" v-model=\"dialogVisible\" width=\"50%\">\r\n        <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"gateRules\" label-width=\"110px\">\r\n          <el-form-item label=\"省份\" prop=\"province\">\r\n            <el-select v-model=\"form.data.province\" placeholder=\"请选择省份\">\r\n              <el-option\r\n                  v-for=\"item in provinceList\"\r\n                  :key=\"item.province\"\r\n                  :label=\"item.province\"\r\n                  :value=\"item.province\"\r\n                  @click=\"changeProvince\" \r\n                >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"地市\" prop=\"city\">\r\n            <el-select v-model=\"form.data.city\" placeholder=\"请选择地市\">\r\n              <el-option\r\n                  v-for=\"item in cityList\"\r\n                  :key=\"item.city\"\r\n                  :label=\"item.city\"\r\n                  :value=\"item.city\"\r\n                  @click=\"changeCity\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"区县\" prop=\"district\">\r\n            <el-select v-model=\"form.data.district\" placeholder=\"请选择区县\">\r\n              <el-option\r\n                  v-for=\"item in districtList\"\r\n                  :key=\"item.district\"\r\n                  :label=\"item.district\"\r\n                  :value=\"item.district\"\r\n                  @click=\"changeDistrict\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>    \r\n          <el-form-item label=\"小区\" prop=\"community\">\r\n            <el-select v-model=\"form.data.community\" placeholder=\"请选择小区\">\r\n              <el-option\r\n                  v-for=\"item in communityList\"\r\n                  :key=\"item.community\"\r\n                  :label=\"item.community\"\r\n                  :value=\"item.community\"\r\n                  @click=\"changeCommunity\"  \r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>    \r\n          <el-form-item label=\"出入口名称\" prop=\"gatename\">\r\n            <el-input v-model=\"form.data.gatename\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"出入口编码\" prop=\"parkingcode\">\r\n            <el-input v-model=\"form.data.parkingcode\"  style=\"width: 80%\"></el-input>\r\n          </el-form-item>   \r\n          <el-form-item label=\"parkingsecret\" prop=\"parkingsecret\">\r\n            <el-input v-model=\"form.data.parkingsecret\" style=\"width: 80%\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"parkingkey\" prop=\"parkingkey\">\r\n            <el-input v-model=\"form.data.parkingkey\"  style=\"width: 80%\"\r\n            @blur=\"getTickets\"  ></el-input>\r\n          </el-form-item>                                                                               \r\n        </el-form>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>\r\n    <div>\r\n      <el-dialog title=\"楼栋权限\" v-model=\"treeVisible\" width=\"50%\">      \r\n        <el-form  :model=\"form.ticketsData\" ref=\"ticketFormRef\" :rules=\"ticketRules\" label-width=\"100px\">\r\n          <el-form-item label=\"月票类型\" prop=\"ticketName\">\r\n            <el-select v-model=\"form.ticketsData.ticketName\" placeholder=\"请选择月票类型\">\r\n              <el-option\r\n                  v-for=\"item in form.ticketsData.ticketList\"\r\n                  :key=\"item.ticketName\"\r\n                  :label=\"item.ticketName\"\r\n                  :value=\"item.ticketName\"  \r\n                  @click=\"changeTickets\"    \r\n                >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>   \r\n          <el-form-item label=\"选择楼栋\" prop=\"arrayId\">        \r\n          <el-tree\r\n            ref=\"treeBuilding\"\r\n            :data=\"form.ticketsData.treeData\"\r\n            show-checkbox\r\n            node-key=\"id\"\r\n            @check-change=\"handleCheckChange\"\r\n            :props=\"treeProps\">\r\n          </el-tree>   \r\n        </el-form-item>                                                                  \r\n        </el-form>\r\n        <template #footer>\r\n          <span class=\"dialog-footer\">\r\n            <el-button @click=\"treeVisible = false\">取 消</el-button>\r\n            <el-button type=\"primary\" @click=\"saveTree\">确 定</el-button>\r\n          </span>\r\n        </template>\r\n      </el-dialog>\r\n    </div>    \r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport {useRoute, useRouter} from \"vue-router\";\r\nimport {reactive, ref} from \"vue\";\r\nimport request from \"@/utils/request\";\r\n\r\nimport {ElMessage, ElMessageBox} from \"element-plus\";\r\nimport {useStore} from \"vuex\";\r\n\r\nconst treeVisible = ref(false)\r\nconst treeBuilding = ref();\r\nconst root = \"/parking/gate/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  {label: \"省份\", prop: \"province\"},\r\n  {label: \"地市\", prop: \"city\"},\r\n  {label: \"县区\", prop: \"district\"},\r\n  {label: \"小区\", prop: \"community\"},\r\n  {label: \"出入口名称\", prop: \"gatename\"},\r\n  {label: \"出入口编码\", prop: \"parkingcode\"},\r\n  {label: \"secret\", prop: \"parkingsecret\"},\r\n  {label: \"key\", prop: \"parkingkey\"},\r\n\r\n];\r\n\r\nconst form = reactive({\r\n  data: {\r\n    id: '',\r\n    province: '',\r\n    city: '',\r\n    district: '',\r\n    community: '',\r\n    gatename: '',\r\n    parkingcode: '',\r\n    parkingsecret: '',\r\n    parkingkey: '',  \r\n  },\r\n  ticketsData:{\r\n    id: '',\r\n    parkingcode: '',\r\n    parkingsecret: '',\r\n    parkingkey: '',  \r\n    ticketList:[],\r\n    ticketName:'',\r\n    ticketCode:'',\r\n    province: '',\r\n    city: '',\r\n    district: '',\r\n    community: '',\r\n    treeData:[],\r\n    arrayId:[],\r\n  },\r\n});\r\nconst handleExport = () => {\r\n  window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n  form.data.id = ''\r\n  form.data.province = ''\r\n  form.data.city = ''\r\n  form.data.district = ''\r\n  form.data.community = ''\r\n  form.data.gatename = ''\r\n  form.data.parkingcode = ''\r\n  form.data.parkingsecret = ''\r\n  form.data.parkingkey= ''\r\n\r\n  form.ticketsData.id =''\r\n  form.ticketsData.parkingcode = ''\r\n  form.ticketsData.parkingkey =''\r\n  form.ticketsData.parkingsecret = '' \r\n  form.ticketsData.province = ''\r\n  form.ticketsData.city = ''\r\n  form.ticketsData.district = ''\r\n  form.ticketsData.community = ''    \r\n  form.ticketsData.ticketCode = ''\r\n  form.ticketsData.ticketName = ''  \r\n  form.ticketsData.treeData=[],\r\n  form.ticketsData.arrayId=[]\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\nconst handleView = (row) => {\r\n  console.log(\"这批我\")\r\n  if (row.fileReason !== null) {\r\n    viewShow.value = true\r\n    content.value = row.fileReason\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\nconst viewShow1 = ref(false)\r\nconst content1 = ref(\"\");\r\nconst handleView1 = (row) => {\r\n  console.log(\"这批我\")\r\n  if (row.purchaseVoucher !== null) {\r\n    viewShow.value = true\r\n    content1.value = row.purchaseVoucher\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\n\r\nconst query = reactive({\r\n  community: \"\",\r\n  gatename: \"\",\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst dialogVisible = ref(false)\r\n//指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\n\r\n// 获取表格数据\r\n\r\nconst getData = () => {\r\n  request\r\n      .get(root + \"querypage\", {\r\n        params: query,\r\n      })\r\n      .then((res) => {\r\n        tableData.value = res.data.records;\r\n        pageTotal.value = res.data.total;\r\n      });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n  // 二次确认删除\r\n  ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n    type: \"warning\",\r\n  })\r\n      .then(() => {\r\n        request.delete(root + sid).then((res) => {\r\n          if (res.data) {\r\n            ElMessage.success(\"删除成功\");\r\n            tableData.value.splice(index, 1);\r\n          } else {\r\n            ElMessage.error(\"删除失败\");\r\n          }\r\n        });\r\n      })\r\n      .catch(() => {\r\n      });\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n  dialogVisible.value = true;\r\n  onReset();\r\n\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n  dialogVisible.value = true\r\n  form.data.id = row.id\r\n  form.data.province = row.province\r\n  form.data.city = row.city\r\n  form.data.district = row.district\r\n  form.data.community = row.community\r\n  form.data.gatename = row.gatename\r\n  form.data.parkingcode = row.parkingcode\r\n  form.data.parkingsecret = row.parkingsecret\r\n  form.data.parkingkey=row.parkingkey\r\n};\r\nconst  gateRules= {\r\n  province: [\r\n        {\r\n          required: true,\r\n          message: \"请选择省份\",\r\n          trigger: 'change'\r\n        },\r\n      ],  \r\n      city: [\r\n        {\r\n          required: true,\r\n          message: \"请选择地市\",\r\n          trigger: 'change'\r\n        },\r\n      ],     \r\n      district: [\r\n        {\r\n          required: true,\r\n          message: \"请选择县区\",\r\n          trigger: 'change'\r\n        },\r\n      ],       \r\n      community: [\r\n        {\r\n          required: true,\r\n          message: \"请选择小区\",\r\n          trigger: 'change'\r\n        },\r\n      ],              \r\n      gatename: [\r\n        {\r\n          required: true,\r\n          message: \"请填写出入口名称\",\r\n          trigger: 'blur'\r\n        },\r\n      ],  \r\n      parkingcode: [\r\n        {\r\n          required: true,\r\n          message: \"请填写出入口编码\",\r\n          trigger: 'blur'\r\n        },\r\n      ],  \r\n      parkingsecret: [\r\n        {\r\n          required: true,\r\n          message: \"请填写secret\",\r\n          trigger: 'blur'\r\n        },\r\n      ],  \r\n      parkingkey: [\r\n        {\r\n          required: true,\r\n          message: \"请填写key\",\r\n          trigger: 'blur'\r\n        },\r\n      ],                    \r\n};\r\nconst provinceList = ref([]);\r\nconst cityList = ref([]);\r\nconst districtList = ref([]);\r\nconst communityList = ref([]);\r\n\r\n\r\nrequest.get(\"/parking/community/province\").then((res) => {\r\n  provinceList.value = res.data;\r\n});\r\nconst changeProvince = () => {\r\n  request\r\n      .get(\"/parking/community/city\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        cityList.value = res.data;\r\n        form.data.city=\"\";\r\n        form.data.district=\"\";\r\n        form.data.community=\"\";                    \r\n      });\r\n\r\n};\r\nconst changeCity = () => {\r\n  console.log(form.data.province);\r\n  request\r\n      .get(\"/parking/community/district\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n          city:form.data.city,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        districtList.value = res.data;\r\n        form.data.district=\"\";\r\n        form.data.community=\"\";\r\n \r\n      });\r\n\r\n};\r\nconst changeDistrict = () => {\r\n  request\r\n      .get(\"/parking/community/community\",\r\n      {\r\n        params: {\r\n          province:form.data.province,\r\n          city:form.data.city,\r\n          district:form.data.district,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        communityList.value = res.data;\r\n        form.data.community=\"\";\r\n\r\n      });\r\n\r\n};\r\n\r\n\r\n\r\nconst formRef = ref(null);\r\nconst ticketFormRef = ref(null);\r\nconst save = () => {\r\n  // 表单校验\r\n  formRef.value.validate((valid) => {\r\n    console.log(form.data.id);\r\n    if (valid) {\r\n      var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n      console.log(method);\r\n      request({\r\n        url: root,\r\n        method: method,\r\n        data: form.data,\r\n      }).then((res) => {\r\n\r\n        form.data = {}\r\n        if (res.code === null) {\r\n          getData()\r\n          ElMessage.success(\"提交成功！\");\r\n          // 关闭当前页面的标签页;\r\n          dialogVisible.value = false\r\n        } else {\r\n          dialogVisible.value = false\r\n          ElMessage.error(res.msg);\r\n        }\r\n      });\r\n    } else {\r\n      return false;\r\n    }\r\n  });\r\n};\r\nconst treeProps =  {\r\n          children: 'children',\r\n          label: 'building'\r\n};\r\n\r\nconst ticketRules = {\r\n  ticketName: [\r\n        {\r\n          required: true,\r\n          message: \"请选择月票类型\",\r\n         \r\n        },\r\n      ],  \r\n  arrayId:[ { required: true, message: '请选择', trigger: 'change' }, \r\n      ],  \r\n};\r\n// 月票楼栋\r\nconst handleTickets = (row) => {\r\n  onReset();\r\n  treeVisible.value = true;\r\n  form.ticketsData.id = row.id\r\n  form.ticketsData.parkingcode = row.parkingcode\r\n  form.ticketsData.parkingkey = row.parkingkey\r\n  form.ticketsData.parkingsecret = row.parkingsecret  \r\n  form.ticketsData.province = row.province\r\n  form.ticketsData.city = row.city\r\n  form.ticketsData.district = row.district\r\n  form.ticketsData.community = row.community    \r\n  console.log(form.ticketsData);\r\n  //获得多有楼层\r\n  getTickets();\r\n  //获得多有楼层\r\n  request({\r\n    url: \"/parking/community/getBuilding\",\r\n    method: \"GET\",\r\n    params: {\r\n      province:form.ticketsData.province,\r\n      city:form.ticketsData.city,\r\n      district:form.ticketsData.district,      \r\n      community:form.ticketsData.community,\r\n    },\r\n  }).then((res) => {\r\n    console.log(res);\r\n    form.ticketsData.treeData=res.data\r\n    //所管楼栋\r\n    request({\r\n      url: \"/parking/tickets/getManageBuilding\",\r\n      method: \"GET\",\r\n      params: {\r\n        gateid:form.ticketsData.id\r\n      },\r\n    }).then((res) => {\r\n      console.log(res.data);\r\n      if(res.data.length>0){\r\n        form.ticketsData.ticketCode=res.data[0].ticketcode\r\n        form.ticketsData.ticketName=res.data[0].ticketname\r\n        var arr=[];\r\n        for(let i=0;i<res.data.length;i++){\r\n          arr.push(res.data[i].building)\r\n        }\r\n        console.log(arr);\r\n        treeBuilding.value.setCheckedKeys(arr)\r\n      }else{\r\n        treeBuilding.value.setCheckedKeys([])\r\n      }   \r\n    });\r\n  }); \r\n}\r\nconst changeTickets = () => {\r\nconsole.log(form.ticketsData.ticketName)\r\nfor(let i=0;i<form.ticketsData.ticketList.length;i++){\r\n  if(form.ticketsData.ticketList[i].ticketName==form.ticketsData.ticketName)\r\n  {\r\n    form.ticketsData.ticketCode=form.ticketsData.ticketList[i].ticketCode\r\n    console.log(form.ticketsData.ticketCode)\r\n    return\r\n\r\n  }\r\n}\r\n};\r\nconst handleCheckChange=() =>{\r\n      form.ticketsData.arrayId= treeBuilding.value.getCheckedKeys();\r\n  }\r\nconst saveTree = () => {\r\n  ticketFormRef.value.validate((valid) => {\r\n    console.log(form.ticketsData.ticketName)\r\n    if (valid) {  \r\n      let arrayId;\r\n      arrayId=treeBuilding.value.getCheckedKeys();\r\n      form.ticketsData.arrayId=arrayId;\r\n      console.log(arrayId);\r\n      console.log(form.ticketsData);\r\n          request({\r\n            url: \"/parking/tickets/insertTickets\",\r\n            method: \"POST\",\r\n            params: {\r\n              gateid:form.ticketsData.id,\r\n              createman:localStorage.getItem(\"loginname\"),\r\n              ticketcode:form.ticketsData.ticketCode,\r\n              ticketname:form.ticketsData.ticketName,\r\n              arrayId:form.ticketsData.arrayId,\r\n            },\r\n          }).then((res) => {\r\n\r\n            form.data = {}\r\n            if (res.code === null) {\r\n              getData()\r\n              ElMessage.success(\"提交成功！\");\r\n              // 关闭当前页面的标签页;\r\n              treeVisible.value = false\r\n            } else {\r\n              treeVisible.value = false\r\n              ElMessage.error(res.msg);\r\n            }\r\n          });\r\n        } else {\r\n      return false;\r\n    }\r\n  });\r\n};\r\nconst getTickets = () => {\r\n  console.log(\"00000000\");\r\n  request\r\n      .get(\"/parking/gate/getTickets\",\r\n      {\r\n        params: {\r\n          parkingKey:form.ticketsData.parkingkey,\r\n          parkingSecret:form.ticketsData.parkingsecret,\r\n          parkingCode: form.ticketsData.parkingcode,\r\n        },\r\n      })\r\n      .then((res) => {\r\n        var data=JSON.parse(res.data); \r\n        console.log(data);\r\n        console.log(data.data.recordList);\r\n        form.ticketsData.ticketList=   data.data.recordList              \r\n      });\r\n\r\n};\r\n  \r\n</script>\r\n<style lang=\"scss\" scoped>               \r\n\r\n// :deep(.el-form-item__label) {\r\n//     color: red;\r\n//   }\r\n.odd-row {\r\n\tbackground-color: rgb(219, 244, 252) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(208, 250, 202) !important;\r\n}\r\n\r\n</style>\r\n", "import script from \"./Gate.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./Gate.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./Gate.vue?vue&type=style&index=0&id=c46f44e6&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-c46f44e6\"]])\n\nexport default __exports__", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Gate.vue?vue&type=style&index=0&id=c46f44e6&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/Gate.32deb647.svg\";"], "sourceRoot": ""}