{"remainingRequest": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\RefuseReason.vue?vue&type=template&id=6c87a52d&scoped=true", "dependencies": [{"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\RefuseReason.vue", "mtime": 1720140646526}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "class", "_createElementVNode", "src", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_form", "inline", "model", "$setup", "query", "_component_el_form_item", "label", "_component_el_input", "reason", "$event", "placeholder", "clearable", "_component_el_button", "type", "icon", "onClick", "handleSearch", "handleAdd", "_component_el_table", "data", "tableData", "border", "ref", "cellStyle", "tableRowClassName", "_Fragment", "_renderList", "props", "item", "_createBlock", "_component_el_table_column", "prop", "key", "width", "align", "fixed", "default", "_withCtx", "scope", "handleEdit", "row", "id", "handleDelete", "$index", "_hoisted_5", "_component_el_pagination", "currentPage", "pageNum", "pageSize", "layout", "total", "pageTotal", "onSizeChange", "handleSizeChange", "onCurrentChange", "handlePageChange"], "sources": ["F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\RefuseReason.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/RefuseReason.svg\"></i> 拒绝原因\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"80px\">\r\n          <el-form-item label-width=\"80px\" label=\"拒绝原因\">\r\n            <el-input v-model=\"query.reason\" placeholder=\"拒绝原因\" class=\"handle-input mr10\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索\r\n          </el-button>\r\n          <el-button type=\"primary\" @click=\"handleAdd\">新增\r\n          </el-button>\r\n        </el-form>\r\n      </div>\r\n      <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n        :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n        <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\" v-for=\"item in props\"\r\n          :key=\"item.prop\">\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row.id)\">编辑\r\n            </el-button>\r\n            <el-button type=\"text\" icon=\"el-icon-delete\" class=\"red\"\r\n              @click=\"handleDelete(scope.$index, scope.row.id)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n          @current-change=\"handlePageChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive } from \"vue\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useRouter } from \"vue-router\";\r\nimport request from \"../../utils/request\";\r\n\r\nexport default {\r\n  name: \"RefuseReason\",\r\n  setup() {\r\n    const root = \"/parking/refusereason/\";\r\n    const router = useRouter();\r\n\r\n    const props = [\r\n      { label: \"拒绝原因\", prop: \"reason\" },\r\n      { label: \"序号\", prop: \"sortno\" },\r\n\r\n    ];\r\n\r\n    const query = reactive({\r\n      reason: \"\",\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n    });\r\n    const tableData = ref([]);\r\n    const pageTotal = ref(0);\r\n    // 获取表格数据\r\n    //指定行颜色\r\n    const tableRowClassName = ({ row, rowIndex }) => {\r\n      // console.log(rowIndex)\r\n      if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n      } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n      }\r\n    };\r\n    //指定行高\r\n    const cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n      let style = { padding: '0px 3px' }\r\n      return style\r\n    };\r\n    const getData = () => {\r\n      request\r\n        .get(root + \"page\", {\r\n          params: query,\r\n        })\r\n        .then((res) => {\r\n          tableData.value = res.data.records;\r\n          pageTotal.value = res.data.total;\r\n        });\r\n    };\r\n    getData();\r\n    // 查询操作\r\n    const handleSearch = () => {\r\n      query.pageNum = 1;\r\n      getData();\r\n    };\r\n    // 分页大小\r\n    const handleSizeChange = (val) => {\r\n      query.pageSize = val;\r\n      getData();\r\n    };\r\n    // 分页导航\r\n    const handlePageChange = (val) => {\r\n      query.pageNum = val;\r\n      getData();\r\n    };\r\n    // 删除操作\r\n    const handleDelete = (index, sid) => {\r\n      // 二次确认删除\r\n      ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          request.delete(root + sid).then((res) => {\r\n            if (res.data) {\r\n              ElMessage.success(\"删除成功\");\r\n              tableData.value.splice(index, 1);\r\n            } else {\r\n              ElMessage.error(\"删除失败\");\r\n            }\r\n          });\r\n        })\r\n        .catch(() => { });\r\n    };\r\n\r\n    // 新增操作\r\n    const handleAdd = () => {\r\n      router.push(\"/admin/parking/addRefuseReason\");\r\n    };\r\n\r\n    // 表格编辑时弹窗和保存\r\n    const editVisible = ref(false);\r\n    let form = reactive({\r\n      reason: \"\",\r\n      sortno: \"\",\r\n    });\r\n    const handleEdit = (id) => {\r\n      console.log(id)\r\n      router.push({ path: \"/admin/parking/addRefuseReason\", query: { id: id } });\r\n    };\r\n\r\n    return {\r\n      props,\r\n      query,\r\n      tableData,\r\n      pageTotal,\r\n      editVisible,\r\n      form,\r\n      handleSearch,\r\n      handleSizeChange,\r\n      handlePageChange,\r\n      handleAdd,\r\n      handleDelete,\r\n      handleEdit,\r\n      cellStyle,\r\n      tableRowClassName\r\n    };\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n  background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n  background-color: rgb(255, 255, 255) !important;\r\n}\r\n\r\n.handle-box {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.handle-select {\r\n  width: 120px;\r\n}\r\n\r\n.handle-input {\r\n  width: 300px;\r\n  display: inline-block;\r\n}\r\n\r\n.table {\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.red {\r\n  color: #ff0000;\r\n}\r\n\r\n.mr10 {\r\n  margin-right: 10px;\r\n}\r\n\r\n.table-td-thumb {\r\n  display: block;\r\n  margin: auto;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n</style>\r\n"], "mappings": ";OAKkBA,UAA6C;;;EAHtDC,KAAK,EAAC;AAAQ;gEAGbC,mBAAA,CAA0D,Y,aAAvDA,mBAAA,CAAmD;EAA9CC,GAA6C,EAA7CH;AAA6C,G;;EAItDC,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EA0BlBA,KAAK,EAAC;AAAY;;;;;;;;;;;uBAnC3BG,mBAAA,CA0CM,cAzCJF,mBAAA,CAMM,OANNG,UAMM,GALJC,YAAA,CAIgBC,wBAAA;IAJDC,SAAS,EAAC;EAAG;sBAC1B,MAEqB,CAFrBF,YAAA,CAEqBG,6BAAA;wBADnB,MAA0D,CAA1DC,UAA0D,E,iBAAA,QAC5D,E;;;;QAGJR,mBAAA,CAiCM,OAjCNS,UAiCM,GAhCJT,mBAAA,CAUM,OAVNU,UAUM,GATJN,YAAA,CAQUO,kBAAA;IARAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,KAAK;IAAEhB,KAAK,EAAC,kBAAkB;IAAC,aAAW,EAAC;;sBAC1E,MAEe,CAFfK,YAAA,CAEeY,uBAAA;MAFD,aAAW,EAAC,MAAM;MAACC,KAAK,EAAC;;wBACrC,MAAmG,CAAnGb,YAAA,CAAmGc,mBAAA;oBAAhFJ,MAAA,CAAAC,KAAK,CAACI,MAAM;mEAAZL,MAAA,CAAAC,KAAK,CAACI,MAAM,GAAAC,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACtB,KAAK,EAAC,mBAAmB;QAACuB,SAAS,EAAT;;;QAEhFlB,YAAA,CACYmB,oBAAA;MADDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,gBAAgB;MAAEC,OAAK,EAAEZ,MAAA,CAAAa;;wBAAc,MACtE,C,iBADsE,KACtE,E;;wBACAvB,YAAA,CACYmB,oBAAA;MADDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEZ,MAAA,CAAAc;;wBAAW,MAC7C,C,iBAD6C,KAC7C,E;;;;sBAGJxB,YAAA,CAcWyB,mBAAA;IAdAC,IAAI,EAAEhB,MAAA,CAAAiB,SAAS;IAAEC,MAAM,EAAN,EAAM;IAACjC,KAAK,EAAC,OAAO;IAACkC,GAAG,EAAC,eAAe;IAAC,wBAAsB,EAAC,cAAc;IACvG,YAAU,EAAEnB,MAAA,CAAAoB,SAAS;IAAG,gBAAc,EAAEpB,MAAA,CAAAqB;;sBAC4C,MAAqB,E,kBAA1GjC,mBAAA,CAEkBkC,SAAA,QAAAC,WAAA,CAFkFvB,MAAA,CAAAwB,KAAK,EAAbC,IAAI;2BAAhGC,YAAA,CAEkBC,0BAAA;QAFA,uBAAqB,EAAE,IAAI;QAAGC,IAAI,EAAEH,IAAI,CAACG,IAAI;QAAGzB,KAAK,EAAEsB,IAAI,CAACtB,KAAK;QAChF0B,GAAG,EAAEJ,IAAI,CAACG;;eAEbtC,YAAA,CAOkBqC,0BAAA;MAPDxB,KAAK,EAAC,IAAI;MAAC2B,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC,QAAQ;MAACC,KAAK,EAAC;;MAChDC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvB7C,YAAA,CACYmB,oBAAA;QADDC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,cAAc;QAAEC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAoC,UAAU,CAACD,KAAK,CAACE,GAAG,CAACC,EAAE;;0BAAG,MAC7E,C,iBAD6E,KAC7E,E;;6BACAhD,YAAA,CACkEmB,oBAAA;QADvDC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,gBAAgB;QAAC1B,KAAK,EAAC,KAAK;QACrD2B,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAuC,YAAY,CAACJ,KAAK,CAACK,MAAM,EAAEL,KAAK,CAACE,GAAG,CAACC,EAAE;;0BAAG,MAAE,C,iBAAF,IAAE,E;;;;;;mDAK5DpD,mBAAA,CAKM,OALNuD,UAKM,GAJJnD,YAAA,CAGgBoD,wBAAA;IAHAC,WAAW,EAAE3C,MAAA,CAAAC,KAAK,CAAC2C,OAAO;IAAG,YAAU,EAAE,YAAY;IAAG,WAAS,EAAE5C,MAAA,CAAAC,KAAK,CAAC4C,QAAQ;IAC/FC,MAAM,EAAC,yCAAyC;IAAEC,KAAK,EAAE/C,MAAA,CAAAgD,SAAS;IAAGC,YAAW,EAAEjD,MAAA,CAAAkD,gBAAgB;IACjGC,eAAc,EAAEnD,MAAA,CAAAoD"}]}