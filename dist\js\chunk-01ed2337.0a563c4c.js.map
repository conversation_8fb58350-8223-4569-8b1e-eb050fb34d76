{"version": 3, "sources": ["webpack:///./src/views/admin/VehicleReservation.vue?1e7c", "webpack:///./src/icons/svg-black/VehicleReservation.svg", "webpack:///./src/views/admin/VehicleReservation.vue", "webpack:///./src/views/admin/VehicleReservation.vue?51e3"], "names": ["module", "exports", "root", "form<PERSON>abe<PERSON><PERSON>", "useRouter", "useRoute", "useStore", "props", "label", "prop", "rules", "yardName", "required", "message", "trigger", "plateNumber", "merchantName", "notifierName", "appointmentTime", "remark", "form", "reactive", "data", "id", "yardCode", "channelName", "vehicleClassification", "releaseReason", "enterTime", "leaveTime", "reserveFlag", "formClean", "timeOutInterval", "cleanUp", "dialogFormVisible", "value", "ids", "selectTimeOutData", "map", "item", "console", "log", "request", "post", "then", "response", "code", "ElMessage", "success", "tableVisible", "error", "msg", "ref", "multipleTimeOutTable", "gridData", "tableRowClassName", "row", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding", "onReset", "applicantUserId", "localStorage", "getItem", "query", "pageNum", "pageSize", "tableData", "selectData", "pageTotal", "dialogVisible", "dialogVisibleUpdate", "dialogTableVisible", "timeOutCleanup", "selectTimeOutTables", "get", "params", "res", "handleBeforeClose", "resetTimeOut", "handleSelectionChange", "val", "getData", "records", "total", "handleSearch", "handleSizeChange", "handlePageChange", "handleReservation", "ElMessageBox", "confirm", "type", "catch", "display", "selectChanged", "delBatch", "handleAdd", "handleEdit", "yardNameList", "channelNameList", "vehicleClassificationList", "merchantNameList", "releaseReasonList", "notifierNameList", "appointmentTimeList", "changeYardName", "date", "Date", "formattedDate", "formatDate", "changeMerchantName", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "convertToUpperCase", "toUpperCase", "trim", "replace", "formRef", "save", "length", "alert", "validate", "valid", "url", "method", "carCode", "parkCodeList", "count", "confirmButtonText", "cancelButtonText", "center", "resetFields", "update", "test", "chineseCharacters", "match", "__exports__"], "mappings": "kHAAA,W,qBCAAA,EAAOC,QAAU,IAA0B,uC,6fCyNrCC,EAAO,+BAsHPC,EAAiB,Q,4CArHRC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CAEV,CAAEC,MAAO,OAAQC,KAAM,YAEvB,CAAED,MAAO,OAAQC,KAAM,eAEvB,CAAED,MAAO,OAAQC,KAAM,gBACvB,CAAED,MAAO,QAASC,KAAM,gBACxB,CAAED,MAAO,OAAQC,KAAM,mBAGvB,CAAED,MAAO,KAAMC,KAAM,UAIrB,CAAED,MAAO,OAAQC,KAAM,eAYrBC,EAAQ,CACVC,SAAU,CACN,CACIC,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBC,YAAa,CACT,CACIH,UAAU,EACVC,QAAS,SACTC,QAAS,SAGjBE,aAAc,CACV,CACIJ,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBG,aAAc,CACV,CACIL,UAAU,EACVC,QAAS,WACTC,QAAS,WAGjBI,gBAAiB,CACb,CACIN,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBK,OAAQ,CACJ,CACIP,UAAU,EACVC,QAAS,UACTC,QAAS,UAIfM,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJC,SAAU,GACVb,SAAU,GACVc,YAAa,GACbV,YAAa,GACbW,sBAAuB,GACvBV,aAAc,GACdW,cAAe,GACfV,aAAc,GACdW,UAAW,GACXC,UAAW,GACXV,OAAQ,GAERD,gBAAiB,GACjBY,aAAc,KAIhBC,EAAYV,sBAAS,CACvBW,gBAAiB,KAEfC,EAAUA,KACZC,EAAkBC,OAAQ,EAE1B,MAAMC,EAAMC,EAAkBF,MAAMG,IAAIC,GAAQA,EAAKhB,IACrDiB,QAAQC,IAAIL,GACZM,OAAQC,KAAK,0CAA2CP,GACnDQ,KAAKC,IACFL,QAAQC,IAAII,GACS,GAAjBA,EAASC,MACTC,OAAUC,QAAQ,aAElBC,EAAad,OAAQ,EACrBJ,EAAUC,gBAAkB,IAE5Be,OAAUG,MAAML,EAASM,QAInCjB,EAAoBkB,kBAAI,GACxBC,EAAuBD,iBAAI,MAE3BE,EAAWF,iBAAI,IAEfG,EAAoBA,EAAGC,MAAKC,eAEzBA,EAAW,GAAK,GAAK,GACtBjB,QAAQC,IAAIgB,GACL,YACCA,EAAW,GAAK,GAAK,GAC7BjB,QAAQC,IAAIgB,GACL,iBAFJ,EAMLC,EAAYA,EAAGF,MAAKG,SAAQF,WAAUG,kBACxC,IAAIC,EAAQ,CAAEC,QAAS,WACvB,OAAOD,GAGLE,EAAUA,KACZ3C,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKE,SAAW,GACrBJ,EAAKE,KAAKX,SAAW,GACrBS,EAAKE,KAAKG,YAAc,GACxBL,EAAKE,KAAKP,YAAc,GACxBK,EAAKE,KAAKI,sBAAwB,GAClCN,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKH,OAAS,IAKjB6C,GAHWZ,kBAAI,GACLA,iBAAI,IAEIA,iBAAI,KAC5BY,EAAgB7B,MAAQ8B,aAAaC,QAAQ,UAE7C,MAAMC,EAAQ9C,sBAAS,CACnBN,YAAa,GACbJ,SAAU,GACVyD,QAAS,EACTC,SAAU,KAERC,EAAYlB,iBAAI,IAChBmB,EAAanB,iBAAI,IACjBf,EAAoBe,iBAAI,IACxBoB,EAAYpB,iBAAI,GAEhBqB,GADSR,aAAaC,QAAQ,UACdd,kBAAI,IACpBsB,EAAsBtB,kBAAI,GAC1BuB,EAAqBvB,kBAAI,GACzBH,EAAeG,kBAAI,GAEnBwB,EAAiBA,KACnBD,EAAmBxC,OAAQ,GAEzB0C,EAAsBA,KACxB5B,EAAad,OAAQ,EAGrBO,OACKoC,IAAI5E,EAAO,iBAAkB,CAC1B6E,OAAQ,CACJ/C,gBAAiBD,EAAUC,mBAGlCY,KAAMoC,IACH1B,EAASnB,MAAQ6C,EAAI1D,KAErBkB,QAAQC,IAAIa,EAASnB,UAG3B8C,EAAoBA,KACtBlD,EAAUC,gBAAkB,GAC5B2C,EAAmBxC,OAAQ,EAC3Bc,EAAad,OAAQ,GAEnB+C,EAAeA,KACjBnD,EAAUC,gBAAkB,GAC5BiB,EAAad,OAAQ,GAEnBgD,EAAyBC,IAE3B/C,EAAkBF,MAAQiD,EAC1B5C,QAAQC,IAAIJ,EAAkBF,QAG5BkD,EAAUA,KACZ3C,OACKoC,IAAI5E,EAAO,OAAQ,CAChB6E,OAAQZ,IAEXvB,KAAMoC,IACHV,EAAUnC,MAAQ6C,EAAI1D,KAAKgE,QAC3Bd,EAAUrC,MAAQ6C,EAAI1D,KAAKiE,MAC3B/C,QAAQC,IAAIuC,EAAI1D,SAG5B+D,IAEA,MAAMG,EAAeA,KACjBrB,EAAMC,QAAU,EAChBiB,KAGEI,EAAoBL,IACtBjB,EAAME,SAAWe,EACjBC,KAGEK,EAAoBN,IACtBjB,EAAMC,QAAUgB,EAChBC,KAwBEM,EAAqBnC,IAEvBoC,OAAaC,QAAQ,iBAAkB,KAAM,CACzCC,KAAM,YAELlD,KAAK,KACFF,OAAQC,KAAK,6CAA8Ca,GAAKZ,KAAMoC,IAC7DA,EAAI1D,KAAKA,KAIVyB,OAAUG,MAAM,WAHhBH,OAAUC,QAAQ,UAClBqC,SAMXU,MAAM,SAGTC,EAAUA,KACZvB,EAActC,OAAQ,EACtBf,EAAKE,KAAKJ,gBAAkB,IAE1B+E,EAAiBb,IACnBb,EAAWpC,MAAQiD,EACnB5C,QAAQC,IAAI8B,EAAWpC,QAGrB+D,EAAWA,KACb,MAAM9D,EAAMmC,EAAWpC,MAAMG,IAAIC,GAAQA,EAAKhB,IAC9CiB,QAAQC,IAAIL,GAEZM,OAAQC,KAAK,0CAA2CP,GACnDQ,KAAKC,IACFL,QAAQC,IAAII,GACS,GAAjBA,EAASC,MACTC,OAAUC,QAAQ,WAElBqC,KAEAtC,OAAUG,MAAML,EAASM,QAMnCgD,EAAYA,KACdpC,IACAU,EAActC,OAAQ,GAIpBiE,GADchD,kBAAI,GACJI,IAChBhB,QAAQC,IAAIe,GACZkB,EAAoBvC,OAAQ,EAC5Bf,EAAKE,KAAKC,GAAKiC,EAAIjC,GACnBH,EAAKE,KAAKE,SAAWgC,EAAIhC,SACzBJ,EAAKE,KAAKX,SAAW6C,EAAI7C,SACzBS,EAAKE,KAAKG,YAAc+B,EAAI/B,YAC5BL,EAAKE,KAAKP,YAAcyC,EAAIzC,YAC5BK,EAAKE,KAAKI,sBAAwB8B,EAAI9B,sBACtCN,EAAKE,KAAKN,aAAewC,EAAIxC,aAC7BI,EAAKE,KAAKK,cAAgB6B,EAAI7B,cAC9BP,EAAKE,KAAKL,aAAeuC,EAAIvC,aAC7BG,EAAKE,KAAKJ,gBAAkBsC,EAAItC,gBAChCE,EAAKE,KAAKH,OAASqC,EAAIrC,SAGrBkF,GADejD,iBAAI,IACJA,iBAAI,KACnBkD,GAAkBlD,iBAAI,IACtBmD,GAA4BnD,iBAAI,IAChCoD,GAAmBpD,iBAAI,IACvBqD,GAAoBrD,iBAAI,IACxBsD,GAAmBtD,iBAAI,IACvBuD,GAAsBvD,iBAAI,IAChCV,OAAQoC,IAAI,8BAA8BlC,KAAMoC,IAC5CxC,QAAQC,IAAIuC,EAAI1D,MAChB+E,EAAalE,MAAQ6C,EAAI1D,OAE7BoB,OAAQoC,IAAI,wDAAwDlC,KAC/DoC,IACGuB,GAA0BpE,MAAQ6C,EAAI1D,OAE9CoB,OAAQoC,IAAI,sCAAsClC,KAC7CoC,IACGwB,GAAiBrE,MAAQ6C,EAAI1D,OAErCoB,OAAQoC,IAAI,wCAAwClC,KAC/CoC,IACGyB,GAAkBtE,MAAQ6C,EAAI1D,OAEtC,MAAMsF,GAAiBA,KACnBpE,QAAQC,IAAIrB,EAAKE,KAAKE,UACtBkB,OACKoC,IAAI,6BACD,CACIC,OAAQ,CACJpE,SAAUS,EAAKE,KAAKX,YAG/BiC,KAAMoC,IACHxC,QAAQC,IAAIuC,EAAI1D,KAAK,IACrBF,EAAKE,KAAKG,YAAc,GACxBL,EAAKE,KAAKI,sBAAwB,GAClCN,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKE,SAAWwD,EAAI1D,KAAK,GAC9BoB,OACKoC,IAAI,iDACD,CACIC,OAAQ,CACJvD,SAAUwD,EAAI1D,KAAK,MAG9BsB,KAAMoC,IACHxC,QAAQC,IAAI,SAAUrB,EAAKE,KAAKE,UAChCJ,EAAKE,KAAKI,sBAAwB,GAClC,MAAMmF,EAAO,IAAIC,KACXC,EAAgBC,GAAWH,GACjCzF,EAAKE,KAAKJ,gBAAkB6F,EAEH,QAAtB3F,EAAKE,KAAKX,SACTS,EAAKE,KAAKN,aAAe,OACI,QAAtBI,EAAKE,KAAKX,SACjBS,EAAKE,KAAKN,aAAe,UAEzBI,EAAKE,KAAKN,aAAeI,EAAKE,KAAKX,SAEvC+B,OACKoC,IAAI,qCACD,CACIC,OAAQ,CACJ/D,aAAcI,EAAKE,KAAKN,gBAGnC4B,KAAMoC,IACH5D,EAAKE,KAAKK,cAAgB,GAC1B+E,GAAiBvE,MAAQ6C,EAAI1D,KAE7BF,EAAKE,KAAKL,aAAe+D,EAAI1D,KAAK,GAAGL,eAE7CG,EAAKE,KAAKK,cAAgB,GAC1BP,EAAKE,KAAKH,OAAS,KACnBmF,GAAgBnE,MAAQ6C,EAAI1D,UAI1C2F,GAAqBA,KACvBvE,OACKoC,IAAI,qCACD,CACIC,OAAQ,CACJ/D,aAAcI,EAAKE,KAAKN,gBAGnC4B,KAAMoC,IACH5D,EAAKE,KAAKL,aAAe,GACzBG,EAAKE,KAAKK,cAAgB,GAC1B+E,GAAiBvE,MAAQ6C,EAAI1D,QAGnC0F,GAAcH,IAChB,MAAMK,EAAOL,EAAKM,cACZC,EAAQC,OAAOR,EAAKS,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOR,EAAKY,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOR,EAAKc,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOR,EAAKgB,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOR,EAAKkB,cAAcR,SAAS,EAAG,KACtD,MAAQ,GAAEL,KAAQE,KAASI,KAAOE,KAASE,KAAWE,KAEpDE,GAAqBA,KACvB5G,EAAKE,KAAKP,YAAcK,EAAKE,KAAKP,YAAYkH,cAC9C7G,EAAKE,KAAKP,YAAcK,EAAKE,KAAKP,YAAYmH,OAC9C9G,EAAKE,KAAKP,YAAcK,EAAKE,KAAKP,YAAYoH,QAAQ,OAAQ,KAE5DC,GAAUhF,iBAAI,MAGdiF,GAAOA,KACT,GAAIjH,EAAKE,KAAKP,YAAYuH,OAAS,GAAKlH,EAAKE,KAAKP,YAAYuH,OAAS,EAGnE,OAFAC,MAAM,oBACNnH,EAAKE,KAAKP,YAAc,IAG5BqH,GAAQjG,MAAMqG,SAAUC,IACpB,IAAIA,EAqGA,OAAO,EAnGP/F,eAAQ,CACJgG,IAAK,kCACLC,OAAQ,MACR5D,OAAQ,CACJ6D,QAASxH,EAAKE,KAAKP,YACnB8H,aAAczH,EAAKE,KAAKE,YAE7BoB,KAAMoC,IACLxC,QAAQC,IAAI,KAAOuC,EAAI1D,KAAKA,KAAKwH,OAEN,GAAvB9D,EAAI1D,KAAKA,KAAKwH,MACdlD,OAAaC,QACT,qBACA,CACIkD,kBAAmB,MACnBC,iBAAkB,MAClBlD,KAAM,UACNmD,QAAQ,IACTrG,KAAK,KACJF,eAAQ,CACJgG,IAAK,qCACLC,OAAQ,OACRrH,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBb,SAAUS,EAAKE,KAAKX,SACpBc,YAAaL,EAAKE,KAAKG,YACvBV,YAAaK,EAAKE,KAAKP,YACvBW,sBAAuBN,EAAKE,KAAKI,sBACjCV,aAAcI,EAAKE,KAAKN,aACxBW,cAAeP,EAAKE,KAAKK,cACzBV,aAAcG,EAAKE,KAAKL,aACxBC,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvByB,KAAMoC,IACgB,GAAjBA,EAAI1D,KAAKwB,MACT1B,EAAKE,KAAO,GACZ+D,IACAtC,OAAUC,QAAQ,SAClBoF,GAAQjG,MAAM+G,cAEdzE,EAActC,OAAQ,IAEtBsC,EAActC,OAAQ,EACtBY,OAAUG,MAAM8B,EAAI1D,KAAK6B,KACzBiF,GAAQjG,MAAM+G,mBAIzBnD,MAAM,KACHhD,eAAU,CACN+C,KAAM,QACNjF,QAAS,aAIrB2B,QAAQC,IAAI,SACZD,QAAQC,IAAIrB,EAAKE,KAAKL,cACtByB,eAAQ,CACJgG,IAAK,qCACLC,OAAQ,OACRrH,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBb,SAAUS,EAAKE,KAAKX,SACpBc,YAAaL,EAAKE,KAAKG,YACvBV,YAAaK,EAAKE,KAAKP,YACvBW,sBAAuBN,EAAKE,KAAKI,sBACjCV,aAAcI,EAAKE,KAAKN,aACxBW,cAAeP,EAAKE,KAAKK,cACzBV,aAAcG,EAAKE,KAAKL,aACxBC,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvByB,KAAMoC,IACLxC,QAAQC,IAAIrB,EAAKE,KAAKL,cAED,GAAjB+D,EAAI1D,KAAKwB,MACT1B,EAAKE,KAAO,GACZ+D,IACAtC,OAAUC,QAAQ,SAClBoF,GAAQjG,MAAM+G,cAEdzE,EAActC,OAAQ,IAEtBsC,EAActC,OAAQ,EACtBY,OAAUG,MAAM8B,EAAI1D,KAAK6B,KACzBiF,GAAQjG,MAAM+G,iBAGtB9H,EAAKE,KAAO,GACZ+D,IACA+C,GAAQjG,MAAM+G,cAEdzE,EAActC,OAAQ,QAepCgH,GAASA,KACX,GAAI/H,EAAKE,KAAKP,YAAYuH,OAAS,GAAKlH,EAAKE,KAAKP,YAAYuH,OAAS,EAGnE,OAFAC,MAAM,oBACNnH,EAAKE,KAAKP,YAAc,IAErB,GAAI,kBAAkBqI,KAAKhI,EAAKE,KAAKP,aAAc,CAEtD,MAAMsI,EAAoBjI,EAAKE,KAAKP,YAAYuI,MAAM,oBACtD,GAAID,GAAqBA,EAAkBf,OAAS,EAGhD,YADAlH,EAAKE,KAAKP,YAAc,IAKhCqH,GAAQjG,MAAMqG,SAAUC,IACpB,IAAIA,EAiCA,OAAO,EAhCP/F,eAAQ,CACJgG,IAAK,qCACLC,OAAQ,OACRrH,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdC,SAAUJ,EAAKE,KAAKE,SACpBb,SAAUS,EAAKE,KAAKX,SACpBc,YAAaL,EAAKE,KAAKG,YACvBV,YAAaK,EAAKE,KAAKP,YACvBW,sBAAuBN,EAAKE,KAAKI,sBACjCV,aAAcI,EAAKE,KAAKN,aACxBW,cAAeP,EAAKE,KAAKK,cACzBV,aAAcG,EAAKE,KAAKL,aACxBC,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvByB,KAAMoC,IACLxC,QAAQC,IAAI,QACZD,QAAQC,IAAIuC,GACZxC,QAAQC,IAAIuC,EAAI1D,MAChBF,EAAKE,KAAO,GACS,GAAjB0D,EAAI1D,KAAKwB,MACTuC,IACAtC,OAAUC,QAAQ,SAElB0B,EAAoBvC,OAAQ,IAE5BuC,EAAoBvC,OAAQ,EAC5BY,OAAUG,MAAM8B,EAAI1D,KAAK6B,W,qsdCvyB7C,MAAMoG,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB", "file": "js/chunk-01ed2337.0a563c4c.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./VehicleReservation.vue?vue&type=style&index=0&id=0f4855e6&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/VehicleReservation.63e19717.svg\";", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/VehicleReservation.svg\"></i> 外来车辆信息管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                        <el-input v-model=\"query.yardName\" placeholder=\"车场名称\" class=\"handle-input mr10\" clearable\r\n                            style=\"width: 200px;\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"车牌号码\">\r\n                        <el-input v-model=\"query.plateNumber\" placeholder=\"车牌号码\" class=\"handle-input mr10\" clearable\r\n                            style=\"width: 150px;\"></el-input>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n                        style=\"margin-left: 20px;\">搜 索\r\n                    </el-button>\r\n                    <el-button type=\"primary\" class=\"addButton\" icon=\"el-icon-circle-plus\" @click=\"handleAdd\"\r\n                        style=\"margin-left: 20px;\">新增预约\r\n                    </el-button>\r\n                    <el-button type=\"danger\" class=\"addButton\" icon=\"el-icon-remove\" @click=\"delBatch()\"\r\n                        style=\"margin-left: 20px;\">批量删除\r\n                    </el-button>\r\n                    <el-button type=\"success\" class=\"addButton\" icon=\"el-icon-time\" @click=\"timeOutCleanup()\"\r\n                        style=\"margin-left: 20px;\">超时清理</el-button>\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\" @selection-change=\"selectChanged\">\r\n                <el-table-column type=\"selection\" width=\"55px\"> </el-table-column>\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in props\" :key=\"item.prop\" align=\"center\" width=\"200px\" height=\"10px\">\r\n                </el-table-column>\r\n                <el-table-column label=\"入场状态\" prop=\"reserveFlag\" align=\"center\" width=\"200px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"danger\" v-if=\"scope.row.reserveFlag === 0\" effect=\"dark\" size=\"large\">未入场\r\n                        </el-tag>\r\n                        <el-tag type=\"success\" v-else-if=\"scope.row.reserveFlag === 1\" effect=\"dark\" size=\"large\">已入场\r\n                        </el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"280px\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\">编辑\r\n                        </el-button>\r\n                        <el-button type=\"text\" icon=\"el-icon-position\" @click=\"handleReservation(scope.row)\">添加入场\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"添加外来车辆预约信息\" v-model=\"dialogVisible\" width=\"48%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"form.data.yardCode\" disabled></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 30%\"\r\n                            @input=\"convertToUpperCase\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\"\r\n                                :label=\"item.merchantName\" :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\"\r\n                                :label=\"item.notifierName\" :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"预约时间\" prop=\"appointmentTime\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 70%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"display()\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"修改外来车辆预约信息\" v-model=\"dialogVisibleUpdate\" width=\"48%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"form.data.yardCode\" disabled></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 30%\"\r\n                            @input=\"convertToUpperCase\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\"\r\n                                :label=\"item.merchantName\" :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\"\r\n                                :label=\"item.notifierName\" :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"预约时间\" prop=\"appointmentTime\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 70%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisibleUpdate = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"update\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n\r\n        </div>\r\n        <div>\r\n            <el-dialog v-model=\"dialogTableVisible\" title=\"预约超时车辆删除\" :before-close=\"handleBeforeClose\">\r\n                <el-form :model=\"formClean\">\r\n                    <el-form-item label=\"车辆超时时间\" :label-width=\"formLabelWidth\" size=\"large\">\r\n                        <el-select v-model=\"formClean.timeOutInterval\" placeholder=\"请选择预约车辆超时时间\"\r\n                            style=\"margin-left: 20px; width: 250px;\">\r\n                            <el-option label=\"6小时\" value=\"6\" />\r\n                            <el-option label=\"9小时\" value=\"9\" />\r\n                            <el-option label=\"12小时\" value=\"12\" />\r\n                            <el-option label=\"24小时\" value=\"24\" />\r\n                            <el-option label=\"36小时\" value=\"36\" />\r\n                            <el-option label=\"48小时\" value=\"48\" />\r\n                        </el-select>\r\n                        <el-button type=\"primary\" @click=\"selectTimeOutTables()\"\r\n                            style=\"width: 90px; margin-left: 50px;\">查\r\n                            询</el-button>\r\n                        <el-button type=\"danger\" @click=\"resetTimeOut()\" style=\"width: 90px; margin-left: 20px;\">重\r\n                            置</el-button>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer v-if=\"tableVisible == true\">\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"tableVisible = false\">取消删除</el-button>\r\n                        <el-button type=\"primary\" @click=\"cleanUp()\"\r\n                            style=\"margin-left: 35px; margin-right: 20px;\">确认删除</el-button>\r\n                    </span>\r\n                </template>\r\n                <el-table :data=\"gridData\" v-if=\"tableVisible == true\" @selection-change=\"handleSelectionChange\"\r\n                    ref=\"multipleTimeOutTable\">\r\n                    <el-table-column type=\"selection\" width=\"55px\"> </el-table-column>\r\n                    <el-table-column property=\"yardName\" label=\"车场名称\" width=\"150\" />\r\n                    <el-table-column property=\"plateNumber\" label=\"车牌号码\" />\r\n                    <el-table-column property=\"appointmentTime\" label=\"预约时间\" />\r\n                    <el-table-column property=\"timeOutInterval\" label=\"超时时间\" />\r\n                </el-table>\r\n            </el-dialog>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\n\r\nconst root = \"/parking/vehicleReservation/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    // { label: \"车场编码\", prop: \"yardCode\" },\r\n    { label: \"车场名称\", prop: \"yardName\" },\r\n    // { label: \"入场通道\", prop: \"channelName\" },\r\n    { label: \"车牌号码\", prop: \"plateNumber\" },\r\n    // { label: \"车辆分类\", prop: \"vehicleClassification\" },\r\n    { label: \"商户名称\", prop: \"merchantName\" },\r\n    { label: \"通知人姓名\", prop: \"notifierName\" },\r\n    { label: \"预约时间\", prop: \"appointmentTime\" },\r\n    // { label: \"进场时间\", prop: \"enterTime\" },\r\n    // { label: \"离场时间\", prop: \"leaveTime\" },\r\n    { label: \"备注\", prop: \"remark\" },\r\n    // { label: \"进场车辆类型\", prop: \"enterVipType\" },\r\n    // { label: \"进场类型\", prop: \"enterType\" },\r\n    // { label: \"创建时间\", prop: \"createTime\" },\r\n    { label: \"修改时间\", prop: \"updateTime\" },\r\n];\r\nconst button1 = [\r\n    { type: 'success', text: 'success' }\r\n]\r\nconst button2 = [\r\n    { type: 'primary', text: 'primary' },\r\n    { type: 'success', text: 'success' },\r\n    { type: 'info', text: 'info' },\r\n    { type: 'warning', text: 'warning' },\r\n    { type: 'danger', text: 'danger' },\r\n]\r\nconst rules = {\r\n    yardName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择车场名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    plateNumber: [\r\n        {\r\n            required: true,\r\n            message: \"请输入车牌号\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    merchantName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择商户名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    notifierName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择通知人姓名\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    appointmentTime: [\r\n        {\r\n            required: true,\r\n            message: \"请选择预约时间\",\r\n            trigger: \"change\"\r\n        },\r\n    ],\r\n    remark: [\r\n        {\r\n            required: true,\r\n            message: \"请输入备注信息\",\r\n            trigger: \"blur\"\r\n        },\r\n    ],\r\n};\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        yardCode: '',\r\n        yardName: '',\r\n        channelName: '',\r\n        plateNumber: '',\r\n        vehicleClassification: '',\r\n        merchantName: '',\r\n        releaseReason: '',\r\n        notifierName: '',\r\n        enterTime: '',\r\n        leaveTime: '',\r\n        remark: '',\r\n        // appointmentFlag: -1,\r\n        appointmentTime: \"\",\r\n        reserveFlag: -1\r\n    },\r\n\r\n});\r\nconst formClean = reactive({\r\n    timeOutInterval: ''\r\n});\r\nconst cleanUp = () => {\r\n    dialogFormVisible.value = false\r\n    // TODO 删除所选择的数据\r\n    const ids = selectTimeOutData.value.map(item => item.id);\r\n    console.log(ids)\r\n    request.post('/parking/vehicleReservation/batchDelete', ids)\r\n        .then(response => {\r\n            console.log(response)\r\n            if (response.code == 0) {\r\n                ElMessage.success('超时信息删除成功!');\r\n                // 重新加载数据\r\n                tableVisible.value = false;\r\n                formClean.timeOutInterval = '';\r\n            } else {\r\n                ElMessage.error(response.msg);\r\n            }\r\n        })\r\n};\r\nconst dialogFormVisible = ref(false)\r\nconst multipleTimeOutTable = ref(null)\r\nconst formLabelWidth = '100px'\r\nconst gridData = ref([]);\r\n//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n    let style = { padding: '0px 3px' }\r\n    return style\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.yardCode = ''\r\n    form.data.yardName = ''\r\n    form.data.channelName = ''\r\n    form.data.plateNumber = ''\r\n    form.data.vehicleClassification = ''\r\n    form.data.merchantName = ''\r\n    form.data.releaseReason = ''\r\n    form.data.notifierName = ''\r\n    form.data.remark = ''\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\n\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n\r\nconst query = reactive({\r\n    plateNumber: \"\",\r\n    yardName: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10\r\n});\r\nconst tableData = ref([]);\r\nconst selectData = ref([]);\r\nconst selectTimeOutData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\nconst dialogVisibleUpdate = ref(false)\r\nconst dialogTableVisible = ref(false);\r\nconst tableVisible = ref(false);\r\n// 超时清理按钮\r\nconst timeOutCleanup = () => {\r\n    dialogTableVisible.value = true;\r\n};\r\nconst selectTimeOutTables = () => {\r\n    tableVisible.value = true;\r\n    //TODO 调用查询接口\r\n    // multipleTimeOutTable.value.toggleAllSelection;\r\n    request\r\n        .get(root + \"timeOutCleanUp\", {\r\n            params: {\r\n                timeOutInterval: formClean.timeOutInterval\r\n            },\r\n        })\r\n        .then((res) => {\r\n            gridData.value = res.data;\r\n            // pageTotal.value = res.data.total;\r\n            console.log(gridData.value);\r\n        });\r\n}\r\nconst handleBeforeClose = () => {\r\n    formClean.timeOutInterval = '';\r\n    dialogTableVisible.value = false;\r\n    tableVisible.value = false;\r\n};\r\nconst resetTimeOut = () => {\r\n    formClean.timeOutInterval = '';\r\n    tableVisible.value = false;\r\n};\r\nconst handleSelectionChange = (val) => {\r\n    // 选择的数据\r\n    selectTimeOutData.value = val;\r\n    console.log(selectTimeOutData.value)\r\n};\r\n// 获取表格数据\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"page\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n            console.log(res.data);\r\n        });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\n// const handleDelete = (index, sid) => {\r\n//     // 二次确认删除\r\n//     ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n//         type: \"warning\",\r\n//     })\r\n//         .then(() => {\r\n//             request.delete(root + sid).then((res) => {\r\n//                 if (res.data) {\r\n//                     ElMessage.success(\"删除成功\");\r\n//                     query.pageNum = 1;\r\n//                     getData();\r\n//                     tableData.value.splice(index, 1);\r\n//                 } else {\r\n//                     ElMessage.error(\"删除失败\");\r\n//                 }\r\n//             });\r\n//         })\r\n//         .catch(() => {\r\n//         });\r\n// };\r\n//添加入场操作 添加入场弹窗打开\r\nconst handleReservation = (row) => {\r\n    // 二次确认添加入场\r\n    ElMessageBox.confirm(\"确定要将此条数据添加入场吗？\", \"提示\", {\r\n        type: \"success\",\r\n    })\r\n        .then(() => {\r\n            request.post(\"/parking/vehicleReservation/addReservation\", row).then((res) => {\r\n                if (!res.data.data) {\r\n                    ElMessage.success(\"添加入场成功\");\r\n                    getData();\r\n                } else {\r\n                    ElMessage.error(\"添加入场失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\nconst display = () => {\r\n    dialogVisible.value = false;\r\n    form.data.appointmentTime = ''\r\n}\r\nconst selectChanged = (val) => {\r\n    selectData.value = val;\r\n    console.log(selectData.value)\r\n    // 将selectData.value添加到数组中\r\n};\r\nconst delBatch = () => {\r\n    const ids = selectData.value.map(item => item.id);\r\n    console.log(ids)\r\n    //     const ids = selectData.value.map(item => item.id);\r\n    request.post('/parking/vehicleReservation/batchDelete', ids)\r\n        .then(response => {\r\n            console.log(response)\r\n            if (response.code == 0) {\r\n                ElMessage.success('批量删除成功!');\r\n                // 重新加载数据\r\n                getData();\r\n            } else {\r\n                ElMessage.error(response.msg);\r\n            }\r\n        })\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n    onReset();\r\n    dialogVisible.value = true;\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n    console.log(row)\r\n    dialogVisibleUpdate.value = true\r\n    form.data.id = row.id\r\n    form.data.yardCode = row.yardCode\r\n    form.data.yardName = row.yardName\r\n    form.data.channelName = row.channelName\r\n    form.data.plateNumber = row.plateNumber\r\n    form.data.vehicleClassification = row.vehicleClassification\r\n    form.data.merchantName = row.merchantName\r\n    form.data.releaseReason = row.releaseReason\r\n    form.data.notifierName = row.notifierName\r\n    form.data.appointmentTime = row.appointmentTime\r\n    form.data.remark = row.remark\r\n};\r\nconst yardCodeList = ref([]);\r\nconst yardNameList = ref([]);\r\nconst channelNameList = ref([]);\r\nconst vehicleClassificationList = ref([]);\r\nconst merchantNameList = ref([]);\r\nconst releaseReasonList = ref([]);\r\nconst notifierNameList = ref([]);\r\nconst appointmentTimeList = ref([]);\r\nrequest.get(\"/parking/yardInfo/yardName\").then((res) => {\r\n    console.log(res.data)\r\n    yardNameList.value = res.data;\r\n});\r\nrequest.get(\"/parking/vehicleClassification/vehicleClassification\").then(\r\n    (res) => {\r\n        vehicleClassificationList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/notifierInfo/merchantName\").then(\r\n    (res) => {\r\n        merchantNameList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/releaseReason/releaseReason\").then(\r\n    (res) => {\r\n        releaseReasonList.value = res.data;\r\n    });\r\nconst changeYardName = () => {\r\n    console.log(form.data.yardCode);\r\n    request\r\n        .get(\"/parking/yardInfo/yardCode\",\r\n            {\r\n                params: {\r\n                    yardName: form.data.yardName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            console.log(res.data[0])\r\n            form.data.channelName = \"\";\r\n            form.data.vehicleClassification = \"\";\r\n            form.data.notifierName = \"\";\r\n            form.data.merchantName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            form.data.yardCode = res.data[0]\r\n            request\r\n                .get(\"/parking/vehicleReservation/aikeGetChannelInfo\",\r\n                    {\r\n                        params: {\r\n                            yardCode: res.data[0]\r\n                        },\r\n                    })\r\n                .then((res) => {\r\n                    console.log(\"传递的参数为\", form.data.yardCode)\r\n                    form.data.vehicleClassification = \"\";\r\n                    const date = new Date();\r\n                    const formattedDate = formatDate(date);\r\n                    form.data.appointmentTime = formattedDate;\r\n                    // form.data.notifierName = \"\";\r\n                    if(form.data.yardName == '四季上东') {\r\n                        form.data.merchantName = \"四季一期\";\r\n                    } else if (form.data.yardName == '爱建锦园') {\r\n                        form.data.merchantName = \"爱建锦园3号场\";\r\n                    }else {\r\n                        form.data.merchantName = form.data.yardName;\r\n                    }\r\n                    request\r\n                        .get(\"/parking/notifierInfo/notifierName\",\r\n                            {\r\n                                params: {\r\n                                    merchantName: form.data.merchantName\r\n                                },\r\n                            })\r\n                        .then((res) => {\r\n                            form.data.releaseReason = \"\";\r\n                            notifierNameList.value = res.data;\r\n                            // console.log(res.data[0].notifierName)\r\n                            form.data.notifierName = res.data[0].notifierName;\r\n                        });\r\n                    form.data.releaseReason = \"\";\r\n                    form.data.remark = \"放行\";\r\n                    channelNameList.value = res.data\r\n                });\r\n        });\r\n};\r\nconst changeMerchantName = () => {\r\n    request\r\n        .get(\"/parking/notifierInfo/notifierName\",\r\n            {\r\n                params: {\r\n                    merchantName: form.data.merchantName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.notifierName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            notifierNameList.value = res.data;\r\n        });\r\n};\r\nconst formatDate = (date) => {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    const seconds = String(date.getSeconds()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n}\r\nconst convertToUpperCase = () => {\r\n    form.data.plateNumber = form.data.plateNumber.toUpperCase();\r\n    form.data.plateNumber = form.data.plateNumber.trim();\r\n    form.data.plateNumber = form.data.plateNumber.replace(/\\s+/g, '');\r\n};\r\nconst formRef = ref(null);\r\n// 校验车牌号的正则表达式（排除字母 \"O\" 和 \"I\"）\r\n\r\nconst save = () => {\r\n    if (form.data.plateNumber.length < 7 || form.data.plateNumber.length > 8) {\r\n        alert('输入长度必须为7-8位');\r\n        form.data.plateNumber = \"\";\r\n        return;\r\n    }\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            //调用AKE查询黑名单车辆\r\n            request({\r\n                url: \"/parking/blackList/getParkBlack\",\r\n                method: \"GET\",\r\n                params: {\r\n                    carCode: form.data.plateNumber,\r\n                    parkCodeList: form.data.yardCode\r\n                },\r\n            }).then((res) => {\r\n                console.log(\"测试\" + res.data.data.count)\r\n                // 根据响应的数据进行提示用户是否需要确认添加\r\n                if (res.data.data.count != 0) {\r\n                    ElMessageBox.confirm(\r\n                        '当前车辆为黑名单车辆，是否需要添加?',\r\n                        {\r\n                            confirmButtonText: '确 定',\r\n                            cancelButtonText: '取 消',\r\n                            type: 'warning',\r\n                            center: true,\r\n                        }).then(() => {\r\n                            request({\r\n                                url: \"/parking/vehicleReservation/insert\",\r\n                                method: \"POST\",\r\n                                data: {\r\n                                    id: form.data.id,\r\n                                    yardCode: form.data.yardCode,\r\n                                    yardName: form.data.yardName,\r\n                                    channelName: form.data.channelName,\r\n                                    plateNumber: form.data.plateNumber,\r\n                                    vehicleClassification: form.data.vehicleClassification,\r\n                                    merchantName: form.data.merchantName,\r\n                                    releaseReason: form.data.releaseReason,\r\n                                    notifierName: form.data.notifierName,\r\n                                    appointmentTime: form.data.appointmentTime,\r\n                                    remark: form.data.remark\r\n                                },\r\n                            }).then((res) => {\r\n                                if (res.data.code == 0) {\r\n                                    form.data = {}\r\n                                    getData()\r\n                                    ElMessage.success(\"添加成功！\");\r\n                                    formRef.value.resetFields(); // 重置表单校验状态\r\n                                    // 关闭当前页面的标签页;\r\n                                    dialogVisible.value = false\r\n                                } else {\r\n                                    dialogVisible.value = false\r\n                                    ElMessage.error(res.data.msg);\r\n                                    formRef.value.resetFields();\r\n                                }\r\n                            });\r\n                        })\r\n                        .catch(() => {\r\n                            ElMessage({\r\n                                type: 'error',\r\n                                message: '取消添加！',\r\n                            })\r\n                        })\r\n                } else {\r\n                    console.log(\"写入之前：\")\r\n                    console.log(form.data.notifierName)\r\n                    request({\r\n                        url: \"/parking/vehicleReservation/insert\",\r\n                        method: \"POST\",\r\n                        data: {\r\n                            id: form.data.id,\r\n                            yardCode: form.data.yardCode,\r\n                            yardName: form.data.yardName,\r\n                            channelName: form.data.channelName,\r\n                            plateNumber: form.data.plateNumber,\r\n                            vehicleClassification: form.data.vehicleClassification,\r\n                            merchantName: form.data.merchantName,\r\n                            releaseReason: form.data.releaseReason,\r\n                            notifierName: form.data.notifierName,\r\n                            appointmentTime: form.data.appointmentTime,\r\n                            remark: form.data.remark\r\n                        },\r\n                    }).then((res) => {\r\n                        console.log(form.data.notifierName)\r\n                        // 黑A11111\r\n                        if (res.data.code == 0) {\r\n                            form.data = {}\r\n                            getData()\r\n                            ElMessage.success(\"添加成功！\");\r\n                            formRef.value.resetFields(); // 重置表单校验状态\r\n                            // 关闭当前页面的标签页;\r\n                            dialogVisible.value = false\r\n                        } else {\r\n                            dialogVisible.value = false\r\n                            ElMessage.error(res.data.msg);\r\n                            formRef.value.resetFields();\r\n                        }\r\n                    });\r\n                    form.data = {}\r\n                    getData()\r\n                    formRef.value.resetFields(); // 重置表单校验状态\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisible.value = false\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n    // // 表单校验\r\n    // if (form.data.yardName === '四季上东') {\r\n    //     ElMessage.error('请选择万象上东进行录入！');\r\n    // } else {\r\n\r\n    // }\r\n\r\n};\r\nconst update = () => {\r\n    if (form.data.plateNumber.length < 7 || form.data.plateNumber.length > 8) {\r\n        alert('输入长度必须为7-8位');\r\n        form.data.plateNumber = \"\";\r\n        return;\r\n    } else if (/[\\u4e00-\\u9fa5]/.test(form.data.plateNumber)) {\r\n        // 检查输入值是否包含多个汉字\r\n        const chineseCharacters = form.data.plateNumber.match(/[\\u4e00-\\u9fa5]/g);\r\n        if (chineseCharacters && chineseCharacters.length > 2) {\r\n            ('除第一个和最后一个外不允许输入多个汉字');\r\n            form.data.plateNumber = \"\";\r\n            return;\r\n        }\r\n    }\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            request({\r\n                url: \"/parking/vehicleReservation/update\",\r\n                method: \"POST\",\r\n                data: {\r\n                    id: form.data.id,\r\n                    yardCode: form.data.yardCode,\r\n                    yardName: form.data.yardName,\r\n                    channelName: form.data.channelName,\r\n                    plateNumber: form.data.plateNumber,\r\n                    vehicleClassification: form.data.vehicleClassification,\r\n                    merchantName: form.data.merchantName,\r\n                    releaseReason: form.data.releaseReason,\r\n                    notifierName: form.data.notifierName,\r\n                    appointmentTime: form.data.appointmentTime,\r\n                    remark: form.data.remark\r\n                },\r\n            }).then((res) => {\r\n                console.log(\"修改页面\")\r\n                console.log(res)\r\n                console.log(res.data)\r\n                form.data = {}\r\n                if (res.data.code == 0) {\r\n                    getData()\r\n                    ElMessage.success(\"修改成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisibleUpdate.value = false\r\n                } else {\r\n                    dialogVisibleUpdate.value = false\r\n                    ElMessage.error(res.data.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n    background-color: rgb(245, 247, 250) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n\r\n.el-button--text {\r\n    margin-right: 15px;\r\n}\r\n\r\n.el-select {\r\n    width: 300px;\r\n}\r\n\r\n.el-input {\r\n    width: 300px;\r\n}\r\n</style>", "import script from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VehicleReservation.vue?vue&type=style&index=0&id=0f4855e6&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\ParkingDemoAKEHRBU\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0f4855e6\"]])\n\nexport default __exports__"], "sourceRoot": ""}