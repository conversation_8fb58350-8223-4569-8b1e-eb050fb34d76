(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-229c2e5e"],{3045:function(t,e,n){},d39c:function(t,e,n){
/*!
 * qrcode.vue v3.4.1
 * A Vue.js component to generate QRCode.
 * © 2017-2023 @scopewu(https://github.com/scopewu)
 * MIT License.
 */
(function(e,r){t.exports=r(n("7a23"))})(0,(function(t){"use strict";var e,n=function(){return n=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},n.apply(this,arguments)};"function"===typeof SuppressedError&&SuppressedError,function(t){var e=function(){function e(t,n,r,i){if(this.version=t,this.errorCorrectionLevel=n,this.modules=[],this.isFunction=[],t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version value out of range");if(i<-1||i>7)throw new RangeError("Mask value out of range");this.size=4*t+17;for(var a=[],s=0;s<this.size;s++)a.push(!1);for(s=0;s<this.size;s++)this.modules.push(a.slice()),this.isFunction.push(a.slice());this.drawFunctionPatterns();var u=this.addEccAndInterleave(r);if(this.drawCodewords(u),-1==i){var h=1e9;for(s=0;s<8;s++){this.applyMask(s),this.drawFormatBits(s);var c=this.getPenaltyScore();c<h&&(i=s,h=c),this.applyMask(s)}}o(0<=i&&i<=7),this.mask=i,this.applyMask(i),this.drawFormatBits(i),this.isFunction=[]}return e.encodeText=function(n,r){var o=t.QrSegment.makeSegments(n);return e.encodeSegments(o,r)},e.encodeBinary=function(n,r){var o=t.QrSegment.makeBytes(n);return e.encodeSegments([o],r)},e.encodeSegments=function(t,r,a,s,u,h){if(void 0===a&&(a=1),void 0===s&&(s=40),void 0===u&&(u=-1),void 0===h&&(h=!0),!(e.MIN_VERSION<=a&&a<=s&&s<=e.MAX_VERSION)||u<-1||u>7)throw new RangeError("Invalid value");var c,f;for(c=a;;c++){var l=8*e.getNumDataCodewords(c,r),d=i.getTotalBits(t,c);if(d<=l){f=d;break}if(c>=s)throw new RangeError("Data too long")}for(var v=0,g=[e.Ecc.MEDIUM,e.Ecc.QUARTILE,e.Ecc.HIGH];v<g.length;v++){var m=g[v];h&&f<=8*e.getNumDataCodewords(c,m)&&(r=m)}for(var p=[],E=0,w=t;E<w.length;E++){var M=w[E];n(M.mode.modeBits,4,p),n(M.numChars,M.mode.numCharCountBits(c),p);for(var C=0,R=M.getData();C<R.length;C++){var N=R[C];p.push(N)}}o(p.length==f);var A=8*e.getNumDataCodewords(c,r);o(p.length<=A),n(0,Math.min(4,A-p.length),p),n(0,(8-p.length%8)%8,p),o(p.length%8==0);for(var y=236;p.length<A;y^=253)n(y,8,p);var P=[];while(8*P.length<p.length)P.push(0);return p.forEach((function(t,e){return P[e>>>3]|=t<<7-(7&e)})),new e(c,r,P,u)},e.prototype.getModule=function(t,e){return 0<=t&&t<this.size&&0<=e&&e<this.size&&this.modules[e][t]},e.prototype.getModules=function(){return this.modules},e.prototype.drawFunctionPatterns=function(){for(var t=0;t<this.size;t++)this.setFunctionModule(6,t,t%2==0),this.setFunctionModule(t,6,t%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);var e=this.getAlignmentPatternPositions(),n=e.length;for(t=0;t<n;t++)for(var r=0;r<n;r++)0==t&&0==r||0==t&&r==n-1||t==n-1&&0==r||this.drawAlignmentPattern(e[t],e[r]);this.drawFormatBits(0),this.drawVersion()},e.prototype.drawFormatBits=function(t){for(var e=this.errorCorrectionLevel.formatBits<<3|t,n=e,i=0;i<10;i++)n=n<<1^1335*(n>>>9);var a=21522^(e<<10|n);o(a>>>15==0);for(i=0;i<=5;i++)this.setFunctionModule(8,i,r(a,i));this.setFunctionModule(8,7,r(a,6)),this.setFunctionModule(8,8,r(a,7)),this.setFunctionModule(7,8,r(a,8));for(i=9;i<15;i++)this.setFunctionModule(14-i,8,r(a,i));for(i=0;i<8;i++)this.setFunctionModule(this.size-1-i,8,r(a,i));for(i=8;i<15;i++)this.setFunctionModule(8,this.size-15+i,r(a,i));this.setFunctionModule(8,this.size-8,!0)},e.prototype.drawVersion=function(){if(!(this.version<7)){for(var t=this.version,e=0;e<12;e++)t=t<<1^7973*(t>>>11);var n=this.version<<12|t;o(n>>>18==0);for(e=0;e<18;e++){var i=r(n,e),a=this.size-11+e%3,s=Math.floor(e/3);this.setFunctionModule(a,s,i),this.setFunctionModule(s,a,i)}}},e.prototype.drawFinderPattern=function(t,e){for(var n=-4;n<=4;n++)for(var r=-4;r<=4;r++){var o=Math.max(Math.abs(r),Math.abs(n)),i=t+r,a=e+n;0<=i&&i<this.size&&0<=a&&a<this.size&&this.setFunctionModule(i,a,2!=o&&4!=o)}},e.prototype.drawAlignmentPattern=function(t,e){for(var n=-2;n<=2;n++)for(var r=-2;r<=2;r++)this.setFunctionModule(t+r,e+n,1!=Math.max(Math.abs(r),Math.abs(n)))},e.prototype.setFunctionModule=function(t,e,n){this.modules[e][t]=n,this.isFunction[e][t]=!0},e.prototype.addEccAndInterleave=function(t){var n=this.version,r=this.errorCorrectionLevel;if(t.length!=e.getNumDataCodewords(n,r))throw new RangeError("Invalid argument");for(var i=e.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][n],a=e.ECC_CODEWORDS_PER_BLOCK[r.ordinal][n],s=Math.floor(e.getNumRawDataModules(n)/8),u=i-s%i,h=Math.floor(s/i),c=[],f=e.reedSolomonComputeDivisor(a),l=0,d=0;l<i;l++){var v=t.slice(d,d+h-a+(l<u?0:1));d+=v.length;var g=e.reedSolomonComputeRemainder(v,f);l<u&&v.push(0),c.push(v.concat(g))}var m=[],p=function(t){c.forEach((function(e,n){(t!=h-a||n>=u)&&m.push(e[t])}))};for(l=0;l<c[0].length;l++)p(l);return o(m.length==s),m},e.prototype.drawCodewords=function(t){if(t.length!=Math.floor(e.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var n=0,i=this.size-1;i>=1;i-=2){6==i&&(i=5);for(var a=0;a<this.size;a++)for(var s=0;s<2;s++){var u=i-s,h=0==(i+1&2),c=h?this.size-1-a:a;!this.isFunction[c][u]&&n<8*t.length&&(this.modules[c][u]=r(t[n>>>3],7-(7&n)),n++)}}o(n==8*t.length)},e.prototype.applyMask=function(t){if(t<0||t>7)throw new RangeError("Mask value out of range");for(var e=0;e<this.size;e++)for(var n=0;n<this.size;n++){var r=void 0;switch(t){case 0:r=(n+e)%2==0;break;case 1:r=e%2==0;break;case 2:r=n%3==0;break;case 3:r=(n+e)%3==0;break;case 4:r=(Math.floor(n/3)+Math.floor(e/2))%2==0;break;case 5:r=n*e%2+n*e%3==0;break;case 6:r=(n*e%2+n*e%3)%2==0;break;case 7:r=((n+e)%2+n*e%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[e][n]&&r&&(this.modules[e][n]=!this.modules[e][n])}},e.prototype.getPenaltyScore=function(){for(var t=0,n=0;n<this.size;n++){for(var r=!1,i=0,a=[0,0,0,0,0,0,0],s=0;s<this.size;s++)this.modules[n][s]==r?(i++,5==i?t+=e.PENALTY_N1:i>5&&t++):(this.finderPenaltyAddHistory(i,a),r||(t+=this.finderPenaltyCountPatterns(a)*e.PENALTY_N3),r=this.modules[n][s],i=1);t+=this.finderPenaltyTerminateAndCount(r,i,a)*e.PENALTY_N3}for(s=0;s<this.size;s++){r=!1;var u=0;for(a=[0,0,0,0,0,0,0],n=0;n<this.size;n++)this.modules[n][s]==r?(u++,5==u?t+=e.PENALTY_N1:u>5&&t++):(this.finderPenaltyAddHistory(u,a),r||(t+=this.finderPenaltyCountPatterns(a)*e.PENALTY_N3),r=this.modules[n][s],u=1);t+=this.finderPenaltyTerminateAndCount(r,u,a)*e.PENALTY_N3}for(n=0;n<this.size-1;n++)for(s=0;s<this.size-1;s++){var h=this.modules[n][s];h==this.modules[n][s+1]&&h==this.modules[n+1][s]&&h==this.modules[n+1][s+1]&&(t+=e.PENALTY_N2)}for(var c=0,f=0,l=this.modules;f<l.length;f++){var d=l[f];c=d.reduce((function(t,e){return t+(e?1:0)}),c)}var v=this.size*this.size,g=Math.ceil(Math.abs(20*c-10*v)/v)-1;return o(0<=g&&g<=9),t+=g*e.PENALTY_N4,o(0<=t&&t<=2568888),t},e.prototype.getAlignmentPatternPositions=function(){if(1==this.version)return[];for(var t=Math.floor(this.version/7)+2,e=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*t-2)),n=[6],r=this.size-7;n.length<t;r-=e)n.splice(1,0,r);return n},e.getNumRawDataModules=function(t){if(t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version number out of range");var n=(16*t+128)*t+64;if(t>=2){var r=Math.floor(t/7)+2;n-=(25*r-10)*r-55,t>=7&&(n-=36)}return o(208<=n&&n<=29648),n},e.getNumDataCodewords=function(t,n){return Math.floor(e.getNumRawDataModules(t)/8)-e.ECC_CODEWORDS_PER_BLOCK[n.ordinal][t]*e.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][t]},e.reedSolomonComputeDivisor=function(t){if(t<1||t>255)throw new RangeError("Degree out of range");for(var n=[],r=0;r<t-1;r++)n.push(0);n.push(1);var o=1;for(r=0;r<t;r++){for(var i=0;i<n.length;i++)n[i]=e.reedSolomonMultiply(n[i],o),i+1<n.length&&(n[i]^=n[i+1]);o=e.reedSolomonMultiply(o,2)}return n},e.reedSolomonComputeRemainder=function(t,n){for(var r=n.map((function(t){return 0})),o=function(t){var o=t^r.shift();r.push(0),n.forEach((function(t,n){return r[n]^=e.reedSolomonMultiply(t,o)}))},i=0,a=t;i<a.length;i++){var s=a[i];o(s)}return r},e.reedSolomonMultiply=function(t,e){if(t>>>8!=0||e>>>8!=0)throw new RangeError("Byte out of range");for(var n=0,r=7;r>=0;r--)n=n<<1^285*(n>>>7),n^=(e>>>r&1)*t;return o(n>>>8==0),n},e.prototype.finderPenaltyCountPatterns=function(t){var e=t[1];o(e<=3*this.size);var n=e>0&&t[2]==e&&t[3]==3*e&&t[4]==e&&t[5]==e;return(n&&t[0]>=4*e&&t[6]>=e?1:0)+(n&&t[6]>=4*e&&t[0]>=e?1:0)},e.prototype.finderPenaltyTerminateAndCount=function(t,e,n){return t&&(this.finderPenaltyAddHistory(e,n),e=0),e+=this.size,this.finderPenaltyAddHistory(e,n),this.finderPenaltyCountPatterns(n)},e.prototype.finderPenaltyAddHistory=function(t,e){0==e[0]&&(t+=this.size),e.pop(),e.unshift(t)},e.MIN_VERSION=1,e.MAX_VERSION=40,e.PENALTY_N1=3,e.PENALTY_N2=3,e.PENALTY_N3=40,e.PENALTY_N4=10,e.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],e.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],e}();function n(t,e,n){if(e<0||e>31||t>>>e!=0)throw new RangeError("Value out of range");for(var r=e-1;r>=0;r--)n.push(t>>>r&1)}function r(t,e){return 0!=(t>>>e&1)}function o(t){if(!t)throw new Error("Assertion error")}t.QrCode=e;var i=function(){function t(t,e,n){if(this.mode=t,this.numChars=e,this.bitData=n,e<0)throw new RangeError("Invalid argument");this.bitData=n.slice()}return t.makeBytes=function(e){for(var r=[],o=0,i=e;o<i.length;o++){var a=i[o];n(a,8,r)}return new t(t.Mode.BYTE,e.length,r)},t.makeNumeric=function(e){if(!t.isNumeric(e))throw new RangeError("String contains non-numeric characters");for(var r=[],o=0;o<e.length;){var i=Math.min(e.length-o,3);n(parseInt(e.substring(o,o+i),10),3*i+1,r),o+=i}return new t(t.Mode.NUMERIC,e.length,r)},t.makeAlphanumeric=function(e){if(!t.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");var r,o=[];for(r=0;r+2<=e.length;r+=2){var i=45*t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r));i+=t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r+1)),n(i,11,o)}return r<e.length&&n(t.ALPHANUMERIC_CHARSET.indexOf(e.charAt(r)),6,o),new t(t.Mode.ALPHANUMERIC,e.length,o)},t.makeSegments=function(e){return""==e?[]:t.isNumeric(e)?[t.makeNumeric(e)]:t.isAlphanumeric(e)?[t.makeAlphanumeric(e)]:[t.makeBytes(t.toUtf8ByteArray(e))]},t.makeEci=function(e){var r=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<128)n(e,8,r);else if(e<16384)n(2,2,r),n(e,14,r);else{if(!(e<1e6))throw new RangeError("ECI assignment value out of range");n(6,3,r),n(e,21,r)}return new t(t.Mode.ECI,0,r)},t.isNumeric=function(e){return t.NUMERIC_REGEX.test(e)},t.isAlphanumeric=function(e){return t.ALPHANUMERIC_REGEX.test(e)},t.prototype.getData=function(){return this.bitData.slice()},t.getTotalBits=function(t,e){for(var n=0,r=0,o=t;r<o.length;r++){var i=o[r],a=i.mode.numCharCountBits(e);if(i.numChars>=1<<a)return 1/0;n+=4+a+i.bitData.length}return n},t.toUtf8ByteArray=function(t){t=encodeURI(t);for(var e=[],n=0;n<t.length;n++)"%"!=t.charAt(n)?e.push(t.charCodeAt(n)):(e.push(parseInt(t.substring(n+1,n+3),16)),n+=2);return e},t.NUMERIC_REGEX=/^[0-9]*$/,t.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,t.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t}();t.QrSegment=i}(e||(e={})),function(t){(function(t){var e=function(){function t(t,e){this.ordinal=t,this.formatBits=e}return t.LOW=new t(0,1),t.MEDIUM=new t(1,0),t.QUARTILE=new t(2,3),t.HIGH=new t(3,2),t}();t.Ecc=e})(t.QrCode||(t.QrCode={}))}(e||(e={})),function(t){(function(t){var e=function(){function t(t,e){this.modeBits=t,this.numBitsCharCount=e}return t.prototype.numCharCountBits=function(t){return this.numBitsCharCount[Math.floor((t+7)/17)]},t.NUMERIC=new t(1,[10,12,14]),t.ALPHANUMERIC=new t(2,[9,11,13]),t.BYTE=new t(4,[8,16,16]),t.KANJI=new t(8,[8,10,12]),t.ECI=new t(7,[0,0,0]),t}();t.Mode=e})(t.QrSegment||(t.QrSegment={}))}(e||(e={}));var r=e,o="H",i={L:r.QrCode.Ecc.LOW,M:r.QrCode.Ecc.MEDIUM,Q:r.QrCode.Ecc.QUARTILE,H:r.QrCode.Ecc.HIGH},a=function(){try{(new Path2D).addPath(new Path2D)}catch(t){return!1}return!0}();function s(t){return t in i}function u(t,e){void 0===e&&(e=0);var n=[];return t.forEach((function(t,r){var o=null;t.forEach((function(i,a){if(!i&&null!==o)return n.push("M".concat(o+e," ").concat(r+e,"h").concat(a-o,"v1H").concat(o+e,"z")),void(o=null);if(a!==t.length-1)i&&null===o&&(o=a);else{if(!i)return;null===o?n.push("M".concat(a+e,",").concat(r+e," h1v1H").concat(a+e,"z")):n.push("M".concat(o+e,",").concat(r+e," h").concat(a+1-o,"v1H").concat(o+e,"z"))}}))})),n.join("")}var h={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:o,validator:function(t){return s(t)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0}},c=n(n({},h),{renderAs:{type:String,required:!1,default:"canvas",validator:function(t){return["canvas","svg"].indexOf(t)>-1}}}),f=t.defineComponent({name:"QRCodeSvg",props:h,setup:function(e){var n=t.ref(0),o=t.ref(""),a=function(){var t=e.value,a=e.level,s=e.margin,h=r.QrCode.encodeText(t,i[a]).getModules();n.value=h.length+2*s,o.value=u(h,s)};return a(),t.onUpdated(a),function(){return t.h("svg",{width:e.size,height:e.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(n.value," ").concat(n.value)},[t.h("path",{fill:e.background,d:"M0,0 h".concat(n.value,"v").concat(n.value,"H0z")}),t.h("path",{fill:e.foreground,d:o.value})])}}}),l=t.defineComponent({name:"QRCodeCanvas",props:h,setup:function(e){var n=t.ref(null),o=function(){var t=e.value,o=e.level,s=e.size,h=e.margin,c=e.background,f=e.foreground,l=n.value;if(l){var d=l.getContext("2d");if(d){var v=r.QrCode.encodeText(t,i[o]).getModules(),g=v.length+2*h,m=window.devicePixelRatio||1,p=s/g*m;l.height=l.width=s*m,d.scale(p,p),d.fillStyle=c,d.fillRect(0,0,g,g),d.fillStyle=f,a?d.fill(new Path2D(u(v,h))):v.forEach((function(t,e){t.forEach((function(t,n){t&&d.fillRect(n+h,e+h,1,1)}))}))}}};return t.onMounted(o),t.onUpdated(o),function(){return t.h("canvas",{ref:n,style:{width:"".concat(e.size,"px"),height:"".concat(e.size,"px")}})}}}),d=t.defineComponent({name:"Qrcode",render:function(){var e=this.$props,n=e.renderAs,r=e.value,i=e.size,a=e.margin,u=e.level,h=e.background,c=e.foreground,d=i>>>0,v=a>>>0,g=s(u)?u:o;return t.h("svg"===n?f:l,{value:r,size:d,margin:v,level:g,background:h,foreground:c})},props:c});return d}))}}]);
//# sourceMappingURL=chunk-229c2e5e.6fae31f8.js.map