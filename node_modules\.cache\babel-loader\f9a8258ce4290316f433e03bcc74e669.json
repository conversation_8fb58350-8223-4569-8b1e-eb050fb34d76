{"remainingRequest": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\OwnerInfo.vue?vue&type=template&id=9f60b912&scoped=true", "dependencies": [{"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\OwnerInfo.vue", "mtime": 1725760766286}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlRWxlbWVudFZOb2RlIGFzIF9jcmVhdGVFbGVtZW50Vk5vZGUsIGNyZWF0ZVRleHRWTm9kZSBhcyBfY3JlYXRlVGV4dFZOb2RlLCByZXNvbHZlQ29tcG9uZW50IGFzIF9yZXNvbHZlQ29tcG9uZW50LCB3aXRoQ3R4IGFzIF93aXRoQ3R4LCBjcmVhdGVWTm9kZSBhcyBfY3JlYXRlVk5vZGUsIHJlbmRlckxpc3QgYXMgX3JlbmRlckxpc3QsIEZyYWdtZW50IGFzIF9GcmFnbWVudCwgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrLCBjcmVhdGVCbG9jayBhcyBfY3JlYXRlQmxvY2ssIGNyZWF0ZUNvbW1lbnRWTm9kZSBhcyBfY3JlYXRlQ29tbWVudFZOb2RlLCB0b0Rpc3BsYXlTdHJpbmcgYXMgX3RvRGlzcGxheVN0cmluZywgcHVzaFNjb3BlSWQgYXMgX3B1c2hTY29wZUlkLCBwb3BTY29wZUlkIGFzIF9wb3BTY29wZUlkIH0gZnJvbSAidnVlIjsKaW1wb3J0IF9pbXBvcnRzXzAgZnJvbSAnLi4vLy4uL2ljb25zL3N2Zy1ibGFjay9Pd25lckluZm8uc3ZnJzsKaW1wb3J0IF9pbXBvcnRzXzEgZnJvbSAnQC9hc3NldHMvaW1nL2RlbC1jYXJDb2RlLnN2Zyc7CmltcG9ydCBfaW1wb3J0c18yIGZyb20gJ0AvYXNzZXRzL2ltZy9hZGRDYXJDb2RlLnN2Zyc7CmNvbnN0IF93aXRoU2NvcGVJZCA9IG4gPT4gKF9wdXNoU2NvcGVJZCgiZGF0YS12LTlmNjBiOTEyIiksIG4gPSBuKCksIF9wb3BTY29wZUlkKCksIG4pOwpjb25zdCBfaG9pc3RlZF8xID0gewogIGNsYXNzOiAiY3J1bWJzIgp9Owpjb25zdCBfaG9pc3RlZF8yID0gLyojX19QVVJFX18qL193aXRoU2NvcGVJZCgoKSA9PiAvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaSIsIG51bGwsIFsvKiNfX1BVUkVfXyovX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW1nIiwgewogIHNyYzogX2ltcG9ydHNfMAp9KV0sIC0xIC8qIEhPSVNURUQgKi8pKTsKY29uc3QgX2hvaXN0ZWRfMyA9IHsKICBjbGFzczogImNvbnRhaW5lciIKfTsKY29uc3QgX2hvaXN0ZWRfNCA9IHsKICBjbGFzczogImhhbmRsZS1ib3giCn07CmNvbnN0IF9ob2lzdGVkXzUgPSB7CiAgY2xhc3M6ICJwYWdpbmF0aW9uIgp9Owpjb25zdCBfaG9pc3RlZF82ID0gWyJvbkNsaWNrIl07CmNvbnN0IF9ob2lzdGVkXzcgPSBbIm9uQ2xpY2siXTsKY29uc3QgX2hvaXN0ZWRfOCA9IHsKICBjbGFzczogImRpYWxvZy1mb290ZXIiCn07CmNvbnN0IF9ob2lzdGVkXzkgPSBbIm9uQ2xpY2siXTsKY29uc3QgX2hvaXN0ZWRfMTAgPSBbIm9uQ2xpY2siXTsKY29uc3QgX2hvaXN0ZWRfMTEgPSB7CiAgY2xhc3M6ICJkaWFsb2ctZm9vdGVyIgp9Owpjb25zdCBfaG9pc3RlZF8xMiA9IHsKICBzdHlsZTogewogICAgIm1hcmdpbi1sZWZ0IjogIjUwcHgiCiAgfQp9Owpjb25zdCBfaG9pc3RlZF8xMyA9IHsKICBjbGFzczogImRpYWxvZy1mb290ZXIiCn07CmNvbnN0IF9ob2lzdGVkXzE0ID0gewogIHN0eWxlOiB7CiAgICAibWFyZ2luLWxlZnQiOiAiNTBweCIKICB9Cn07CmNvbnN0IF9ob2lzdGVkXzE1ID0gewogIGNsYXNzOiAiZGlhbG9nLWZvb3RlciIKfTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICBjb25zdCBfY29tcG9uZW50X2VsX2JyZWFkY3J1bWJfaXRlbSA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1icmVhZGNydW1iLWl0ZW0iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2JyZWFkY3J1bWIgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtYnJlYWRjcnVtYiIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfaW5wdXQgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtaW5wdXQiKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2Zvcm1faXRlbSA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1mb3JtLWl0ZW0iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2J1dHRvbiA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC1idXR0b24iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX3VwbG9hZCA9IF9yZXNvbHZlQ29tcG9uZW50KCJlbC11cGxvYWQiKTsKICBjb25zdCBfY29tcG9uZW50X2VsX2Zvcm0gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtZm9ybSIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfdGFibGVfY29sdW1uID0gX3Jlc29sdmVDb21wb25lbnQoImVsLXRhYmxlLWNvbHVtbiIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfdGFibGUgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtdGFibGUiKTsKICBjb25zdCBfY29tcG9uZW50X2VsX3BhZ2luYXRpb24gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtcGFnaW5hdGlvbiIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfb3B0aW9uID0gX3Jlc29sdmVDb21wb25lbnQoImVsLW9wdGlvbiIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfc2VsZWN0ID0gX3Jlc29sdmVDb21wb25lbnQoImVsLXNlbGVjdCIpOwogIGNvbnN0IF9jb21wb25lbnRfZWxfcmFkaW8gPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtcmFkaW8iKTsKICBjb25zdCBfY29tcG9uZW50X2VsX3JhZGlvX2dyb3VwID0gX3Jlc29sdmVDb21wb25lbnQoImVsLXJhZGlvLWdyb3VwIik7CiAgY29uc3QgX2NvbXBvbmVudF9lbF9kaWFsb2cgPSBfcmVzb2x2ZUNvbXBvbmVudCgiZWwtZGlhbG9nIik7CiAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUVsZW1lbnRCbG9jaygiZGl2IiwgbnVsbCwgW19jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzEsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9icmVhZGNydW1iLCB7CiAgICBzZXBhcmF0b3I6ICIvIgogIH0sIHsKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9icmVhZGNydW1iX2l0ZW0sIG51bGwsIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19ob2lzdGVkXzIsIF9jcmVhdGVUZXh0Vk5vZGUoIiDkuJrkuLvnrqHnkIYgIildKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0pXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIF9ob2lzdGVkXzMsIFtfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBfaG9pc3RlZF80LCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybSwgewogICAgaW5saW5lOiB0cnVlLAogICAgbW9kZWw6ICRzZXR1cC5xdWVyeSwKICAgIGNsYXNzOiAiZGVtby1mb3JtLWlubGluZSIsCiAgICAibGFiZWwtd2lkdGgiOiAiNjBweCIKICB9LCB7CiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICJsYWJlbC13aWR0aCI6ICI4MHB4IiwKICAgICAgbGFiZWw6ICLlsI/ljLrlkI3np7AiCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5xdWVyeS5jb21tdW5pdHksCiAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMF0gfHwgKF9jYWNoZVswXSA9ICRldmVudCA9PiAkc2V0dXAucXVlcnkuY29tbXVuaXR5ID0gJGV2ZW50KSwKICAgICAgICBwbGFjZWhvbGRlcjogIuWwj+WMuuWQjeensCIsCiAgICAgICAgY2xhc3M6ICJoYW5kbGUtaW5wdXQgbXIxMCIsCiAgICAgICAgY2xlYXJhYmxlOiAiIgogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgImxhYmVsLXdpZHRoIjogIjgwcHgiLAogICAgICBsYWJlbDogIuS4muS4u+Wnk+WQjSIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLnF1ZXJ5Lm93bmVybmFtZSwKICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxXSB8fCAoX2NhY2hlWzFdID0gJGV2ZW50ID0+ICRzZXR1cC5xdWVyeS5vd25lcm5hbWUgPSAkZXZlbnQpLAogICAgICAgIHBsYWNlaG9sZGVyOiAi5Lia5Li75aeT5ZCNIiwKICAgICAgICBjbGFzczogImhhbmRsZS1pbnB1dCBtcjEwIiwKICAgICAgICBjbGVhcmFibGU6ICIiCiAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICB0eXBlOiAicHJpbWFyeSIsCiAgICAgIGNsYXNzOiAic2VhcmNoQnV0dG9uIiwKICAgICAgaWNvbjogInNlYXJjaCIsCiAgICAgIG9uQ2xpY2s6ICRzZXR1cC5oYW5kbGVTZWFyY2gKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuaQnOe0oiAiKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICB0eXBlOiAicHJpbWFyeSIsCiAgICAgIGNsYXNzOiAiYWRkQnV0dG9uIiwKICAgICAgb25DbGljazogJHNldHVwLmhhbmRsZUFkZAogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi5paw5aKeICIpXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdXBsb2FkLCB7CiAgICAgIHJlZjogInVwbG9hZCIsCiAgICAgIGNsYXNzOiAidXBsb2FkLWRlbW8iLAogICAgICBhY3Rpb246ICIiLAogICAgICBhY2NlcHQ6ICIueGxzLC54bHN4IiwKICAgICAgIm9uLWNoYW5nZSI6ICRzZXR1cC5vblVwbG9hZCwKICAgICAgbGltaXQ6IDEsCiAgICAgICJvbi1leGNlZWQiOiBfY3R4LmhhbmRsZUV4Y2VlZCwKICAgICAgIm9uLWVycm9yIjogJHNldHVwLm9uRXJyb3JGaWxlLAogICAgICAib24tc3VjY2VzcyI6ICRzZXR1cC5vblN1Y2Nlc3NGaWxlLAogICAgICAiYXV0by11cGxvYWQiOiBmYWxzZSwKICAgICAgImZpbGUtbGlzdCI6ICRzZXR1cC5maWxlTGlzdCwKICAgICAgInNob3ctZmlsZS1saXN0IjogZmFsc2UsCiAgICAgIG5hbWU6ICJmaWxlIgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgY2xhc3M6ICJ1cGxvYWRCdXR0b24iLAogICAgICAgIHR5cGU6ICJwcmltYXJ5IgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIumAieaLqeaWh+S7tiIpXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsib24tZXhjZWVkIiwgImZpbGUtbGlzdCJdKV0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsIl0pXSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3RhYmxlLCB7CiAgICBkYXRhOiAkc2V0dXAudGFibGVEYXRhLAogICAgYm9yZGVyOiAiIiwKICAgIGNsYXNzOiAidGFibGUiLAogICAgcmVmOiAibXVsdGlwbGVUYWJsZSIsCiAgICAiaGVhZGVyLWNlbGwtY2xhc3MtbmFtZSI6ICJ0YWJsZS1oZWFkZXIiLAogICAgImNlbGwtc3R5bGUiOiAkc2V0dXAuY2VsbFN0eWxlLAogICAgInJvdy1jbGFzcy1uYW1lIjogJHNldHVwLnRhYmxlUm93Q2xhc3NOYW1lCiAgfSwgewogICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gWyhfb3BlbkJsb2NrKCksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkc2V0dXAucHJvcHMsIGl0ZW0gPT4gewogICAgICByZXR1cm4gX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfdGFibGVfY29sdW1uLCB7CiAgICAgICAgInNob3ctb3ZlcmZsb3ctdG9vbHRpcCI6IHRydWUsCiAgICAgICAgcHJvcDogaXRlbS5wcm9wLAogICAgICAgIGxhYmVsOiBpdGVtLmxhYmVsLAogICAgICAgIGtleTogaXRlbS5wcm9wLAogICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbInByb3AiLCAibGFiZWwiXSk7CiAgICB9KSwgNjQgLyogU1RBQkxFX0ZSQUdNRU5UICovKSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3RhYmxlX2NvbHVtbiwgewogICAgICBsYWJlbDogIuaTjeS9nCIsCiAgICAgIHdpZHRoOiAiMTgwIiwKICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICBmaXhlZDogInJpZ2h0IgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eChzY29wZSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgdHlwZTogInRleHQiLAogICAgICAgIGljb246ICJlbC1pY29uLWVkaXQiLAogICAgICAgIG9uQ2xpY2s6ICRldmVudCA9PiAkc2V0dXAuaGFuZGxlRWRpdChzY29wZS5yb3cpCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi57yW6L6RICIpXSksCiAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgIH0sIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsib25DbGljayJdKSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgICAgdHlwZTogInRleHQiLAogICAgICAgIGljb246ICJlbC1pY29uLWRlbGV0ZSIsCiAgICAgICAgY2xhc3M6ICJyZWQiLAogICAgICAgIG9uQ2xpY2s6ICRldmVudCA9PiAkc2V0dXAuaGFuZGxlRGVsZXRlKHNjb3BlLiRpbmRleCwgc2NvcGUucm93LmlkKQogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuWIoOmZpCAiKV0pLAogICAgICAgIF86IDIgLyogRFlOQU1JQyAqLwogICAgICB9LCAxMDMyIC8qIFBST1BTLCBEWU5BTUlDX1NMT1RTICovLCBbIm9uQ2xpY2siXSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsiZGF0YSJdKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgX2hvaXN0ZWRfNSwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3BhZ2luYXRpb24sIHsKICAgIGN1cnJlbnRQYWdlOiAkc2V0dXAucXVlcnkucGFnZU51bSwKICAgICJwYWdlLXNpemVzIjogWzEwLCAyMCwgNDBdLAogICAgInBhZ2Utc2l6ZSI6ICRzZXR1cC5xdWVyeS5wYWdlU2l6ZSwKICAgIGxheW91dDogInRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIsCiAgICB0b3RhbDogJHNldHVwLnBhZ2VUb3RhbCwKICAgIG9uU2l6ZUNoYW5nZTogJHNldHVwLmhhbmRsZVNpemVDaGFuZ2UsCiAgICBvbkN1cnJlbnRDaGFuZ2U6ICRzZXR1cC5oYW5kbGVQYWdlQ2hhbmdlCiAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJjdXJyZW50UGFnZSIsICJwYWdlLXNpemUiLCAidG90YWwiXSldKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBudWxsLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZGlhbG9nLCB7CiAgICB0aXRsZTogIuWinuWKoOS4muS4u+S/oeaBryIsCiAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZGlhbG9nVmlzaWJsZSwKICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzE1XSB8fCAoX2NhY2hlWzE1XSA9ICRldmVudCA9PiAkc2V0dXAuZGlhbG9nVmlzaWJsZSA9ICRldmVudCksCiAgICB3aWR0aDogIjUwJSIKICB9LCB7CiAgICBmb290ZXI6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgX2hvaXN0ZWRfOCwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICBvbkNsaWNrOiBfY2FjaGVbMTRdIHx8IChfY2FjaGVbMTRdID0gJGV2ZW50ID0+ICRzZXR1cC5kaWFsb2dWaXNpYmxlID0gZmFsc2UpCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKCLlj5Yg5raIIildKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9idXR0b24sIHsKICAgICAgdHlwZTogInByaW1hcnkiLAogICAgICBvbkNsaWNrOiAkc2V0dXAuc2F2ZQogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi56GuIOWumiIpXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KV0pXSksCiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybSwgewogICAgICBtb2RlbDogJHNldHVwLmZvcm0uZGF0YSwKICAgICAgcmVmOiAiZm9ybVJlZiIsCiAgICAgIHJ1bGVzOiAkc2V0dXAucnVsZXMsCiAgICAgICJsYWJlbC13aWR0aCI6ICIxMDBweCIKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi55yB5Lu9IiwKICAgICAgICBwcm9wOiAicHJvdmluY2UiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLnByb3ZpbmNlLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMl0gfHwgKF9jYWNoZVsyXSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLnByb3ZpbmNlID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup55yB5Lu9IgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5wcm92aW5jZUxpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0ucHJvdmluY2UsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0ucHJvdmluY2UsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucHJvdmluY2UsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZVByb3ZpbmNlCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5Zyw5biCIiwKICAgICAgICBwcm9wOiAiY2l0eSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9zZWxlY3QsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEuY2l0eSwKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzNdIHx8IChfY2FjaGVbM10gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5jaXR5ID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5Zyw5biCIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5jaXR5TGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5jaXR5LAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmNpdHksCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uY2l0eSwKICAgICAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuY2hhbmdlQ2l0eQogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuWMuuWOvyIsCiAgICAgICAgcHJvcDogImRpc3RyaWN0IgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5kaXN0cmljdCwKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzRdIHx8IChfY2FjaGVbNF0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5kaXN0cmljdCA9ICRldmVudCksCiAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqeWMuuWOvyIKICAgICAgICB9LCB7CiAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkc2V0dXAuZGlzdHJpY3RMaXN0LCBpdGVtID0+IHsKICAgICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfb3B0aW9uLCB7CiAgICAgICAgICAgICAga2V5OiBpdGVtLmRpc3RyaWN0LAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmRpc3RyaWN0LAogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmRpc3RyaWN0LAogICAgICAgICAgICAgIG9uQ2xpY2s6ICRzZXR1cC5jaGFuZ2VEaXN0cmljdAogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuWwj+WMuiIsCiAgICAgICAgcHJvcDogImNvbW11bml0eSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9zZWxlY3QsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEuY29tbXVuaXR5LAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbNV0gfHwgKF9jYWNoZVs1XSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmNvbW11bml0eSA9ICRldmVudCksCiAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqeWwj+WMuiIKICAgICAgICB9LCB7CiAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkc2V0dXAuY29tbXVuaXR5TGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5jb21tdW5pdHksCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uY29tbXVuaXR5LAogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmNvbW11bml0eSwKICAgICAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuY2hhbmdlQ29tbXVuaXR5CiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5qW85qCLIiwKICAgICAgICBwcm9wOiAiYnVpbGRpbmciCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLmJ1aWxkaW5nLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbNl0gfHwgKF9jYWNoZVs2XSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmJ1aWxkaW5nID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5qW85qCLIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5idWlsZGluZ0xpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZUJ1aWxkaW5nCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5Y2V5YWDIiwKICAgICAgICBwcm9wOiAidW5pdHMiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLnVuaXRzLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbN10gfHwgKF9jYWNoZVs3XSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLnVuaXRzID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5Y2V5YWDIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC51bml0c0xpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0udW5pdHMsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0udW5pdHMsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udW5pdHMsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZVVuaXRzCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5qW85bGCIiwKICAgICAgICBwcm9wOiAiZmxvb3IiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLmZsb29yLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbOF0gfHwgKF9jYWNoZVs4XSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmZsb29yID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5qW85bGCIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5mbG9vckxpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0uZmxvb3IsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uZmxvb3IsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uZmxvb3IsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZUZsb29yCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5oi/5Y+3IiwKICAgICAgICBwcm9wOiAicm9vbW51bWJlciIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9zZWxlY3QsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEucm9vbW51bWJlciwKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzldIHx8IChfY2FjaGVbOV0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5yb29tbnVtYmVyID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi5oi/5Y+3IgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5yb29tbnVtYmVyTGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5yb29tbnVtYmVyLAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLnJvb21udW1iZXIsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucm9vbW51bWJlcgogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuS4muS4u+WQjeensCIsCiAgICAgICAgcHJvcDogIm93bmVybmFtZSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5vd25lcm5hbWUsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxMF0gfHwgKF9jYWNoZVsxMF0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5vd25lcm5hbWUgPSAkZXZlbnQpLAogICAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICAgIndpZHRoIjogIjgwJSIKICAgICAgICAgIH0KICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLkuJrkuLvnlLXor50iLAogICAgICAgIHByb3A6ICJvd25lcnBob25lIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLm93bmVycGhvbmUsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxMV0gfHwgKF9jYWNoZVsxMV0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5vd25lcnBob25lID0gJGV2ZW50KSwKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICJ3aWR0aCI6ICI4MCUiCiAgICAgICAgICB9CiAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVDb21tZW50Vk5vZGUoIiBsYWJlbD1cIui9pueJjOWPt1wiICAgICIpLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIui9pueJjOWPtyIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5mb3JtLmRhdGEuY2FyRGF0YXMsIChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgICAgIGtleTogaW5kZXgsCiAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgIm1hcmdpbi1yaWdodCI6ICIxNnB4IiwKICAgICAgICAgICAgICAiZGlzcGxheSI6ICJpbmxpbmUtYmxvY2siLAogICAgICAgICAgICAgICJtYXJnaW4tYm90dG9tIjogIjE4cHgiCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgICAgICAgIHByb3A6ICdjYXJEYXRhcy4nICsgaW5kZXggKyAnLmRhdGEnLAogICAgICAgICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXovabniYzlj7cnLAogICAgICAgICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgICAgICAid2lkdGgiOiAiMTk0cHgiCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgdHlwZTogInRleHQiLAogICAgICAgICAgICAgICAgbW9kZWxWYWx1ZTogaXRlbS5kYXRhLAogICAgICAgICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiAkZXZlbnQgPT4gaXRlbS5kYXRhID0gJGV2ZW50LAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fovpPlhaXovabniYzlj7ciCiAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIiwgIm9uVXBkYXRlOm1vZGVsVmFsdWUiXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImltZyIsIHsKICAgICAgICAgICAgICAgIHNyYzogX2ltcG9ydHNfMSwKICAgICAgICAgICAgICAgIG9uQ2xpY2s6ICRldmVudCA9PiAkc2V0dXAuZGVsZXRlQ2FyKGluZGV4KSwKICAgICAgICAgICAgICAgIGFsdDogIiIsCiAgICAgICAgICAgICAgICBjbGFzczogImRlbC1jYXJDb2RlIgogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzYpXSksCiAgICAgICAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgICAgICAgIH0sIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsicHJvcCJdKV0pLAogICAgICAgICAgICBfOiAyIC8qIERZTkFNSUMgKi8KICAgICAgICAgIH0sIDEwMjQgLyogRFlOQU1JQ19TTE9UUyAqLyk7CiAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJpbWciLCB7CiAgICAgICAgICBzcmM6IF9pbXBvcnRzXzIsCiAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuYWRkQ2FyLAogICAgICAgICAgYWx0OiAiIiwKICAgICAgICAgIGNsYXNzOiAiYWRkQ2FyQ29kZSIKICAgICAgICB9KV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIui9puS9jeWPt+eggSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5mb3JtLmRhdGEucGFya2luZ0RhdGFzLCAoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgICJtYXJnaW4tcmlnaHQiOiAiMTZweCIsCiAgICAgICAgICAgICAgImRpc3BsYXkiOiAiaW5saW5lLWJsb2NrIiwKICAgICAgICAgICAgICAibWFyZ2luLWJvdHRvbSI6ICIxOHB4IgogICAgICAgICAgICB9CiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICAgICAgICBwcm9wOiAncGFya2luZ0RhdGFzLicgKyBpbmRleCArICcuZGF0YScsCiAgICAgICAgICAgICAgcnVsZXM6IHsKICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpei9puS9jeWPt+eggScsCiAgICAgICAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgICAgICJ3aWR0aCI6ICIxOTRweCIKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB0eXBlOiAidGV4dCIsCiAgICAgICAgICAgICAgICBtb2RlbFZhbHVlOiBpdGVtLmRhdGEsCiAgICAgICAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6ICRldmVudCA9PiBpdGVtLmRhdGEgPSAkZXZlbnQsCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+i+k+WFpei9puS9jeWPt+eggSIKICAgICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiLCAib25VcGRhdGU6bW9kZWxWYWx1ZSJdKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW1nIiwgewogICAgICAgICAgICAgICAgc3JjOiBfaW1wb3J0c18xLAogICAgICAgICAgICAgICAgb25DbGljazogJGV2ZW50ID0+ICRzZXR1cC5kZWxldGVQYXJraW5nKGluZGV4KSwKICAgICAgICAgICAgICAgIGFsdDogIiIsCiAgICAgICAgICAgICAgICBjbGFzczogImRlbC1jYXJDb2RlIgogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzcpXSksCiAgICAgICAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgICAgICAgIH0sIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsicHJvcCJdKV0pLAogICAgICAgICAgICBfOiAyIC8qIERZTkFNSUMgKi8KICAgICAgICAgIH0sIDEwMjQgLyogRFlOQU1JQ19TTE9UUyAqLyk7CiAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJpbWciLCB7CiAgICAgICAgICBzcmM6IF9pbXBvcnRzXzIsCiAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuYWRkUGFya2luZywKICAgICAgICAgIGFsdDogIiIsCiAgICAgICAgICBjbGFzczogImFkZENhckNvZGUiCiAgICAgICAgfSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLmmK/lkKblrqHmibkiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfcmFkaW9fZ3JvdXAsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEuaXNhdWRpdCwKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzEyXSB8fCAoX2NhY2hlWzEyXSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmlzYXVkaXQgPSAkZXZlbnQpCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3JhZGlvLCB7CiAgICAgICAgICAgIGxhYmVsOiAn5pivJwogICAgICAgICAgfSwgewogICAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi5pivIildKSwKICAgICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpbywgewogICAgICAgICAgICBsYWJlbDogJ+WQpicKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuWQpiIpXSksCiAgICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgICB9KV0pLAogICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLmmK/lkKblvIDlkK/oh6rliqnpooTnuqYiLAogICAgICAgICJsYWJlbC13aWR0aCI6ICIxNTBweCIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpb19ncm91cCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5wZXJtaXR2ZXJpZnksCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxM10gfHwgKF9jYWNoZVsxM10gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5wZXJtaXR2ZXJpZnkgPSAkZXZlbnQpCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3JhZGlvLCB7CiAgICAgICAgICAgIGxhYmVsOiAn5pivJwogICAgICAgICAgfSwgewogICAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi5pivIildKSwKICAgICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpbywgewogICAgICAgICAgICBsYWJlbDogJ+WQpicKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuWQpiIpXSksCiAgICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgICB9KV0pLAogICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbCJdKV0pLAogICAgXzogMSAvKiBTVEFCTEUgKi8KICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiZGl2IiwgbnVsbCwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2RpYWxvZywgewogICAgdGl0bGU6ICLkv67mlLnkuJrkuLvkv6Hmga8iLAogICAgbW9kZWxWYWx1ZTogJHNldHVwLmRpYWxvZ1Zpc2libGVVcGRhdGUsCiAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyOV0gfHwgKF9jYWNoZVsyOV0gPSAkZXZlbnQgPT4gJHNldHVwLmRpYWxvZ1Zpc2libGVVcGRhdGUgPSAkZXZlbnQpLAogICAgd2lkdGg6ICI1MCUiCiAgfSwgewogICAgZm9vdGVyOiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIF9ob2lzdGVkXzExLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgIG9uQ2xpY2s6IF9jYWNoZVsyOF0gfHwgKF9jYWNoZVsyOF0gPSAkZXZlbnQgPT4gJHNldHVwLmRpYWxvZ1Zpc2libGVVcGRhdGUgPSBmYWxzZSkKICAgIH0sIHsKICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuWPliDmtogiKV0pLAogICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICB0eXBlOiAicHJpbWFyeSIsCiAgICAgIG9uQ2xpY2s6ICRzZXR1cC5zYXZlVXBkYXRlCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKCLnoa4g5a6aIildKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pXSldKSwKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtLCB7CiAgICAgIG1vZGVsOiAkc2V0dXAuZm9ybS5kYXRhLAogICAgICByZWY6ICJmb3JtUmVmIiwKICAgICAgcnVsZXM6ICRzZXR1cC5ydWxlcywKICAgICAgImxhYmVsLXdpZHRoIjogIjEwMHB4IgogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLnnIHku70iLAogICAgICAgIHByb3A6ICJwcm92aW5jZSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9zZWxlY3QsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEucHJvdmluY2UsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxNl0gfHwgKF9jYWNoZVsxNl0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5wcm92aW5jZSA9ICRldmVudCksCiAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqeecgeS7vSIKICAgICAgICB9LCB7CiAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkc2V0dXAucHJvdmluY2VMaXN0LCBpdGVtID0+IHsKICAgICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfb3B0aW9uLCB7CiAgICAgICAgICAgICAga2V5OiBpdGVtLnByb3ZpbmNlLAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLnByb3ZpbmNlLAogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLnByb3ZpbmNlLAogICAgICAgICAgICAgIG9uQ2xpY2s6ICRzZXR1cC5jaGFuZ2VQcm92aW5jZQogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuWcsOW4giIsCiAgICAgICAgcHJvcDogImNpdHkiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLmNpdHksCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxN10gfHwgKF9jYWNoZVsxN10gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5jaXR5ID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5Zyw5biCIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5jaXR5TGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5jaXR5LAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmNpdHksCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uY2l0eSwKICAgICAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuY2hhbmdlQ2l0eQogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuWMuuWOvyIsCiAgICAgICAgcHJvcDogImRpc3RyaWN0IgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5kaXN0cmljdCwKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzE4XSB8fCAoX2NhY2hlWzE4XSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmRpc3RyaWN0ID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5Yy65Y6/IgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5kaXN0cmljdExpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0uZGlzdHJpY3QsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uZGlzdHJpY3QsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uZGlzdHJpY3QsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZURpc3RyaWN0CiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5bCP5Yy6IiwKICAgICAgICBwcm9wOiAiY29tbXVuaXR5IgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5jb21tdW5pdHksCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsxOV0gfHwgKF9jYWNoZVsxOV0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5jb21tdW5pdHkgPSAkZXZlbnQpLAogICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fpgInmi6nlsI/ljLoiCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gWyhfb3BlbkJsb2NrKHRydWUpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgbnVsbCwgX3JlbmRlckxpc3QoJHNldHVwLmNvbW11bml0eUxpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0uY29tbXVuaXR5LAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmNvbW11bml0eSwKICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5jb21tdW5pdHksCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZUNvbW11bml0eQogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIualvOagiyIsCiAgICAgICAgcHJvcDogImJ1aWxkaW5nIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3NlbGVjdCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5idWlsZGluZywKICAgICAgICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzIwXSB8fCAoX2NhY2hlWzIwXSA9ICRldmVudCA9PiAkc2V0dXAuZm9ybS5kYXRhLmJ1aWxkaW5nID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36YCJ5oup5qW85qCLIgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5idWlsZGluZ0xpc3QsIGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlQmxvY2soX2NvbXBvbmVudF9lbF9vcHRpb24sIHsKICAgICAgICAgICAgICBrZXk6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0uYnVpbGRpbmcsCiAgICAgICAgICAgICAgb25DbGljazogJHNldHVwLmNoYW5nZUJ1aWxkaW5nCiAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIFsibGFiZWwiLCAidmFsdWUiXSk7CiAgICAgICAgICB9KSwgMTI4IC8qIEtFWUVEX0ZSQUdNRU5UICovKSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5Y2V5YWDIiwKICAgICAgICBwcm9wOiAidW5pdHMiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLnVuaXRzLAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMjFdIHx8IChfY2FjaGVbMjFdID0gJGV2ZW50ID0+ICRzZXR1cC5mb3JtLmRhdGEudW5pdHMgPSAkZXZlbnQpLAogICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fpgInmi6nljZXlhYMiCiAgICAgICAgfSwgewogICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gWyhfb3BlbkJsb2NrKHRydWUpLCBfY3JlYXRlRWxlbWVudEJsb2NrKF9GcmFnbWVudCwgbnVsbCwgX3JlbmRlckxpc3QoJHNldHVwLnVuaXRzTGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS51bml0cywKICAgICAgICAgICAgICBsYWJlbDogaXRlbS51bml0cywKICAgICAgICAgICAgICB2YWx1ZTogaXRlbS51bml0cywKICAgICAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuY2hhbmdlVW5pdHMKICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJsYWJlbCIsICJ2YWx1ZSJdKTsKICAgICAgICAgIH0pLCAxMjggLyogS0VZRURfRlJBR01FTlQgKi8pKV0pLAogICAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgICB9LCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLmpbzlsYIiLAogICAgICAgIHByb3A6ICJmbG9vciIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9zZWxlY3QsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEuZmxvb3IsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyMl0gfHwgKF9jYWNoZVsyMl0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5mbG9vciA9ICRldmVudCksCiAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqealvOWxgiIKICAgICAgICB9LCB7CiAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbKF9vcGVuQmxvY2sodHJ1ZSksIF9jcmVhdGVFbGVtZW50QmxvY2soX0ZyYWdtZW50LCBudWxsLCBfcmVuZGVyTGlzdCgkc2V0dXAuZmxvb3JMaXN0LCBpdGVtID0+IHsKICAgICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfb3B0aW9uLCB7CiAgICAgICAgICAgICAga2V5OiBpdGVtLmZsb29yLAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmZsb29yLAogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmZsb29yLAogICAgICAgICAgICAgIG9uQ2xpY2s6ICRzZXR1cC5jaGFuZ2VGbG9vcgogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuaIv+WPtyIsCiAgICAgICAgcHJvcDogInJvb21udW1iZXIiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfc2VsZWN0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLnJvb21udW1iZXIsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyM10gfHwgKF9jYWNoZVsyM10gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5yb29tbnVtYmVyID0gJGV2ZW50KSwKICAgICAgICAgIHBsYWNlaG9sZGVyOiAi5oi/5Y+3IgogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5yb29tbnVtYmVyTGlzdCwgaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX29wdGlvbiwgewogICAgICAgICAgICAgIGtleTogaXRlbS5yb29tbnVtYmVyLAogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLnJvb21udW1iZXIsCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucm9vbW51bWJlcgogICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbImxhYmVsIiwgInZhbHVlIl0pOwogICAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpXSksCiAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIuS4muS4u+WQjeensCIsCiAgICAgICAgcHJvcDogIm93bmVybmFtZSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9pbnB1dCwgewogICAgICAgICAgbW9kZWxWYWx1ZTogJHNldHVwLmZvcm0uZGF0YS5vd25lcm5hbWUsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyNF0gfHwgKF9jYWNoZVsyNF0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5vd25lcm5hbWUgPSAkZXZlbnQpLAogICAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICAgIndpZHRoIjogIjgwJSIKICAgICAgICAgIH0KICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiXSldKSwKICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgbGFiZWw6ICLkuJrkuLvnlLXor50iLAogICAgICAgIHByb3A6ICJvd25lcnBob25lIgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLm93bmVycGhvbmUsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyNV0gfHwgKF9jYWNoZVsyNV0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5vd25lcnBob25lID0gJGV2ZW50KSwKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICJ3aWR0aCI6ICI4MCUiCiAgICAgICAgICB9CiAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVDb21tZW50Vk5vZGUoIiBsYWJlbD1cIui9pueJjOWPt1wiICAgICIpLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIui9pueJjOWPtyIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5mb3JtLmRhdGEuY2FyRGF0YXMsIChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgcmV0dXJuIF9vcGVuQmxvY2soKSwgX2NyZWF0ZUJsb2NrKF9jb21wb25lbnRfZWxfZm9ybV9pdGVtLCB7CiAgICAgICAgICAgIGtleTogaW5kZXgsCiAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgIm1hcmdpbi1yaWdodCI6ICIxNnB4IiwKICAgICAgICAgICAgICAiZGlzcGxheSI6ICJpbmxpbmUtYmxvY2siLAogICAgICAgICAgICAgICJtYXJnaW4tYm90dG9tIjogIjE4cHgiCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgICAgICAgIHByb3A6ICdjYXJEYXRhcy4nICsgaW5kZXggKyAnLmRhdGEnLAogICAgICAgICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXovabniYzlj7cnLAogICAgICAgICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2lucHV0LCB7CiAgICAgICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgICAgICAid2lkdGgiOiAiMTk0cHgiCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgdHlwZTogInRleHQiLAogICAgICAgICAgICAgICAgbW9kZWxWYWx1ZTogaXRlbS5kYXRhLAogICAgICAgICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiAkZXZlbnQgPT4gaXRlbS5kYXRhID0gJGV2ZW50LAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fovpPlhaXovabniYzlj7ciCiAgICAgICAgICAgICAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIiwgIm9uVXBkYXRlOm1vZGVsVmFsdWUiXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImltZyIsIHsKICAgICAgICAgICAgICAgIHNyYzogX2ltcG9ydHNfMSwKICAgICAgICAgICAgICAgIG9uQ2xpY2s6ICRldmVudCA9PiAkc2V0dXAuZGVsZXRlQ2FyKGluZGV4KSwKICAgICAgICAgICAgICAgIGFsdDogIiIsCiAgICAgICAgICAgICAgICBjbGFzczogImRlbC1jYXJDb2RlIgogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzkpXSksCiAgICAgICAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgICAgICAgIH0sIDEwMzIgLyogUFJPUFMsIERZTkFNSUNfU0xPVFMgKi8sIFsicHJvcCJdKV0pLAogICAgICAgICAgICBfOiAyIC8qIERZTkFNSUMgKi8KICAgICAgICAgIH0sIDEwMjQgLyogRFlOQU1JQ19TTE9UUyAqLyk7CiAgICAgICAgfSksIDEyOCAvKiBLRVlFRF9GUkFHTUVOVCAqLykpLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJpbWciLCB7CiAgICAgICAgICBzcmM6IF9pbXBvcnRzXzIsCiAgICAgICAgICBvbkNsaWNrOiAkc2V0dXAuYWRkQ2FyLAogICAgICAgICAgYWx0OiAiIiwKICAgICAgICAgIGNsYXNzOiAiYWRkQ2FyQ29kZSIKICAgICAgICB9KV0pLAogICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgIH0pLCBfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICBsYWJlbDogIui9puS9jeWPt+eggSIKICAgICAgfSwgewogICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFsoX29wZW5CbG9jayh0cnVlKSwgX2NyZWF0ZUVsZW1lbnRCbG9jayhfRnJhZ21lbnQsIG51bGwsIF9yZW5kZXJMaXN0KCRzZXR1cC5mb3JtLmRhdGEucGFya2luZ0RhdGFzLCAoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICAgIHJldHVybiBfb3BlbkJsb2NrKCksIF9jcmVhdGVCbG9jayhfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgICJtYXJnaW4tcmlnaHQiOiAiMTZweCIsCiAgICAgICAgICAgICAgImRpc3BsYXkiOiAiaW5saW5lLWJsb2NrIiwKICAgICAgICAgICAgICAibWFyZ2luLWJvdHRvbSI6ICIxOHB4IgogICAgICAgICAgICB9CiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9mb3JtX2l0ZW0sIHsKICAgICAgICAgICAgICBwcm9wOiAncGFya2luZ0RhdGFzLicgKyBpbmRleCArICcuZGF0YScsCiAgICAgICAgICAgICAgcnVsZXM6IHsKICAgICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpei9puS9jeWPt+eggScsCiAgICAgICAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfaW5wdXQsIHsKICAgICAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgICAgICJ3aWR0aCI6ICIxOTRweCIKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB0eXBlOiAidGV4dCIsCiAgICAgICAgICAgICAgICBtb2RlbFZhbHVlOiBpdGVtLmRhdGEsCiAgICAgICAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6ICRldmVudCA9PiBpdGVtLmRhdGEgPSAkZXZlbnQsCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+i+k+WFpei9puS9jeWPt+eggSIKICAgICAgICAgICAgICB9LCBudWxsLCA4IC8qIFBST1BTICovLCBbIm1vZGVsVmFsdWUiLCAib25VcGRhdGU6bW9kZWxWYWx1ZSJdKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW1nIiwgewogICAgICAgICAgICAgICAgc3JjOiBfaW1wb3J0c18xLAogICAgICAgICAgICAgICAgb25DbGljazogJGV2ZW50ID0+ICRzZXR1cC5kZWxldGVQYXJraW5nKGluZGV4KSwKICAgICAgICAgICAgICAgIGFsdDogIiIsCiAgICAgICAgICAgICAgICBjbGFzczogImRlbC1jYXJDb2RlIgogICAgICAgICAgICAgIH0sIG51bGwsIDggLyogUFJPUFMgKi8sIF9ob2lzdGVkXzEwKV0pLAogICAgICAgICAgICAgIF86IDIgLyogRFlOQU1JQyAqLwogICAgICAgICAgICB9LCAxMDMyIC8qIFBST1BTLCBEWU5BTUlDX1NMT1RTICovLCBbInByb3AiXSldKSwKICAgICAgICAgICAgXzogMiAvKiBEWU5BTUlDICovCiAgICAgICAgICB9LCAxMDI0IC8qIERZTkFNSUNfU0xPVFMgKi8pOwogICAgICAgIH0pLCAxMjggLyogS0VZRURfRlJBR01FTlQgKi8pKSwgX2NyZWF0ZUVsZW1lbnRWTm9kZSgiaW1nIiwgewogICAgICAgICAgc3JjOiBfaW1wb3J0c18yLAogICAgICAgICAgb25DbGljazogJHNldHVwLmFkZFBhcmtpbmcsCiAgICAgICAgICBhbHQ6ICIiLAogICAgICAgICAgY2xhc3M6ICJhZGRDYXJDb2RlIgogICAgICAgIH0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5piv5ZCm5a6h5om5IgogICAgICB9LCB7CiAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX3JhZGlvX2dyb3VwLCB7CiAgICAgICAgICBtb2RlbFZhbHVlOiAkc2V0dXAuZm9ybS5kYXRhLmlzYXVkaXQsCiAgICAgICAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVsyNl0gfHwgKF9jYWNoZVsyNl0gPSAkZXZlbnQgPT4gJHNldHVwLmZvcm0uZGF0YS5pc2F1ZGl0ID0gJGV2ZW50KQogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpbywgewogICAgICAgICAgICBsYWJlbDogJ+aYrycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuaYryIpXSksCiAgICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfcmFkaW8sIHsKICAgICAgICAgICAgbGFiZWw6ICflkKYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKCLlkKYiKV0pLAogICAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgICAgfSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSksIF9jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2Zvcm1faXRlbSwgewogICAgICAgIGxhYmVsOiAi5piv5ZCm5byA5ZCv6Ieq5Yqp6aKE57qmIiwKICAgICAgICAibGFiZWwtd2lkdGgiOiAiMTUwcHgiCiAgICAgIH0sIHsKICAgICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfcmFkaW9fZ3JvdXAsIHsKICAgICAgICAgIG1vZGVsVmFsdWU6ICRzZXR1cC5mb3JtLmRhdGEucGVybWl0dmVyaWZ5LAogICAgICAgICAgIm9uVXBkYXRlOm1vZGVsVmFsdWUiOiBfY2FjaGVbMjddIHx8IChfY2FjaGVbMjddID0gJGV2ZW50ID0+ICRzZXR1cC5mb3JtLmRhdGEucGVybWl0dmVyaWZ5ID0gJGV2ZW50KQogICAgICAgIH0sIHsKICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9yYWRpbywgewogICAgICAgICAgICBsYWJlbDogJ+aYrycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgZGVmYXVsdDogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVUZXh0Vk5vZGUoIuaYryIpXSksCiAgICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgICB9KSwgX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfcmFkaW8sIHsKICAgICAgICAgICAgbGFiZWw6ICflkKYnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKCLlkKYiKV0pLAogICAgICAgICAgICBfOiAxIC8qIFNUQUJMRSAqLwogICAgICAgICAgfSldKSwKICAgICAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICAgICAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksCiAgICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgICAgfSldKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWwiXSldKSwKICAgIF86IDEgLyogU1RBQkxFICovCiAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSksIF9jcmVhdGVFbGVtZW50Vk5vZGUoImRpdiIsIG51bGwsIFtfY3JlYXRlVk5vZGUoX2NvbXBvbmVudF9lbF9kaWFsb2csIHsKICAgIHRpdGxlOiAi5p+l55yL5a6h5qC45Y6f5ZugIiwKICAgIG1vZGVsVmFsdWU6ICRzZXR1cC52aWV3U2hvdywKICAgICJvblVwZGF0ZTptb2RlbFZhbHVlIjogX2NhY2hlWzMxXSB8fCAoX2NhY2hlWzMxXSA9ICRldmVudCA9PiAkc2V0dXAudmlld1Nob3cgPSAkZXZlbnQpCiAgfSwgewogICAgZm9vdGVyOiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIF9ob2lzdGVkXzEzLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfYnV0dG9uLCB7CiAgICAgIG9uQ2xpY2s6IF9jYWNoZVszMF0gfHwgKF9jYWNoZVszMF0gPSAkZXZlbnQgPT4gJHNldHVwLnZpZXdTaG93ID0gZmFsc2UpCiAgICB9LCB7CiAgICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlVGV4dFZOb2RlKCLlj5Yg5raIIildKSwKICAgICAgXzogMSAvKiBTVEFCTEUgKi8KICAgIH0pXSldKSwKICAgIGRlZmF1bHQ6IF93aXRoQ3R4KCgpID0+IFtfY3JlYXRlRWxlbWVudFZOb2RlKCJzcGFuIiwgX2hvaXN0ZWRfMTIsIF90b0Rpc3BsYXlTdHJpbmcoJHNldHVwLmNvbnRlbnQpLCAxIC8qIFRFWFQgKi8pXSksCiAgICBfOiAxIC8qIFNUQUJMRSAqLwogIH0sIDggLyogUFJPUFMgKi8sIFsibW9kZWxWYWx1ZSJdKV0pLCBfY3JlYXRlRWxlbWVudFZOb2RlKCJkaXYiLCBudWxsLCBbX2NyZWF0ZVZOb2RlKF9jb21wb25lbnRfZWxfZGlhbG9nLCB7CiAgICB0aXRsZTogIuafpeeci+WbvueJhyIsCiAgICBtb2RlbFZhbHVlOiAkc2V0dXAudmlld1Nob3csCiAgICAib25VcGRhdGU6bW9kZWxWYWx1ZSI6IF9jYWNoZVszM10gfHwgKF9jYWNoZVszM10gPSAkZXZlbnQgPT4gJHNldHVwLnZpZXdTaG93ID0gJGV2ZW50KQogIH0sIHsKICAgIGZvb3RlcjogX3dpdGhDdHgoKCkgPT4gW19jcmVhdGVFbGVtZW50Vk5vZGUoInNwYW4iLCBfaG9pc3RlZF8xNSwgW19jcmVhdGVWTm9kZShfY29tcG9uZW50X2VsX2J1dHRvbiwgewogICAgICBvbkNsaWNrOiBfY2FjaGVbMzJdIHx8IChfY2FjaGVbMzJdID0gJGV2ZW50ID0+ICRzZXR1cC52aWV3U2hvdyA9IGZhbHNlKQogICAgfSwgewogICAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZVRleHRWTm9kZSgi5Y+WIOa2iCIpXSksCiAgICAgIF86IDEgLyogU1RBQkxFICovCiAgICB9KV0pXSksCiAgICBkZWZhdWx0OiBfd2l0aEN0eCgoKSA9PiBbX2NyZWF0ZUVsZW1lbnRWTm9kZSgic3BhbiIsIF9ob2lzdGVkXzE0LCBfdG9EaXNwbGF5U3RyaW5nKCRzZXR1cC5jb250ZW50MSksIDEgLyogVEVYVCAqLyldKSwKICAgIF86IDEgLyogU1RBQkxFICovCiAgfSwgOCAvKiBQUk9QUyAqLywgWyJtb2RlbFZhbHVlIl0pXSldKTsKfQ=="}, {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "_createElementVNode", "src", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_form", "inline", "model", "$setup", "query", "_component_el_form_item", "label", "_component_el_input", "community", "$event", "placeholder", "clearable", "ownername", "_component_el_button", "type", "icon", "onClick", "handleSearch", "handleAdd", "_component_el_upload", "ref", "action", "accept", "onUpload", "limit", "_ctx", "handleExceed", "onErrorFile", "onSuccessFile", "fileList", "name", "_component_el_table", "data", "tableData", "border", "cellStyle", "tableRowClassName", "_Fragment", "_renderList", "props", "item", "_component_el_table_column", "prop", "key", "align", "width", "fixed", "default", "_withCtx", "scope", "handleEdit", "row", "handleDelete", "$index", "id", "_hoisted_5", "_component_el_pagination", "currentPage", "pageNum", "pageSize", "layout", "total", "pageTotal", "onSizeChange", "handleSizeChange", "onCurrentChange", "handlePageChange", "_component_el_dialog", "title", "dialogVisible", "footer", "_hoisted_8", "_cache", "save", "form", "rules", "_component_el_select", "province", "provinceList", "_createBlock", "_component_el_option", "value", "changeProvince", "city", "cityList", "changeCity", "district", "districtList", "changeDistrict", "communityList", "changeCommunity", "building", "buildingList", "changeBuilding", "units", "unitsList", "changeUnits", "floor", "floorList", "changeFloor", "roomnumber", "roomnumberList", "ownerphone", "_createCommentVNode", "carDatas", "index", "required", "message", "trigger", "deleteCar", "alt", "addCar", "parkingDatas", "deleteParking", "addParking", "_component_el_radio_group", "<PERSON><PERSON><PERSON>", "_component_el_radio", "permitverify", "dialogVisibleUpdate", "_hoisted_11", "saveUpdate", "viewShow", "_hoisted_13", "_hoisted_12", "_toDisplayString", "content", "_hoisted_15", "_hoisted_14", "content1"], "sources": ["F:\\ParkingDemoAKEHRBU\\ParkingManageDemo\\manage-front\\src\\views\\admin\\OwnerInfo.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/OwnerInfo.svg\"></i> 业主管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n                        <el-input v-model=\"query.community\" placeholder=\"小区名称\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"业主姓名\">\r\n                        <el-input v-model=\"query.ownername\" placeholder=\"业主姓名\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\">搜索\r\n                    </el-button>\r\n                    <el-button type=\"primary\" class=\"addButton\" @click=\"handleAdd\">新增\r\n                    </el-button>\r\n                    <el-upload ref=\"upload\" class=\"upload-demo\" action=\"\" accept=\".xls,.xlsx\" :on-change=\"onUpload\"\r\n                        :limit=\"1\" :on-exceed=\"handleExceed\" :on-error=\"onErrorFile\" :on-success=\"onSuccessFile\"\r\n                        :auto-upload=\"false\" :file-list=\"fileList\" :show-file-list=\"false\" name=\"file\">\r\n                        <el-button class=\"uploadButton\" type=\"primary\">选择文件</el-button>\r\n                    </el-upload>\r\n\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in props\" :key=\"item.prop\" align=\"center\">\r\n                </el-table-column>\r\n\r\n\r\n                <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\">编辑\r\n                        </el-button>\r\n                        <el-button type=\"text\" icon=\"el-icon-delete\" class=\"red\"\r\n                            @click=\"handleDelete(scope.$index, scope.row.id)\">删除\r\n                        </el-button>\r\n\r\n                    </template>\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"增加业主信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"省份\" prop=\"province\">\r\n                        <el-select v-model=\"form.data.province\" placeholder=\"请选择省份\">\r\n                            <el-option v-for=\"item in provinceList\" :key=\"item.province\" :label=\"item.province\"\r\n                                :value=\"item.province\" @click=\"changeProvince\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"地市\" prop=\"city\">\r\n                        <el-select v-model=\"form.data.city\" placeholder=\"请选择地市\">\r\n                            <el-option v-for=\"item in cityList\" :key=\"item.city\" :label=\"item.city\" :value=\"item.city\"\r\n                                @click=\"changeCity\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"区县\" prop=\"district\">\r\n                        <el-select v-model=\"form.data.district\" placeholder=\"请选择区县\">\r\n                            <el-option v-for=\"item in districtList\" :key=\"item.district\" :label=\"item.district\"\r\n                                :value=\"item.district\" @click=\"changeDistrict\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"小区\" prop=\"community\">\r\n                        <el-select v-model=\"form.data.community\" placeholder=\"请选择小区\">\r\n                            <el-option v-for=\"item in communityList\" :key=\"item.community\" :label=\"item.community\"\r\n                                :value=\"item.community\" @click=\"changeCommunity\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼栋\" prop=\"building\">\r\n                        <el-select v-model=\"form.data.building\" placeholder=\"请选择楼栋\">\r\n                            <el-option v-for=\"item in buildingList\" :key=\"item.building\" :label=\"item.building\"\r\n                                :value=\"item.building\" @click=\"changeBuilding\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单元\" prop=\"units\">\r\n                        <el-select v-model=\"form.data.units\" placeholder=\"请选择单元\">\r\n                            <el-option v-for=\"item in unitsList\" :key=\"item.units\" :label=\"item.units\"\r\n                                :value=\"item.units\" @click=\"changeUnits\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼层\" prop=\"floor\">\r\n                        <el-select v-model=\"form.data.floor\" placeholder=\"请选择楼层\">\r\n                            <el-option v-for=\"item in floorList\" :key=\"item.floor\" :label=\"item.floor\"\r\n                                :value=\"item.floor\" @click=\"changeFloor\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"房号\" prop=\"roomnumber\">\r\n                        <el-select v-model=\"form.data.roomnumber\" placeholder=\"房号\">\r\n                            <el-option v-for=\"item in roomnumberList\" :key=\"item.roomnumber\" :label=\"item.roomnumber\"\r\n                                :value=\"item.roomnumber\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主名称\" prop=\"ownername\">\r\n                        <el-input v-model=\"form.data.ownername\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主电话\" prop=\"ownerphone\">\r\n                        <el-input v-model=\"form.data.ownerphone\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <!-- label=\"车牌号\"    -->\r\n                    <el-form-item label=\"车牌号\">\r\n                        <el-form-item v-for=\"(item, index) in form.data.carDatas\" :key=\"index\"\r\n                            style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'carDatas.' + index + '.data'\"\r\n                                :rules=\"{ required: true, message: '请输入车牌号', trigger: 'blur' }\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车牌号\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteCar(index)\" alt=\"\"\r\n                                    class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addCar\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车位号码\">\r\n                        <el-form-item v-for=\"(item, index) in form.data.parkingDatas\" :key=\"index\"\r\n                            style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'parkingDatas.' + index + '.data'\"\r\n                                :rules=\"{ required: true, message: '请输入车位号码', trigger: 'blur' }\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车位号码\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteParking(index)\" alt=\"\"\r\n                                    class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addParking\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"是否审批\">\r\n                        <el-radio-group v-model=\"form.data.isaudit\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"是否开启自助预约\" label-width=\"150px\">\r\n                        <el-radio-group v-model=\"form.data.permitverify\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"修改业主信息\" v-model=\"dialogVisibleUpdate\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"省份\" prop=\"province\">\r\n                        <el-select v-model=\"form.data.province\" placeholder=\"请选择省份\">\r\n                            <el-option v-for=\"item in provinceList\" :key=\"item.province\" :label=\"item.province\"\r\n                                :value=\"item.province\" @click=\"changeProvince\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"地市\" prop=\"city\">\r\n                        <el-select v-model=\"form.data.city\" placeholder=\"请选择地市\">\r\n                            <el-option v-for=\"item in cityList\" :key=\"item.city\" :label=\"item.city\" :value=\"item.city\"\r\n                                @click=\"changeCity\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"区县\" prop=\"district\">\r\n                        <el-select v-model=\"form.data.district\" placeholder=\"请选择区县\">\r\n                            <el-option v-for=\"item in districtList\" :key=\"item.district\" :label=\"item.district\"\r\n                                :value=\"item.district\" @click=\"changeDistrict\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"小区\" prop=\"community\">\r\n                        <el-select v-model=\"form.data.community\" placeholder=\"请选择小区\">\r\n                            <el-option v-for=\"item in communityList\" :key=\"item.community\" :label=\"item.community\"\r\n                                :value=\"item.community\" @click=\"changeCommunity\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼栋\" prop=\"building\">\r\n                        <el-select v-model=\"form.data.building\" placeholder=\"请选择楼栋\">\r\n                            <el-option v-for=\"item in buildingList\" :key=\"item.building\" :label=\"item.building\"\r\n                                :value=\"item.building\" @click=\"changeBuilding\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单元\" prop=\"units\">\r\n                        <el-select v-model=\"form.data.units\" placeholder=\"请选择单元\">\r\n                            <el-option v-for=\"item in unitsList\" :key=\"item.units\" :label=\"item.units\"\r\n                                :value=\"item.units\" @click=\"changeUnits\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"楼层\" prop=\"floor\">\r\n                        <el-select v-model=\"form.data.floor\" placeholder=\"请选择楼层\">\r\n                            <el-option v-for=\"item in floorList\" :key=\"item.floor\" :label=\"item.floor\"\r\n                                :value=\"item.floor\" @click=\"changeFloor\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"房号\" prop=\"roomnumber\">\r\n                        <el-select v-model=\"form.data.roomnumber\" placeholder=\"房号\">\r\n                            <el-option v-for=\"item in roomnumberList\" :key=\"item.roomnumber\" :label=\"item.roomnumber\"\r\n                                :value=\"item.roomnumber\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主名称\" prop=\"ownername\">\r\n                        <el-input v-model=\"form.data.ownername\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"业主电话\" prop=\"ownerphone\">\r\n                        <el-input v-model=\"form.data.ownerphone\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <!-- label=\"车牌号\"    -->\r\n                    <el-form-item label=\"车牌号\">\r\n                        <el-form-item v-for=\"(item, index) in form.data.carDatas\" :key=\"index\"\r\n                            style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'carDatas.' + index + '.data'\"\r\n                                :rules=\"{ required: true, message: '请输入车牌号', trigger: 'blur' }\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车牌号\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteCar(index)\" alt=\"\"\r\n                                    class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addCar\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车位号码\">\r\n                        <el-form-item v-for=\"(item, index) in form.data.parkingDatas\" :key=\"index\"\r\n                            style=\"  margin-right: 16px; display: inline-block; margin-bottom: 18px;\">\r\n                            <el-form-item :prop=\"'parkingDatas.' + index + '.data'\"\r\n                                :rules=\"{ required: true, message: '请输入车位号码', trigger: 'blur' }\">\r\n                                <el-input style=\"width: 194px;\" type=\"text\" v-model=\"item.data\" placeholder=\"请输入车位号码\">\r\n                                </el-input>\r\n                                <img src=\"@/assets/img/del-carCode.svg\" @click=\"deleteParking(index)\" alt=\"\"\r\n                                    class=\"del-carCode\">\r\n                            </el-form-item>\r\n                        </el-form-item>\r\n                        <img src=\"@/assets/img/addCarCode.svg\" @click=\"addParking\" alt=\"\" class=\"addCarCode\">\r\n                    </el-form-item>\r\n                    <el-form-item label=\"是否审批\">\r\n                        <el-radio-group v-model=\"form.data.isaudit\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"是否开启自助预约\" label-width=\"150px\">\r\n                        <el-radio-group v-model=\"form.data.permitverify\">\r\n                            <el-radio :label=\"'是'\">是</el-radio>\r\n                            <el-radio :label=\"'否'\">否</el-radio>\r\n                        </el-radio-group>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\" dialogVisibleUpdate = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"saveUpdate\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"查看审核原因\" v-model=\"viewShow\">\r\n                <span style=\"margin-left: 50px\">{{ content }}</span>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"viewShow = false\">取 消</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"查看图片\" v-model=\"viewShow\">\r\n                <span style=\"margin-left: 50px\">{{ content1 }}</span>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"viewShow = false\">取 消</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\n\r\nconst root = \"/parking/ownerinfo/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    { label: \"省份\", prop: \"province\" },\r\n    { label: \"地市\", prop: \"city\" },\r\n    { label: \"县区\", prop: \"district\" },\r\n    { label: \"小区\", prop: \"community\" },\r\n    { label: \"栋号\", prop: \"building\" },\r\n    { label: \"单元\", prop: \"units\" },\r\n    { label: \"楼层\", prop: \"floor\" },\r\n    { label: \"房号\", prop: \"roomnumber\" },\r\n    { label: \"业主姓名\", prop: \"ownername\" },\r\n    { label: \"业主电话\", prop: \"ownerphone\" },\r\n    { label: \"是否审批\", prop: \"isaudit\" },\r\n    { label: \"允许验证\", prop: \"permitverify\" },\r\n    { label: \"车牌号码\", prop: \"plates\" },\r\n    { label: \"车位号码\", prop: \"parkingspaces\" },\r\n];\r\n\r\nconst rules = {\r\n    province: [\r\n        {\r\n            required: true,\r\n            message: \"请选择省份\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    city: [\r\n        {\r\n            required: true,\r\n            message: \"请选择地市\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    district: [\r\n        {\r\n            required: true,\r\n            message: \"请选择县区\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    community: [\r\n        {\r\n            required: true,\r\n            message: \"请选择校区\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    building: [\r\n        {\r\n            required: true,\r\n            message: \"请选择楼栋\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    units: [\r\n        {\r\n            required: true,\r\n            message: \"请选择单元\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    floor: [\r\n        {\r\n            required: true,\r\n            message: \"请选择楼层\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    roomnumber: [\r\n        {\r\n            required: true,\r\n            message: \"请选择房号\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    ownername: [\r\n        {\r\n            required: true,\r\n            message: \"请输入业主姓名\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    ownerphone: [\r\n        { required: true, message: \"请输入业主电话\", trigger: \"blur\" },\r\n    ],\r\n};\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        province: '',\r\n        city: '',\r\n        district: '',\r\n        community: '',\r\n        building: '',\r\n        units: '',\r\n        floor: '',\r\n        roomnumber: '',\r\n        ownername: '',\r\n        ownerphone: '',\r\n        isaudit: '',\r\n        permitverify: '',\r\n        plates: '',\r\n        parkingspaces: '',\r\n        carDatas: [\r\n            {\r\n                id: 0,\r\n                data: ''\r\n            }\r\n        ],\r\n        parkingDatas: [\r\n            {\r\n                id: 0,\r\n                data: ''\r\n            }\r\n        ]\r\n    },\r\n\r\n});\r\n\r\nconst handleExport = () => {\r\n    window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.province = ''\r\n    form.data.city = ''\r\n    form.data.district = ''\r\n    form.data.community = ''\r\n    form.data.building = ''\r\n    form.data.units = ''\r\n    form.data.floor = ''\r\n    form.data.roomnumber = ''\r\n    form.data.ownername = ''\r\n    form.data.ownerphone = ''\r\n    form.data.isaudit = ''\r\n    form.data.permitverify = ''\r\n    form.data.plates = ''\r\n    form.data.parkingspaces = ''\r\n    form.data.carDatas = []\r\n    form.data.carDatas.push({\r\n        id: 0,\r\n        data: ''\r\n    }\r\n    )\r\n    form.data.parkingDatas = []\r\n    form.data.parkingDatas.push(\r\n        {\r\n            id: 0,\r\n            data: ''\r\n        }\r\n    )\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\nconst handleView = (row) => {\r\n    console.log(\"这批我\")\r\n    if (row.fileReason !== null) {\r\n        viewShow.value = true\r\n        content.value = row.fileReason\r\n    } else {\r\n        ElMessage.info('没有审核原因');\r\n    }\r\n};\r\nconst viewShow1 = ref(false)\r\nconst content1 = ref(\"\");\r\nconst handleView1 = (row) => {\r\n    console.log(\"这批我\")\r\n    if (row.purchaseVoucher !== null) {\r\n        viewShow.value = true\r\n        content1.value = row.purchaseVoucher\r\n    } else {\r\n        ElMessage.info('没有审核原因');\r\n    }\r\n};\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n// alert(applicantUserId.value)\r\nconst departmentList = ref([]);\r\nrequest.get(\"/parking/department/listDepartment\").then((res) => {\r\n    departmentList.value = res.data;\r\n});\r\nconst query = reactive({\r\n    community: \"\",\r\n    ownername: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst dialogVisibleUpdate = ref(false)\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\n\r\n\r\n// 获取表格数据\r\n\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"querypage\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n        });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n        type: \"warning\",\r\n    })\r\n        .then(() => {\r\n            request.delete(root + sid).then((res) => {\r\n                if (res.data) {\r\n                    ElMessage.success(\"删除成功\");\r\n                    tableData.value.splice(index, 1);\r\n                } else {\r\n                    ElMessage.error(\"删除失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n\r\n// 新增操作\r\nconst handleAdd = () => {\r\n    dialogVisible.value = true;\r\n    onReset();\r\n    form.data.isaudit = '是';\r\n    form.data.permitverify = '是';\r\n\r\n\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n    dialogVisibleUpdate.value = true\r\n    form.data.id = row.id\r\n    form.data.province = row.province\r\n    form.data.city = row.city\r\n    form.data.district = row.district\r\n    form.data.community = row.community\r\n    form.data.building = row.building\r\n    form.data.units = row.units\r\n    form.data.floor = row.floor\r\n    form.data.roomnumber = row.roomnumber\r\n    form.data.ownername = row.ownername\r\n    form.data.ownerphone = row.ownerphone\r\n    form.data.isaudit = row.isaudit\r\n    form.data.permitverify = row.permitverify\r\n    var carArr = []\r\n    var parkingArr = []\r\n    carArr = row.plates.split(',')\r\n    parkingArr = row.parkingspaces.split(',')\r\n    form.data.carDatas = []\r\n    form.data.parkingDatas = []\r\n    dataNum.value = 0\r\n    dataParkingNum.value = 0\r\n    for (let i = 0; i < carArr.length; i++) {\r\n        form.data.carDatas.push(\r\n            // 增加就push进数组一个新值\r\n            {\r\n                id: dataNum.value++,\r\n                data: carArr[i]\r\n            }\r\n        )\r\n    }\r\n    for (let i = 0; i < parkingArr.length; i++) {\r\n        form.data.parkingDatas.push(\r\n            // 增加就push进数组一个新值\r\n            {\r\n                id: dataParkingNum.value++,\r\n                data: parkingArr[i]\r\n            }\r\n        )\r\n    }\r\n};\r\nconst provinceList = ref([]);\r\nconst cityList = ref([]);\r\nconst districtList = ref([]);\r\nconst communityList = ref([]);\r\nconst buildingList = ref([]);\r\nconst unitsList = ref([]);\r\nconst floorList = ref([]);\r\nconst roomnumberList = ref([\r\n    {\r\n        roomnumber: 1,\r\n    },\r\n    {\r\n        roomnumber: 2,\r\n    },\r\n    {\r\n        roomnumber: 3,\r\n    },\r\n    {\r\n        roomnumber: 4,\r\n    },\r\n]);\r\nrequest.get(\"/parking/community/province\").then((res) => {\r\n    provinceList.value = res.data;\r\n});\r\nconst changeProvince = () => {\r\n    request\r\n        .get(\"/parking/community/city\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            cityList.value = res.data;\r\n            form.data.city = \"\";\r\n            form.data.district = \"\";\r\n            form.data.community = \"\";\r\n            form.data.building = \"\";\r\n            form.data.units = \"\";\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeCity = () => {\r\n    console.log(form.data.province);\r\n    request\r\n        .get(\"/parking/community/district\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            districtList.value = res.data;\r\n            form.data.district = \"\";\r\n            form.data.community = \"\";\r\n            form.data.building = \"\";\r\n            form.data.units = \"\";\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeDistrict = () => {\r\n    request\r\n        .get(\"/parking/community/community\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            communityList.value = res.data;\r\n            form.data.community = \"\";\r\n            form.data.building = \"\";\r\n            form.data.units = \"\";\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeCommunity = () => {\r\n    request\r\n        .get(\"/parking/community/building\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                    community: form.data.community,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            buildingList.value = res.data;\r\n            form.data.building = \"\";\r\n            form.data.units = \"\";\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeBuilding = () => {\r\n    request\r\n        .get(\"/parking/community/units\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                    community: form.data.community,\r\n                    building: form.data.building,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            unitsList.value = res.data;\r\n            form.data.units = \"\";\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeUnits = () => {\r\n    request\r\n        .get(\"/parking/community/floor\",\r\n            {\r\n                params: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                    community: form.data.community,\r\n                    building: form.data.building,\r\n                    units: form.data.units,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            floorList.value = res.data;\r\n            form.data.floor = \"\";\r\n            form.data.roomnumber = \"\";\r\n        });\r\n\r\n};\r\nconst changeFloor = () => {\r\n\r\n    form.data.roomnumber = \"\";\r\n\r\n};\r\n\r\nconst formRef = ref(null);\r\nconst save = () => {\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            //车牌处理\r\n            var carstr = ''\r\n            var parkingstr = ''\r\n            var find = false;\r\n            for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                for (let j = i + 1; j < form.data.carDatas.length; j++) {\r\n                    if (form.data.carDatas[i].data == form.data.carDatas[j].data) {\r\n                        find = true;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if (find) {\r\n                ElMessage.warning(\"车牌号有重复！\");\r\n                return false;\r\n            }\r\n            for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                if (carstr == '') carstr = form.data.carDatas[i].data\r\n                else carstr = carstr + ',' + form.data.carDatas[i].data\r\n            }\r\n            //车位编号处理\r\n            for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                for (let j = i + 1; j < form.data.parkingDatas.length; j++) {\r\n                    if (form.data.parkingDatas[i].data == form.data.parkingDatas[j].data) {\r\n                        find = true;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if (find) {\r\n                ElMessage.warning(\"车位编号有重复！\");\r\n                return false;\r\n            }\r\n            for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                if (parkingstr == '') parkingstr = form.data.parkingDatas[i].data\r\n                else parkingstr = parkingstr + ',' + form.data.parkingDatas[i].data\r\n            }\r\n            form.data.plates = carstr\r\n            form.data.parkingspaces = parkingstr\r\n            var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n            request({\r\n                url: \"/parking/ownerinfo\",\r\n                method: method,\r\n                data: {\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                    community: form.data.community,\r\n                    building: form.data.building,\r\n                    units: form.data.units,\r\n                    floor: form.data.floor,\r\n                    roomnumber: form.data.roomnumber,\r\n                    ownername: form.data.ownername,\r\n                    ownerphone: form.data.ownerphone,\r\n                    isaudit: form.data.isaudit,\r\n                    permitverify: form.data.permitverify,\r\n                    plates: form.data.plates,\r\n                    parkingspaces: form.data.parkingspaces,\r\n                },\r\n            }).then((res) => {\r\n                console.log(res)\r\n                if (res.data.code == 0) {\r\n                    getData()\r\n                    ElMessage.success(\"提交成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisible.value = false\r\n                    form.data = {}\r\n                } else {\r\n                    // dialogVisible.value = false\r\n                    ElMessage.error(res.data.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\nconst saveUpdate = () => {\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            //车牌处理\r\n            var carstr = ''\r\n            var parkingstr = ''\r\n            var find = false;\r\n            for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                for (let j = i + 1; j < form.data.carDatas.length; j++) {\r\n                    if (form.data.carDatas[i].data == form.data.carDatas[j].data) {\r\n                        find = true;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if (find) {\r\n                ElMessage.warning(\"车牌号有重复！\");\r\n                return false;\r\n            }\r\n            for (let i = 0; i < form.data.carDatas.length; i++) {\r\n                if (carstr == '') carstr = form.data.carDatas[i].data\r\n                else carstr = carstr + ',' + form.data.carDatas[i].data\r\n            }\r\n            //车位编号处理\r\n            for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                for (let j = i + 1; j < form.data.parkingDatas.length; j++) {\r\n                    if (form.data.parkingDatas[i].data == form.data.parkingDatas[j].data) {\r\n                        find = true;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            if (find) {\r\n                ElMessage.warning(\"车位编号有重复！\");\r\n                return false;\r\n            }\r\n            for (let i = 0; i < form.data.parkingDatas.length; i++) {\r\n                if (parkingstr == '') parkingstr = form.data.parkingDatas[i].data\r\n                else parkingstr = parkingstr + ',' + form.data.parkingDatas[i].data\r\n            }\r\n            form.data.plates = carstr\r\n            form.data.parkingspaces = parkingstr\r\n            request({\r\n                url: \"/parking/ownerinfo/update\",\r\n                method: \"POST\",\r\n                data: {\r\n                    id:form.data.id,\r\n                    province: form.data.province,\r\n                    city: form.data.city,\r\n                    district: form.data.district,\r\n                    community: form.data.community,\r\n                    building: form.data.building,\r\n                    units: form.data.units,\r\n                    floor: form.data.floor,\r\n                    roomnumber: form.data.roomnumber,\r\n                    ownername: form.data.ownername,\r\n                    ownerphone: form.data.ownerphone,\r\n                    isaudit: form.data.isaudit,\r\n                    permitverify: form.data.permitverify,\r\n                    plates: form.data.plates,\r\n                    parkingspaces: form.data.parkingspaces,\r\n                },\r\n            }).then((res) => {\r\n                // console.log(\r\n                console.log(res.data)\r\n                if (res.data.code == 0) {\r\n                    getData()\r\n                    ElMessage.success(\"提交成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisibleUpdate.value = false\r\n                    form.data = {}\r\n                } else {\r\n                    // dialogVisible.value = false\r\n                    ElMessage.error(res.data.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\nconst upload = ref();\r\nconst fileList = ref([]); // 图片列表\r\nconst onUpload = (file) => {\r\n    const files = { 0: file.raw }// 取到File\r\n    // console.log(files)\r\n    if (files === 'undefined') {\r\n        console.log()\r\n    } else {\r\n        readExcel(files)\r\n    }\r\n    //state.upload.value.clearFiles(); //去掉文件列表\r\n    console.log(upload)\r\n    console.log(state.upload)\r\n};\r\nconst readExcel = (files) => { // 表格导入\r\n    console.log(files)\r\n    if (files.length <= 0) { // 如果没有文件名\r\n        return false\r\n    } else if (!/\\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {\r\n        console.log('上传格式不正确，请上传xls或者xlsx格式')\r\n        return false\r\n    }\r\n\r\n    const fileReader = new FileReader();\r\n    fileReader.onload = (ev) => {\r\n        try {\r\n            const data = ev.target.result;\r\n            const workbook = XLSX.read(data, { type: 'binary' });\r\n            const wsname = workbook.SheetNames[0]// 取第一张表\r\n            const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsname])// 生成json表格内容\r\n            console.log(ws)\r\n            request({\r\n                url: \"/parking/ownerinfo/batInsert\",\r\n                method: \"POST\",\r\n                data: ws,\r\n            }).then((res) => {\r\n                if (res.code === null) {\r\n                    getData()\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisible.value = false\r\n                    console.log(res.msg)\r\n                    if (res.msg !== \"\") {\r\n                        ElMessageBox.alert(res.msg, '提示', {\r\n                            // if you want to disable its autofocus\r\n                            // autofocus: false,\r\n                            confirmButtonText: 'OK',\r\n                            callback: (action) => {\r\n                            },\r\n                        })\r\n                    } else {\r\n                        ElMessage.success(\"提交成功！\");\r\n                    }\r\n\r\n                } else {\r\n                    dialogVisible.value = false\r\n                    ElMessage.error(res.msg);\r\n                }\r\n            });\r\n            // 重写数据\r\n            upload.value = ''\r\n        } catch (e) {\r\n            return false\r\n        }\r\n    }\r\n    fileReader.readAsBinaryString(files[0])\r\n};\r\nconst state = reactive({\r\n    upload: null\r\n})\r\n// 文件上传失败钩子\r\nconst onErrorFile = () => {\r\n    ElMessage.error('文件上传失败')\r\n    state.upload.value.clearFiles(); //去掉文件列表\r\n}\r\n\r\n// 文件上传成功钩子\r\nconst onSuccessFile = () => {\r\n    ElMessage.success('文件上传成功')\r\n    state.upload.value.clearFiles(); //去掉文件列表\r\n}\r\n\r\nconst dataNum = ref(0);\r\nconst deleteCar = (index) => {\r\n    if (form.data.carDatas.length <= 1) {\r\n        // 如果只有一个输入框则不可以删除\r\n        return false\r\n    }\r\n    console.log(index);\r\n    form.data.carDatas.splice(index, 1)\r\n    // 删除了数组中对应的数据也就将这个位置的输入框删除\r\n}\r\n\r\n//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n    let style = { padding: '0px 3px' }\r\n    return style\r\n};\r\nconst addCar = () => {\r\n    console.log(dataNum)\r\n    if (form.data.carDatas.length > 9) {\r\n        // 如果只有一个输入框则不可以删除\r\n        ElMessage.success('业主所属车量不能超过10个！')\r\n        return false\r\n    }\r\n    form.data.carDatas.push(\r\n        // 增加就push进数组一个新值\r\n        {\r\n            id: dataNum.value++,\r\n            data: ''\r\n        }\r\n    )\r\n}\r\n\r\nconst dataParkingNum = ref(0);\r\nconst deleteParking = (index) => {\r\n    if (form.data.parkingDatas.length <= 1) {\r\n        // 如果只有一个输入框则不可以删除\r\n        return false\r\n    }\r\n    console.log(index);\r\n    form.data.parkingDatas.splice(index, 1)\r\n    // 删除了数组中对应的数据也就将这个位置的输入框删除\r\n}\r\nconst addParking = () => {\r\n    console.log(dataParkingNum)\r\n    if (form.data.length > 9) {\r\n        // 如果只有一个输入框则不可以删除\r\n        ElMessage.success('业主所属停车位数量不能超过10个！')\r\n        return false\r\n    }\r\n    form.data.parkingDatas.push(\r\n        // 增加就push进数组一个新值\r\n        {\r\n            id: dataParkingNum.value++,\r\n            data: ''\r\n        }\r\n    )\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n    background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n\r\n// ::v-deep .el-upload--picture-card{\r\n//   width: 100px;\r\n//   height: 100px;\r\n// }\r\n\r\n.searchButton {\r\n    display: inline-block;\r\n    margin-right: 10px;\r\n}\r\n\r\n.addButton {\r\n    display: inline-block;\r\n    margin-right: 10px;\r\n}\r\n\r\n.uploadButton {\r\n    display: inline-block;\r\n    margin-right: 10px;\r\n}\r\n\r\n.el-upload {\r\n    // width: 100px;\r\n    // height: 40px;\r\n    // line-height: 100px;\r\n    display: inline-block;\r\n    outline: none;\r\n    border: none;\r\n}\r\n</style>\r\n"], "mappings": ";OAK4BA,UAA0C;OA+HjCC,UAAkC;OAI1CC,UAAiC;;;EAtIjDC,KAAK,EAAC;AAAQ;gEAGPC,mBAAA,CAAuD,Y,aAApDA,mBAAA,CAAgD;EAA3CC,GAA0C,EAA1CL;AAA0C,G;;EAIzDG,KAAK,EAAC;AAAW;;EACbA,KAAK,EAAC;AAAY;;EA0ClBA,KAAK,EAAC;AAAY;;;;EAiHTA,KAAK,EAAC;AAAe;;;;EAiHrBA,KAAK,EAAC;AAAe;;EASzBG,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;EAErBH,KAAK,EAAC;AAAe;;EAQzBG,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;EAErBH,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;uBA1S3CI,mBAAA,CAgTM,cA/SFH,mBAAA,CAMM,OANNI,UAMM,GALFC,YAAA,CAIgBC,wBAAA;IAJDC,SAAS,EAAC;EAAG;sBACxB,MAEqB,CAFrBF,YAAA,CAEqBG,6BAAA;wBADjB,MAAuD,CAAvDC,UAAuD,E,iBAAA,QAC3D,E;;;;QAGRT,mBAAA,CAiDM,OAjDNU,UAiDM,GAhDFV,mBAAA,CAsBM,OAtBNW,UAsBM,GArBFN,YAAA,CAoBUO,kBAAA;IApBAC,MAAM,EAAE,IAAI;IAAGC,KAAK,EAAEC,MAAA,CAAAC,KAAK;IAAEjB,KAAK,EAAC,kBAAkB;IAAC,aAAW,EAAC;;sBACxE,MAGe,CAHfM,YAAA,CAGeY,uBAAA;MAHD,aAAW,EAAC,MAAM;MAACC,KAAK,EAAC;;wBACnC,MACyB,CADzBb,YAAA,CACyBc,mBAAA;oBADNJ,MAAA,CAAAC,KAAK,CAACI,SAAS;mEAAfL,MAAA,CAAAC,KAAK,CAACI,SAAS,GAAAC,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACvB,KAAK,EAAC,mBAAmB;QAC5EwB,SAAS,EAAT;;;QAERlB,YAAA,CAGeY,uBAAA;MAHD,aAAW,EAAC,MAAM;MAACC,KAAK,EAAC;;wBACnC,MACyB,CADzBb,YAAA,CACyBc,mBAAA;oBADNJ,MAAA,CAAAC,KAAK,CAACQ,SAAS;mEAAfT,MAAA,CAAAC,KAAK,CAACQ,SAAS,GAAAH,MAAA;QAAEC,WAAW,EAAC,MAAM;QAACvB,KAAK,EAAC,mBAAmB;QAC5EwB,SAAS,EAAT;;;QAGRlB,YAAA,CACYoB,oBAAA;MADDC,IAAI,EAAC,SAAS;MAAC3B,KAAK,EAAC,cAAc;MAAC4B,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEb,MAAA,CAAAc;;wBAAc,MACnF,C,iBADmF,KACnF,E;;QACAxB,YAAA,CACYoB,oBAAA;MADDC,IAAI,EAAC,SAAS;MAAC3B,KAAK,EAAC,WAAW;MAAE6B,OAAK,EAAEb,MAAA,CAAAe;;wBAAW,MAC/D,C,iBAD+D,KAC/D,E;;QACAzB,YAAA,CAIY0B,oBAAA;MAJDC,GAAG,EAAC,QAAQ;MAACjC,KAAK,EAAC,aAAa;MAACkC,MAAM,EAAC,EAAE;MAACC,MAAM,EAAC,YAAY;MAAE,WAAS,EAAEnB,MAAA,CAAAoB,QAAQ;MACzFC,KAAK,EAAE,CAAC;MAAG,WAAS,EAAEC,IAAA,CAAAC,YAAY;MAAG,UAAQ,EAAEvB,MAAA,CAAAwB,WAAW;MAAG,YAAU,EAAExB,MAAA,CAAAyB,aAAa;MACtF,aAAW,EAAE,KAAK;MAAG,WAAS,EAAEzB,MAAA,CAAA0B,QAAQ;MAAG,gBAAc,EAAE,KAAK;MAAEC,IAAI,EAAC;;wBACxE,MAA+D,CAA/DrC,YAAA,CAA+DoB,oBAAA;QAApD1B,KAAK,EAAC,cAAc;QAAC2B,IAAI,EAAC;;0BAAU,MAAI,C,iBAAJ,MAAI,E;;;;;;kCAK/DrB,YAAA,CAkBWsC,mBAAA;IAlBAC,IAAI,EAAE7B,MAAA,CAAA8B,SAAS;IAAEC,MAAM,EAAN,EAAM;IAAC/C,KAAK,EAAC,OAAO;IAACiC,GAAG,EAAC,eAAe;IAAC,wBAAsB,EAAC,cAAc;IACrG,YAAU,EAAEjB,MAAA,CAAAgC,SAAS;IAAG,gBAAc,EAAEhC,MAAA,CAAAiC;;sBAErC,MAAqB,E,cADzB7C,mBAAA,CAEkB8C,SAAA,QAAAC,WAAA,CADCnC,MAAA,CAAAoC,KAAK,EAAbC,IAAI;aADf/C,YAAA,CAEkBgD,0BAAA;QAFA,uBAAqB,EAAE,IAAI;QAAGC,IAAI,EAAEF,IAAI,CAACE,IAAI;QAAGpC,KAAK,EAAEkC,IAAI,CAAClC,KAAK;QACxDqC,GAAG,EAAEH,IAAI,CAACE,IAAI;QAAEE,KAAK,EAAC;;oCAIjDnD,YAAA,CASkBgD,0BAAA;MATDnC,KAAK,EAAC,IAAI;MAACuC,KAAK,EAAC,KAAK;MAACD,KAAK,EAAC,QAAQ;MAACE,KAAK,EAAC;;MAC9CC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACrBxD,YAAA,CACYoB,oBAAA;QADDC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,cAAc;QAAEC,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAA+C,UAAU,CAACD,KAAK,CAACE,GAAG;;0BAAG,MAC1E,C,iBAD0E,KAC1E,E;;wDACA1D,YAAA,CAEYoB,oBAAA;QAFDC,IAAI,EAAC,MAAM;QAACC,IAAI,EAAC,gBAAgB;QAAC5B,KAAK,EAAC,KAAK;QACnD6B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAAiD,YAAY,CAACH,KAAK,CAACI,MAAM,EAAEJ,KAAK,CAACE,GAAG,CAACG,EAAE;;0BAAG,MACtD,C,iBADsD,KACtD,E;;;;;;+BAMZlE,mBAAA,CAKM,OALNmE,UAKM,GAJF9D,YAAA,CAGgB+D,wBAAA;IAHAC,WAAW,EAAEtD,MAAA,CAAAC,KAAK,CAACsD,OAAO;IAAG,YAAU,EAAE,YAAY;IAAG,WAAS,EAAEvD,MAAA,CAAAC,KAAK,CAACuD,QAAQ;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,KAAK,EAAE1D,MAAA,CAAA2D,SAAS;IAAGC,YAAW,EAAE5D,MAAA,CAAA6D,gBAAgB;IACjGC,eAAc,EAAE9D,MAAA,CAAA+D;sEAI7B9E,mBAAA,CAgHM,cA/GFK,YAAA,CA8GY0E,oBAAA;IA9GDC,KAAK,EAAC,QAAQ;gBAAUjE,MAAA,CAAAkE,aAAa;iEAAblE,MAAA,CAAAkE,aAAa,GAAA5D,MAAA;IAAEoC,KAAK,EAAC;;IAwGzCyB,MAAM,EAAAtB,QAAA,CACb,MAGO,CAHP5D,mBAAA,CAGO,QAHPmF,UAGO,GAFH9E,YAAA,CAAyDoB,oBAAA;MAA7CG,OAAK,EAAAwD,MAAA,SAAAA,MAAA,OAAA/D,MAAA,IAAEN,MAAA,CAAAkE,aAAa;;wBAAU,MAAG,C,iBAAH,KAAG,E;;QAC7C5E,YAAA,CAAuDoB,oBAAA;MAA5CC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEb,MAAA,CAAAsE;;wBAAM,MAAG,C,iBAAH,KAAG,E;;;sBA1GnD,MAsGU,CAtGVhF,YAAA,CAsGUO,kBAAA;MAtGAE,KAAK,EAAEC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI;MAAEZ,GAAG,EAAC,SAAS;MAAEuD,KAAK,EAAExE,MAAA,CAAAwE,KAAK;MAAE,aAAW,EAAC;;wBACjE,MAMe,CANflF,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC6C,QAAQ;qEAAlB1E,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC6C,QAAQ,GAAApE,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA2E,YAAY,EAApBtC,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAACqC,QAAQ;cAAGvE,KAAK,EAAEkC,IAAI,CAACqC,QAAQ;cAC7EI,KAAK,EAAEzC,IAAI,CAACqC,QAAQ;cAAG7D,OAAK,EAAEb,MAAA,CAAA+E;;;;;;UAI3CzF,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACmD,IAAI;qEAAdhF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACmD,IAAI,GAAA1E,MAAA;UAAEC,WAAW,EAAC;;4BACjC,MAAwB,E,kBAAnCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAiF,QAAQ,EAAhB5C,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAFyBrC,GAAG,EAAEH,IAAI,CAAC2C,IAAI;cAAG7E,KAAK,EAAEkC,IAAI,CAAC2C,IAAI;cAAGF,KAAK,EAAEzC,IAAI,CAAC2C,IAAI;cACpFnE,OAAK,EAAEb,MAAA,CAAAkF;;;;;;UAIpB5F,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsD,QAAQ;qEAAlBnF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsD,QAAQ,GAAA7E,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAoF,YAAY,EAApB/C,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAAC8C,QAAQ;cAAGhF,KAAK,EAAEkC,IAAI,CAAC8C,QAAQ;cAC7EL,KAAK,EAAEzC,IAAI,CAAC8C,QAAQ;cAAGtE,OAAK,EAAEb,MAAA,CAAAqF;;;;;;UAI3C/F,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACxB,SAAS;qEAAnBL,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACxB,SAAS,GAAAC,MAAA;UAAEC,WAAW,EAAC;;4BACtC,MAA6B,E,kBAAxCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAsF,aAAa,EAArBjD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF8BrC,GAAG,EAAEH,IAAI,CAAChC,SAAS;cAAGF,KAAK,EAAEkC,IAAI,CAAChC,SAAS;cAChFyE,KAAK,EAAEzC,IAAI,CAAChC,SAAS;cAAGQ,OAAK,EAAEb,MAAA,CAAAuF;;;;;;UAI5CjG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC2D,QAAQ;qEAAlBxF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC2D,QAAQ,GAAAlF,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAyF,YAAY,EAApBpD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAACmD,QAAQ;cAAGrF,KAAK,EAAEkC,IAAI,CAACmD,QAAQ;cAC7EV,KAAK,EAAEzC,IAAI,CAACmD,QAAQ;cAAG3E,OAAK,EAAEb,MAAA,CAAA0F;;;;;;UAI3CpG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC8D,KAAK;qEAAf3F,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC8D,KAAK,GAAArF,MAAA;UAAEC,WAAW,EAAC;;4BAClC,MAAyB,E,kBAApCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA4F,SAAS,EAAjBvD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF0BrC,GAAG,EAAEH,IAAI,CAACsD,KAAK;cAAGxF,KAAK,EAAEkC,IAAI,CAACsD,KAAK;cACpEb,KAAK,EAAEzC,IAAI,CAACsD,KAAK;cAAG9E,OAAK,EAAEb,MAAA,CAAA6F;;;;;;UAIxCvG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACiE,KAAK;qEAAf9F,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACiE,KAAK,GAAAxF,MAAA;UAAEC,WAAW,EAAC;;4BAClC,MAAyB,E,kBAApCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA+F,SAAS,EAAjB1D,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF0BrC,GAAG,EAAEH,IAAI,CAACyD,KAAK;cAAG3F,KAAK,EAAEkC,IAAI,CAACyD,KAAK;cACpEhB,KAAK,EAAEzC,IAAI,CAACyD,KAAK;cAAGjF,OAAK,EAAEb,MAAA,CAAAgG;;;;;;UAIxC1G,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoE,UAAU;qEAApBjG,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoE,UAAU,GAAA3F,MAAA;UAAEC,WAAW,EAAC;;4BACvC,MAA8B,E,kBAAzCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAkG,cAAc,EAAtB7D,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF+BrC,GAAG,EAAEH,IAAI,CAAC4D,UAAU;cAAG9F,KAAK,EAAEkC,IAAI,CAAC4D,UAAU;cACnFnB,KAAK,EAAEzC,IAAI,CAAC4D;;;;;;UAIzB3G,YAAA,CAEeY,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACoC,IAAI,EAAC;;0BAC5B,MAAsE,CAAtEjD,YAAA,CAAsEc,mBAAA;sBAAnDJ,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACpB,SAAS;uEAAnBT,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACpB,SAAS,GAAAH,MAAA;UAAEnB,KAAkB,EAAlB;YAAA;UAAA;;;UAE5CG,YAAA,CAEeY,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACoC,IAAI,EAAC;;0BAC5B,MAAuE,CAAvEjD,YAAA,CAAuEc,mBAAA;sBAApDJ,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsE,UAAU;uEAApBnG,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsE,UAAU,GAAA7F,MAAA;UAAEnB,KAAkB,EAAlB;YAAA;UAAA;;;UAE7CiH,mBAAA,sBAAuB,EACvB9G,YAAA,CAYeY,uBAAA;QAZDC,KAAK,EAAC;MAAK;0BACP,MAA2C,E,kBAAzDf,mBAAA,CASe8C,SAAA,QAAAC,WAAA,CATuBnC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACwE,QAAQ,GAAlChE,IAAI,EAAEiE,KAAK;+BAAjC1B,YAAA,CASe1E,uBAAA;YAT4CsC,GAAG,EAAE8D,KAAK;YACjEnH,KAAyE,EAAzE;cAAA;cAAA;cAAA;YAAA;;8BACA,MAMe,CANfG,YAAA,CAMeY,uBAAA;cANAqC,IAAI,gBAAgB+D,KAAK;cACnC9B,KAAK,EAAE;gBAAA+B,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;;gCACR,MACW,CADXnH,YAAA,CACWc,mBAAA;gBADDjB,KAAqB,EAArB;kBAAA;gBAAA,CAAqB;gBAACwB,IAAI,EAAC,MAAM;4BAAU0B,IAAI,CAACR,IAAI;iDAATQ,IAAI,CAACR,IAAI,GAAAvB,MAAA;gBAAEC,WAAW,EAAC;8EAE5EtB,mBAAA,CACwB;gBADnBC,GAAkC,EAAlCJ,UAAkC;gBAAE+B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAA0G,SAAS,CAACJ,KAAK;gBAAGK,GAAG,EAAC,EAAE;gBACpE3H,KAAK,EAAC;;;;;;wCAGlBC,mBAAA,CAAiF;UAA5EC,GAAiC,EAAjCH,UAAiC;UAAE8B,OAAK,EAAEb,MAAA,CAAA4G,MAAM;UAAED,GAAG,EAAC,EAAE;UAAC3H,KAAK,EAAC;;;UAExEM,YAAA,CAYeY,uBAAA;QAZDC,KAAK,EAAC;MAAM;0BACR,MAA+C,E,kBAA7Df,mBAAA,CASe8C,SAAA,QAAAC,WAAA,CATuBnC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACgF,YAAY,GAAtCxE,IAAI,EAAEiE,KAAK;+BAAjC1B,YAAA,CASe1E,uBAAA;YATgDsC,GAAG,EAAE8D,KAAK;YACrEnH,KAAyE,EAAzE;cAAA;cAAA;cAAA;YAAA;;8BACA,MAMe,CANfG,YAAA,CAMeY,uBAAA;cANAqC,IAAI,oBAAoB+D,KAAK;cACvC9B,KAAK,EAAE;gBAAA+B,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;;gCACR,MACW,CADXnH,YAAA,CACWc,mBAAA;gBADDjB,KAAqB,EAArB;kBAAA;gBAAA,CAAqB;gBAACwB,IAAI,EAAC,MAAM;4BAAU0B,IAAI,CAACR,IAAI;iDAATQ,IAAI,CAACR,IAAI,GAAAvB,MAAA;gBAAEC,WAAW,EAAC;8EAE5EtB,mBAAA,CACwB;gBADnBC,GAAkC,EAblCJ,UAAkC;gBAaE+B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAA8G,aAAa,CAACR,KAAK;gBAAGK,GAAG,EAAC,EAAE;gBACxE3H,KAAK,EAAC;;;;;;wCAGlBC,mBAAA,CAAqF;UAAhFC,GAAiC,EAbjCH,UAAiC;UAaE8B,OAAK,EAAEb,MAAA,CAAA+G,UAAU;UAAEJ,GAAG,EAAC,EAAE;UAAC3H,KAAK,EAAC;;;UAE5EM,YAAA,CAKeY,uBAAA;QALDC,KAAK,EAAC;MAAM;0BACtB,MAGiB,CAHjBb,YAAA,CAGiB0H,yBAAA;sBAHQhH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoF,OAAO;uEAAjBjH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoF,OAAO,GAAA3G,MAAA;;4BACtC,MAAmC,CAAnChB,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;cACxBb,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;;;;;UAGhCb,YAAA,CAKeY,uBAAA;QALDC,KAAK,EAAC,UAAU;QAAC,aAAW,EAAC;;0BACvC,MAGiB,CAHjBb,YAAA,CAGiB0H,yBAAA;sBAHQhH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsF,YAAY;uEAAtBnH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsF,YAAY,GAAA7G,MAAA;;4BAC3C,MAAmC,CAAnChB,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;cACxBb,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;;;;;;;;;uCAY5ClB,mBAAA,CAgHM,cA/GFK,YAAA,CA8GY0E,oBAAA;IA9GDC,KAAK,EAAC,QAAQ;gBAAUjE,MAAA,CAAAoH,mBAAmB;iEAAnBpH,MAAA,CAAAoH,mBAAmB,GAAA9G,MAAA;IAAEoC,KAAK,EAAC;;IAwG/CyB,MAAM,EAAAtB,QAAA,CACb,MAGO,CAHP5D,mBAAA,CAGO,QAHPoI,WAGO,GAFH/H,YAAA,CAAgEoB,oBAAA;MAApDG,OAAK,EAAAwD,MAAA,SAAAA,MAAA,OAAA/D,MAAA,IAAGN,MAAA,CAAAoH,mBAAmB;;wBAAU,MAAG,C,iBAAH,KAAG,E;;QACpD9H,YAAA,CAA6DoB,oBAAA;MAAlDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAEb,MAAA,CAAAsH;;wBAAY,MAAG,C,iBAAH,KAAG,E;;;sBA1GzD,MAsGU,CAtGVhI,YAAA,CAsGUO,kBAAA;MAtGAE,KAAK,EAAEC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI;MAAEZ,GAAG,EAAC,SAAS;MAAEuD,KAAK,EAAExE,MAAA,CAAAwE,KAAK;MAAE,aAAW,EAAC;;wBACjE,MAMe,CANflF,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC6C,QAAQ;uEAAlB1E,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC6C,QAAQ,GAAApE,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA2E,YAAY,EAApBtC,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAACqC,QAAQ;cAAGvE,KAAK,EAAEkC,IAAI,CAACqC,QAAQ;cAC7EI,KAAK,EAAEzC,IAAI,CAACqC,QAAQ;cAAG7D,OAAK,EAAEb,MAAA,CAAA+E;;;;;;UAI3CzF,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACmD,IAAI;uEAAdhF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACmD,IAAI,GAAA1E,MAAA;UAAEC,WAAW,EAAC;;4BACjC,MAAwB,E,kBAAnCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAiF,QAAQ,EAAhB5C,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAFyBrC,GAAG,EAAEH,IAAI,CAAC2C,IAAI;cAAG7E,KAAK,EAAEkC,IAAI,CAAC2C,IAAI;cAAGF,KAAK,EAAEzC,IAAI,CAAC2C,IAAI;cACpFnE,OAAK,EAAEb,MAAA,CAAAkF;;;;;;UAIpB5F,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsD,QAAQ;uEAAlBnF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsD,QAAQ,GAAA7E,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAoF,YAAY,EAApB/C,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAAC8C,QAAQ;cAAGhF,KAAK,EAAEkC,IAAI,CAAC8C,QAAQ;cAC7EL,KAAK,EAAEzC,IAAI,CAAC8C,QAAQ;cAAGtE,OAAK,EAAEb,MAAA,CAAAqF;;;;;;UAI3C/F,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACxB,SAAS;uEAAnBL,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACxB,SAAS,GAAAC,MAAA;UAAEC,WAAW,EAAC;;4BACtC,MAA6B,E,kBAAxCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAsF,aAAa,EAArBjD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF8BrC,GAAG,EAAEH,IAAI,CAAChC,SAAS;cAAGF,KAAK,EAAEkC,IAAI,CAAChC,SAAS;cAChFyE,KAAK,EAAEzC,IAAI,CAAChC,SAAS;cAAGQ,OAAK,EAAEb,MAAA,CAAAuF;;;;;;UAI5CjG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC2D,QAAQ;uEAAlBxF,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC2D,QAAQ,GAAAlF,MAAA;UAAEC,WAAW,EAAC;;4BACrC,MAA4B,E,kBAAvCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAyF,YAAY,EAApBpD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF6BrC,GAAG,EAAEH,IAAI,CAACmD,QAAQ;cAAGrF,KAAK,EAAEkC,IAAI,CAACmD,QAAQ;cAC7EV,KAAK,EAAEzC,IAAI,CAACmD,QAAQ;cAAG3E,OAAK,EAAEb,MAAA,CAAA0F;;;;;;UAI3CpG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC8D,KAAK;uEAAf3F,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAAC8D,KAAK,GAAArF,MAAA;UAAEC,WAAW,EAAC;;4BAClC,MAAyB,E,kBAApCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA4F,SAAS,EAAjBvD,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF0BrC,GAAG,EAAEH,IAAI,CAACsD,KAAK;cAAGxF,KAAK,EAAEkC,IAAI,CAACsD,KAAK;cACpEb,KAAK,EAAEzC,IAAI,CAACsD,KAAK;cAAG9E,OAAK,EAAEb,MAAA,CAAA6F;;;;;;UAIxCvG,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACiE,KAAK;uEAAf9F,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACiE,KAAK,GAAAxF,MAAA;UAAEC,WAAW,EAAC;;4BAClC,MAAyB,E,kBAApCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAA+F,SAAS,EAAjB1D,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF0BrC,GAAG,EAAEH,IAAI,CAACyD,KAAK;cAAG3F,KAAK,EAAEkC,IAAI,CAACyD,KAAK;cACpEhB,KAAK,EAAEzC,IAAI,CAACyD,KAAK;cAAGjF,OAAK,EAAEb,MAAA,CAAAgG;;;;;;UAIxC1G,YAAA,CAMeY,uBAAA;QANDC,KAAK,EAAC,IAAI;QAACoC,IAAI,EAAC;;0BAC1B,MAIY,CAJZjD,YAAA,CAIYmF,oBAAA;sBAJQzE,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoE,UAAU;uEAApBjG,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoE,UAAU,GAAA3F,MAAA;UAAEC,WAAW,EAAC;;4BACvC,MAA8B,E,kBAAzCnB,mBAAA,CAEY8C,SAAA,QAAAC,WAAA,CAFcnC,MAAA,CAAAkG,cAAc,EAAtB7D,IAAI;iCAAtBuC,YAAA,CAEYC,oBAAA;cAF+BrC,GAAG,EAAEH,IAAI,CAAC4D,UAAU;cAAG9F,KAAK,EAAEkC,IAAI,CAAC4D,UAAU;cACnFnB,KAAK,EAAEzC,IAAI,CAAC4D;;;;;;UAIzB3G,YAAA,CAEeY,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACoC,IAAI,EAAC;;0BAC5B,MAAsE,CAAtEjD,YAAA,CAAsEc,mBAAA;sBAAnDJ,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACpB,SAAS;uEAAnBT,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACpB,SAAS,GAAAH,MAAA;UAAEnB,KAAkB,EAAlB;YAAA;UAAA;;;UAE5CG,YAAA,CAEeY,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACoC,IAAI,EAAC;;0BAC5B,MAAuE,CAAvEjD,YAAA,CAAuEc,mBAAA;sBAApDJ,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsE,UAAU;uEAApBnG,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsE,UAAU,GAAA7F,MAAA;UAAEnB,KAAkB,EAAlB;YAAA;UAAA;;;UAE7CiH,mBAAA,sBAAuB,EACvB9G,YAAA,CAYeY,uBAAA;QAZDC,KAAK,EAAC;MAAK;0BACP,MAA2C,E,kBAAzDf,mBAAA,CASe8C,SAAA,QAAAC,WAAA,CATuBnC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACwE,QAAQ,GAAlChE,IAAI,EAAEiE,KAAK;+BAAjC1B,YAAA,CASe1E,uBAAA;YAT4CsC,GAAG,EAAE8D,KAAK;YACjEnH,KAAyE,EAAzE;cAAA;cAAA;cAAA;YAAA;;8BACA,MAMe,CANfG,YAAA,CAMeY,uBAAA;cANAqC,IAAI,gBAAgB+D,KAAK;cACnC9B,KAAK,EAAE;gBAAA+B,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;;gCACR,MACW,CADXnH,YAAA,CACWc,mBAAA;gBADDjB,KAAqB,EAArB;kBAAA;gBAAA,CAAqB;gBAACwB,IAAI,EAAC,MAAM;4BAAU0B,IAAI,CAACR,IAAI;iDAATQ,IAAI,CAACR,IAAI,GAAAvB,MAAA;gBAAEC,WAAW,EAAC;8EAE5EtB,mBAAA,CACwB;gBADnBC,GAAkC,EAjHlCJ,UAAkC;gBAiHE+B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAA0G,SAAS,CAACJ,KAAK;gBAAGK,GAAG,EAAC,EAAE;gBACpE3H,KAAK,EAAC;;;;;;wCAGlBC,mBAAA,CAAiF;UAA5EC,GAAiC,EAjHjCH,UAAiC;UAiHE8B,OAAK,EAAEb,MAAA,CAAA4G,MAAM;UAAED,GAAG,EAAC,EAAE;UAAC3H,KAAK,EAAC;;;UAExEM,YAAA,CAYeY,uBAAA;QAZDC,KAAK,EAAC;MAAM;0BACR,MAA+C,E,kBAA7Df,mBAAA,CASe8C,SAAA,QAAAC,WAAA,CATuBnC,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACgF,YAAY,GAAtCxE,IAAI,EAAEiE,KAAK;+BAAjC1B,YAAA,CASe1E,uBAAA;YATgDsC,GAAG,EAAE8D,KAAK;YACrEnH,KAAyE,EAAzE;cAAA;cAAA;cAAA;YAAA;;8BACA,MAMe,CANfG,YAAA,CAMeY,uBAAA;cANAqC,IAAI,oBAAoB+D,KAAK;cACvC9B,KAAK,EAAE;gBAAA+B,QAAA;gBAAAC,OAAA;gBAAAC,OAAA;cAAA;;gCACR,MACW,CADXnH,YAAA,CACWc,mBAAA;gBADDjB,KAAqB,EAArB;kBAAA;gBAAA,CAAqB;gBAACwB,IAAI,EAAC,MAAM;4BAAU0B,IAAI,CAACR,IAAI;iDAATQ,IAAI,CAACR,IAAI,GAAAvB,MAAA;gBAAEC,WAAW,EAAC;8EAE5EtB,mBAAA,CACwB;gBADnBC,GAAkC,EA9HlCJ,UAAkC;gBA8HE+B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAA8G,aAAa,CAACR,KAAK;gBAAGK,GAAG,EAAC,EAAE;gBACxE3H,KAAK,EAAC;;;;;;wCAGlBC,mBAAA,CAAqF;UAAhFC,GAAiC,EA9HjCH,UAAiC;UA8HE8B,OAAK,EAAEb,MAAA,CAAA+G,UAAU;UAAEJ,GAAG,EAAC,EAAE;UAAC3H,KAAK,EAAC;;;UAE5EM,YAAA,CAKeY,uBAAA;QALDC,KAAK,EAAC;MAAM;0BACtB,MAGiB,CAHjBb,YAAA,CAGiB0H,yBAAA;sBAHQhH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoF,OAAO;uEAAjBjH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACoF,OAAO,GAAA3G,MAAA;;4BACtC,MAAmC,CAAnChB,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;cACxBb,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;;;;;UAGhCb,YAAA,CAKeY,uBAAA;QALDC,KAAK,EAAC,UAAU;QAAC,aAAW,EAAC;;0BACvC,MAGiB,CAHjBb,YAAA,CAGiB0H,yBAAA;sBAHQhH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsF,YAAY;uEAAtBnH,MAAA,CAAAuE,IAAI,CAAC1C,IAAI,CAACsF,YAAY,GAAA7G,MAAA;;4BAC3C,MAAmC,CAAnChB,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;cACxBb,YAAA,CAAmC4H,mBAAA;YAAxB/G,KAAK,EAAE;UAAG;8BAAE,MAAC,C,iBAAD,GAAC,E;;;;;;;;;;uCAY5ClB,mBAAA,CASM,cARFK,YAAA,CAOY0E,oBAAA;IAPDC,KAAK,EAAC,QAAQ;gBAAUjE,MAAA,CAAAuH,QAAQ;iEAARvH,MAAA,CAAAuH,QAAQ,GAAAjH,MAAA;;IAE5B6D,MAAM,EAAAtB,QAAA,CACb,MAEO,CAFP5D,mBAAA,CAEO,QAFPuI,WAEO,GADHlI,YAAA,CAAoDoB,oBAAA;MAAxCG,OAAK,EAAAwD,MAAA,SAAAA,MAAA,OAAA/D,MAAA,IAAEN,MAAA,CAAAuH,QAAQ;;wBAAU,MAAG,C,iBAAH,KAAG,E;;;sBAHhD,MAAoD,CAApDtI,mBAAA,CAAoD,QAApDwI,WAAoD,EAAAC,gBAAA,CAAjB1H,MAAA,CAAA2H,OAAO,iB;;uCAQlD1I,mBAAA,CASM,cARFK,YAAA,CAOY0E,oBAAA;IAPDC,KAAK,EAAC,MAAM;gBAAUjE,MAAA,CAAAuH,QAAQ;iEAARvH,MAAA,CAAAuH,QAAQ,GAAAjH,MAAA;;IAE1B6D,MAAM,EAAAtB,QAAA,CACb,MAEO,CAFP5D,mBAAA,CAEO,QAFP2I,WAEO,GADHtI,YAAA,CAAoDoB,oBAAA;MAAxCG,OAAK,EAAAwD,MAAA,SAAAA,MAAA,OAAA/D,MAAA,IAAEN,MAAA,CAAAuH,QAAQ;;wBAAU,MAAG,C,iBAAH,KAAG,E;;;sBAHhD,MAAqD,CAArDtI,mBAAA,CAAqD,QAArD4I,WAAqD,EAAAH,gBAAA,CAAlB1H,MAAA,CAAA8H,QAAQ,iB"}]}