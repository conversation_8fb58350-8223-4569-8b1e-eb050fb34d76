{"version": 3, "sources": ["webpack:///./src/icons/svg/UserOwner.svg", "webpack:///./src/views/admin/MonthTicket.vue?ef10", "webpack:///./src/icons/svg/TicketName.svg", "webpack:///./src/icons/svg/AddCarNo.svg", "webpack:///./src/icons/svg/DeletedCarNo.svg", "webpack:///./src/icons/svg/IsFrozen.svg", "webpack:///./src/icons/svg/ParkName.svg", "webpack:///./src/icons/svg/UserPhone.svg", "webpack:///./src/icons/svg/VIPInfo.svg", "webpack:///./src/icons/svg/Remark2.svg", "webpack:///./src/icons/svg/AddBlack.svg", "webpack:///./src/icons/svg/Remark3.svg", "webpack:///./src/icons/svg/ValidStatus.svg", "webpack:///./src/views/admin/MonthTicket.vue", "webpack:///./src/views/admin/MonthTicket.vue?a6a2", "webpack:///./src/icons/svg/TimePeriodList.svg", "webpack:///./src/icons/svg/CarNo.svg", "webpack:///./src/icons/svg-black/ReleaseReason.svg", "webpack:///./src/icons/svg/Remark1.svg"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "importData", "ref", "importSuccess", "importProps", "importDefeat", "importDefeatData", "formRef", "rules", "userName", "required", "message", "trigger", "blackReason", "isPermament", "applicantUserId", "IconName", "monthTicketTypeList", "isRemark", "importNotificationDefeat", "inputs", "addInput", "value", "push", "removeInput", "index", "splice", "handleClose", "done", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "getData", "ElMessage", "catch", "handleSyc", "console", "log", "query", "parkName", "loadingTime", "setTimeout", "loading", "ElLoading", "service", "lock", "text", "background", "request", "get", "params", "validStatus", "res", "close", "data", "success", "timePeriodListResult", "timePeriodListResults", "specialCarTypeNameList", "dialogVisibleUpdate", "dialogVisibleBlack", "isPermamentList", "TableRef", "heightData", "changeParkName", "pageNum", "localStorage", "getItem", "handleInput", "regex", "test", "timeDays", "error", "isFormVisible", "toggleForm", "handleInputPhone", "userPhone", "reactive", "isFrozen", "<PERSON><PERSON><PERSON><PERSON>", "ticketName", "carNo", "timePeriodList", "remark1", "remark2", "remark3", "pageSize", "descriptionData", "blackData", "id", "specialCarTypeName", "blackNameList", "name", "carNoList", "shortcuts", "now", "Date", "tomorrow", "getTime", "endTime", "getFullYear", "getMonth", "getDate", "startTime", "oneMonthLater", "oneMonthEndTime", "threeMonthsLater", "threeMonthsEndTime", "oneYearLater", "oneYearEndTime", "tenYearsLater", "tenYearsEndTime", "clearTicketName", "clearCarNo", "clearUserName", "clearUserPhone", "clearTimeDays", "clearRemark1", "clearRemark2", "clearRemark3", "<PERSON><PERSON><PERSON><PERSON>", "clearF<PERSON>zen", "yardName", "parkCodeList", "recordList", "changeIcon", "handleEdit", "row", "split", "handleAddBlack", "tableData", "pageTotal", "scrollCount", "value2", "tableRowClassName", "rowIndex", "startDate", "newStartDate", "formattedStartDate", "toLocaleString", "year", "month", "day", "replace", "handleOnset", "records", "total", "handleSearch", "handleSizeChange", "val", "getTagClass", "plate", "length", "getTagClassTime", "time", "timeArr", "endDate", "today", "combinedArray", "setForm", "blackTimePeriodList", "update", "validate", "valid", "hour", "minute", "second", "newEndDate", "formattedEndDate", "timePeriod", "join", "specialCarTypeId", "find", "item", "url", "method", "parkCode", "carCode", "car<PERSON>wner", "reason", "code", "for<PERSON>ach", "arr", "ElNotification", "title", "offset", "position", "defeatReason", "msg", "handlePageChange", "scroll<PERSON>eh<PERSON>or", "e", "deltaY", "onMounted", "$refs", "bodyWrapper", "addEventListener", "onUnmounted", "removeEventListener", "__exports__"], "mappings": "qGAAAA,EAAOC,QAAU,IAA0B,8B,oCCA3C,W,uBCAAD,EAAOC,QAAU,IAA0B,+B,uBCA3CD,EAAOC,QAAU,IAA0B,6B,8CCA3CD,EAAOC,QAAU,IAA0B,iC,uBCA3CD,EAAOC,QAAU,IAA0B,6B,uBCA3CD,EAAOC,QAAU,IAA0B,6B,qBCA3CD,EAAOC,QAAU,IAA0B,8B,uBCA3CD,EAAOC,QAAU,IAA0B,4B,qBCA3CD,EAAOC,QAAU,IAA0B,4B,qBCA3CD,EAAOC,QAAU,IAA0B,6B,qBCA3CD,EAAOC,QAAU,IAA0B,4B,qBCA3CD,EAAOC,QAAU,IAA0B,gC,ymGC0WrCC,GAAO,wB,sCACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACV,CAAEC,MAAO,OAAQC,KAAM,cAGvB,CAAED,MAAO,OAAQC,KAAM,SACvB,CAAED,MAAO,MAAOC,KAAM,kBACtB,CAAED,MAAO,MAAOC,KAAM,WACtB,CAAED,MAAO,MAAOC,KAAM,WACtB,CAAED,MAAO,MAAOC,KAAM,YAIpBC,EAAaC,iBAAI,IAEjBC,EAAgBD,iBAAI,IACpBE,EAAc,CAChB,CAAEL,MAAO,OAAQC,KAAM,WACvB,CAAED,MAAO,SAAUC,KAAM,iBAGvBK,EAAeH,iBAAI,IACnBI,EAAmBJ,iBAAI,IACvBK,EAAUL,iBAAI,MACdM,EAAQ,CACVC,SAAU,CACN,CACIC,UAAU,EACVC,QAAS,UACTC,QAAS,SAGjBC,YAAa,CACT,CACIH,UAAU,EACVC,QAAS,WACTC,QAAS,SAGjBE,YAAa,CACT,CACIJ,UAAU,EACVC,QAAS,YACTC,QAAS,YAKfG,GADcb,iBAAI,IACAA,iBAAI,KACtBc,EAAWd,iBAAI,sBAEfe,EAAsBf,iBAAI,IAC1BgB,EAAWhB,kBAAI,GAEfiB,EAA2BjB,kBAAI,GAC/BkB,EAASlB,iBAAI,IAEbmB,EAAWA,KACbD,EAAOE,MAAMC,KAAK,KAGhBC,EAAeC,IAEjBL,EAAOE,MAAMI,OAAOD,EAAO,IAEzBE,EAAeC,IACjBC,OAAaC,QACT,aACA,KACA,CACIC,kBAAmB,KACnBC,iBAAkB,KAClBC,KAAM,YAGTC,KAAK,KAEFjC,EAAWqB,MAAQ,GACnBnB,EAAcmB,MAAQ,GACtBhB,EAAiBgB,MAAQ,GACzBjB,EAAaiB,MAAQ,GACrBH,EAAyBG,OAAQ,EACjCa,KACAC,eAAU,CACNH,KAAM,UACNtB,QAAS,WAGhB0B,MAAM,KACHF,QAGNG,EAAYA,KAEdC,QAAQC,IAAIC,EAAMC,UAClB,MAAMC,EAAc,EAIpBd,OAAaC,QAAQ,YAAa,KAAM,CACpCC,kBAAmB,SACnBC,iBAAkB,SAClBC,KAAM,YACPC,KAAK,KACJU,WAAW,KACP,MAAMC,EAAUC,OAAUC,QAAQ,CAC9BC,MAAM,EACNC,KAAM,kBACNC,WAAY,6BAEhBC,OACKC,IAAI,8BAA+B,CAChCC,OAAQ,CACJX,SAAUD,EAAMC,SAChBY,YAAa,OAGpBpB,KAAMqB,IACHV,EAAQW,QACRjB,QAAQC,IAAIe,EAAIE,KAAKA,MACrBrB,OAAUsB,QAAQ,OAASH,EAAIE,KAAKA,KAAO,OAC3CtB,QAETQ,KACJN,MAAM,KACLO,WAAW,KACP,MAAMC,EAAUC,OAAUC,QAAQ,CAC9BC,MAAM,EACNC,KAAM,kBACNC,WAAY,6BAEhBC,OACKC,IAAI,8BAA+B,CAChCC,OAAQ,CACJX,SAAUD,EAAMC,SAChBY,YAAa,OAGpBpB,KAAMqB,IACHV,EAAQW,QACRjB,QAAQC,IAAIe,EAAIE,KAAKA,MACrBrB,OAAUsB,QAAQ,OAASH,EAAIE,KAAKA,KAAO,OAC3CtB,QAETQ,MAMLgB,EAAuBzD,iBAAI,IAC3B0D,EAAwB1D,iBAAI,IAC5B2D,EAAyB3D,iBAAI,IAG7B4D,EAAsB5D,kBAAI,GAC1B6D,EAAqB7D,kBAAI,GACzB8D,EAAkB9D,iBAAI,CAAC,KAAM,QAC7B+D,EAAW/D,iBAAI,MACfgE,EAAahE,iBAAI,KAGjBiE,EAAiBA,KACnB1B,EAAM2B,QAAU,EAChBjC,MAEJpB,EAAgBO,MAAQ+C,aAAaC,QAAQ,UAE7C,MAAMC,EAAejD,IACjB,MAAMkD,EAAQ,WACd,IAAIA,EAAMC,KAAKnD,GAQX,OAHAmB,EAAMiC,SAAW,QAEjBtC,OAAUuC,MAAM,WANhBlC,EAAMiC,SAAWpD,EACjBiB,QAAQC,IAAIlB,IASdsD,EAAgB1E,kBAAI,GAEpB2E,EAAaA,KACfD,EAActD,OAASsD,EAActD,MACjCsD,EAActD,MACd4C,EAAW5C,MAAQ,IAEnB4C,EAAW5C,MAAQ,KAGrBwD,EAAoBxD,IACtB,MAAMkD,EAAQ,WACd,IAAIA,EAAMC,KAAKnD,GAQX,OAHAmB,EAAMsC,UAAY,QAElB3C,OAAUuC,MAAM,WANhBlC,EAAMsC,UAAYzD,EAClBiB,QAAQC,IAAIlB,IASdmB,EAAQuC,sBAAS,CACnBC,SAAU,GACVC,QAAS,GACTxC,SAAU,OACVyC,WAAY,GACZ1E,SAAU,GACVsE,UAAW,GACXK,MAAO,GACPC,eAAgB,GAChBC,QAAS,GACTC,QAAS,GACTC,QAAS,GACTd,SAAU,GACVN,QAAS,EACTqB,SAAU,KAERC,GAAkBV,sBAAS,CAC7BvB,KAAM,CACFwB,UAAW,EACXC,SAAU,EACVxC,SAAU,GACVyC,WAAY,GACZ1E,SAAU,GACVsE,UAAW,GACXK,MAAO,GACPC,eAAgB,GAChBC,QAAS,GACTC,QAAS,GACTC,QAAS,MAGXG,GAAYX,sBAAS,CACvBvB,KAAM,CACFmC,GAAI,GACJlD,SAAU,GAEVmD,mBAAoB,GACpBpF,SAAU,GACVsE,UAAW,GACXK,MAAO,GACPtE,YAAa,GACbwE,QAAS,GACTC,QAAS,MAGXO,GAAgBd,sBAAS,CAC3BY,GAAI,GACJG,KAAM,KAEJC,GAAY9F,iBAAI,IAChB+F,GAAY,CACd,CACIhD,KAAM,MACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAEVC,EAAW,IAAID,KAAKD,EAAIG,UAAY,OAEpCC,EAAU,IAAIH,KAAKC,EAASG,cAAeH,EAASI,WAAYJ,EAASK,UAAW,GAAI,GAAI,IAE5FC,EAAY,IAAIP,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,GACnF,MAAO,CAACC,EAAWJ,KAG3B,CACIrD,KAAM,MACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAEVC,EAAW,IAAID,KAAKD,EAAIG,UAAY,QAEpCC,EAAU,IAAIH,KAAKC,EAASG,cAAeH,EAASI,WAAYJ,EAASK,UAAW,GAAI,GAAI,IAE5FC,EAAY,IAAIP,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,GACnF,MAAO,CAACC,EAAWJ,KAG3B,CACIrD,KAAM,OACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAGVQ,EAAgB,IAAIR,KAAKD,EAAIK,cAAeL,EAAIM,WAAa,EAAGN,EAAIO,WACpEG,EAAkB,IAAIT,KAAKQ,EAAcJ,cAAeI,EAAcH,WAAYG,EAAcF,UAAW,GAAI,GAAI,IAEnHC,GADoB,IAAIP,KAAKQ,EAAcJ,cAAeI,EAAcH,WAAYG,EAAcF,UAAW,EAAG,EAAG,GACvG,IAAIN,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,IACnF,MAAO,CAACC,EAAWE,KAG3B,CACI3D,KAAM,OACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAGVU,EAAmB,IAAIV,KAAKD,EAAIK,cAAeL,EAAIM,WAAa,EAAGN,EAAIO,WACvEK,EAAqB,IAAIX,KAAKU,EAAiBN,cAAeM,EAAiBL,WAAYK,EAAiBJ,UAAW,GAAI,GAAI,IAE/HC,GADuB,IAAIP,KAAKU,EAAiBN,cAAeM,EAAiBL,WAAYK,EAAiBJ,UAAW,EAAG,EAAG,GACnH,IAAIN,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,IACnF,MAAO,CAACC,EAAWI,KAG3B,CACI7D,KAAM,MACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAGVY,EAAe,IAAIZ,KAAKD,EAAIK,cAAgB,EAAGL,EAAIM,WAAYN,EAAIO,WACnEO,EAAiB,IAAIb,KAAKY,EAAaR,cAAeQ,EAAaP,WAAYO,EAAaN,UAAW,GAAI,GAAI,IAE/GC,GADmB,IAAIP,KAAKY,EAAaR,cAAeQ,EAAaP,WAAYO,EAAaN,UAAW,EAAG,EAAG,GACnG,IAAIN,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,IACnF,MAAO,CAACC,EAAWM,KAG3B,CACI/D,KAAM,MACN3B,MAAOA,KAEH,MAAM4E,EAAM,IAAIC,KAEVc,EAAgB,IAAId,KAAKD,EAAIK,cAAgB,GAAIL,EAAIM,WAAYN,EAAIO,WACrES,EAAkB,IAAIf,KAAKc,EAAcV,cAAeU,EAAcT,WAAYS,EAAcR,UAAW,GAAI,GAAI,IAEnHC,GADoB,IAAIP,KAAKc,EAAcV,cAAeU,EAAcT,WAAYS,EAAcR,UAAW,EAAG,EAAG,GACvG,IAAIN,KAAKD,EAAIK,cAAeL,EAAIM,WAAYN,EAAIO,UAAW,EAAG,EAAG,IACnF,MAAO,CAACC,EAAWQ,MAIzBC,GAAkBA,KACpB1E,EAAM0C,WAAa,GACnBhD,MAEEiF,GAAaA,KACf3E,EAAM2C,MAAQ,GACdjD,MAEEkF,GAAgBA,KAClB5E,EAAMhC,SAAW,GACjB0B,MACKmF,GAAiBA,KACtB7E,EAAMsC,UAAY,GAClB5C,MAEEoF,GAAgBA,KAClB9E,EAAMiC,SAAW,GACjBvC,MAEEqF,GAAeA,KACjB/E,EAAM6C,QAAU,GAChBnD,MAEEsF,GAAeA,KACjBhF,EAAM8C,QAAU,GAChBpD,MAEEuF,GAAeA,KACjBjF,EAAM+C,QAAU,GAChBrD,MAEEwF,GAAaA,KACflF,EAAMyC,QAAU,GAChB/C,MAEEyF,GAAcA,KAChBnF,EAAMwC,SAAW,GACjB9C,MAEJgB,OACKC,IAAI,6BACD,CACIC,OAAQ,CACJwE,SAAUpF,EAAMC,YAG3BR,KAAMqB,IACHJ,OAAQC,IAAI,sDACR,CACIC,OAAQ,CACJyE,aAAcvE,EAAIE,KAAK,MAG9BvB,KAAMqB,IACHtC,EAAoBK,MAAQiC,EAAIE,KAAKA,KAAKsE,eAG1D,MAAMC,GAAaA,KACf9G,EAASI,OAASJ,EAASI,MACvBJ,EAASI,MACTN,EAASM,MAAQ,mBAIrBiB,QAAQC,IAAItB,EAASI,QAGnB2G,GAAcC,IAChBpE,EAAoBxC,OAAQ,EAC5BoE,GAAgBjC,KAAKmC,GAAKsC,EAAItC,GAE9BI,GAAU1E,MAAQ4G,EAAI9C,MAAM+C,MAAM,KAClC5F,QAAQC,IAAI0F,EAAIzH,UAEhBkD,EAAqBrC,MAAQ4G,EAAI7C,eAAe8C,MAAM,KACtDzC,GAAgBjC,KAAKf,SAAWwF,EAAIxF,SACpCgD,GAAgBjC,KAAK0B,WAAa+C,EAAI/C,WACtCO,GAAgBjC,KAAKhD,SAAWyH,EAAIzH,SACpCiF,GAAgBjC,KAAKsB,UAAYmD,EAAInD,UACrCW,GAAgBjC,KAAK6B,QAAU4C,EAAI5C,QACnCI,GAAgBjC,KAAK8B,QAAU2C,EAAI3C,QACnCG,GAAgBjC,KAAK+B,QAAU0C,EAAI1C,QACnCE,GAAgBjC,KAAK2B,MAAQ8C,EAAI9C,MACjCM,GAAgBjC,KAAKwB,SAAWiD,EAAIjD,SACpCS,GAAgBjC,KAAKyB,QAAUgD,EAAI5E,aAEjC8E,GAAkBF,IACpBnE,EAAmBzC,OAAQ,EAC3BqE,GAAUlC,KAAKmC,GAAKsC,EAAItC,GAExBrD,QAAQC,IAAI0F,EAAIzH,UAEhBuF,GAAU1E,MAAQ4G,EAAI9C,MAAM+C,MAAM,KAClCxC,GAAUlC,KAAKf,SAAWwF,EAAIxF,SAC9BiD,GAAUlC,KAAKhD,SAAWyH,EAAIzH,SAC9BkF,GAAUlC,KAAK3C,YAAckD,EAAgB1C,MAAM,GACnDiB,QAAQC,IAAIwB,EAAgB1C,OAE5B6B,OACKC,IAAI,6BACD,CACIC,OAAQ,CACJwE,SAAUK,EAAIxF,YAGzBR,KAAMqB,IACHJ,OAAQC,IAAI,2CACR,CACIC,OAAQ,CACJyE,aAAcvE,EAAIE,KAAK,MAG9BvB,KAAMqB,IACHM,EAAuBvC,MAAQiC,EAAIE,KAAKA,KAAKsE,WAC7CpC,GAAUlC,KAAKoC,mBAAqBhC,EAAuBvC,MAAM,GAAGyE,KACpED,GAAcxE,MAAQiC,EAAIE,KAAKA,KAAKsE,gBAIlDM,GAAYnI,iBAAI,IAChBoI,GAAYpI,iBAAI,GAChBqI,GAAcrI,iBAAI,GAClBsI,GAAStI,iBAAI,IAEbuI,GAAoBA,EAAGP,MAAKQ,eACzBA,EAAW,GAAK,GAAK,EACf,WACCA,EAAW,GAAK,GAAK,EACtB,gBADJ,EAMLvG,GAAUA,KACZ,GAAoB,IAAhBqG,GAAOlH,MAAa,CACpB,MAAMqH,EAAYH,GAAOlH,MACnBsH,EAAe,IAAIzC,KAAKwC,GACxBE,EAAqBD,EAAaE,eAAe,QAAS,CAAEC,KAAM,UAAWC,MAAO,UAAWC,IAAK,YAAaC,QAAQ,MAAO,KAEtIzG,EAAM4C,eAAiBwD,OAEvBpG,EAAM4C,eAAiB,GAGvB5C,EAAM4C,gBAAkB5C,EAAMiC,WAC9BtC,OAAUuC,MAAM,wBAChBwE,MAEJhG,OACKC,IAAI1D,GAAO,OAAQ,CAChB2D,OAAQZ,IAEXP,KAAMqB,IACH8E,GAAU/G,MAAQiC,EAAIE,KAAK2F,QAC3Bd,GAAUhH,MAAQiC,EAAIE,KAAK4F,SAGvClH,KAEA,MAAMmH,GAAeA,KACjB7G,EAAM2B,QAAU,EAChBjC,MAEEgH,GAAcA,KAChBX,GAAOlH,MAAQ,GACfmB,EAAMhC,SAAW,GACbgC,EAAMsC,UAAY,GAClBtC,EAAM0C,WAAa,GACnB1C,EAAM2C,MAAQ,GACd3C,EAAM4C,eAAiB,GACvB5C,EAAM6C,QAAU,GAChB7C,EAAM8C,QAAU,GAChB9C,EAAM+C,QAAU,GAChB/C,EAAMwC,SAAW,GACjBxC,EAAMyC,QAAU,GAChBzC,EAAMiC,SAAW,GACrBvC,MAGEoH,GAAoBC,IACtB/G,EAAMgD,SAAW+D,EACjBrH,MAGEsH,GAAeC,GACI,IAAjBA,EAAMC,OACC,eACiB,IAAjBD,EAAMC,OACN,kBAEA,GAGTC,GAAmBC,IAErB,MAAMC,EAAUD,EAAK1B,MAAM,KACrB4B,EAAU,IAAI5D,KAAK2D,EAAQ,IAC3BE,EAAQ,IAAI7D,KAClB,OAAI4D,EAAUC,EACH,mBAEA,uBAGTC,GAAgB/J,iBAAI,IACpBgK,GAAUA,KACZvE,GAAUlC,KAAK0G,oBAAsB,GACrCxE,GAAUlC,KAAK6B,QAAU,GACzBK,GAAUlC,KAAK8B,QAAU,GACzBI,GAAUlC,KAAK5C,YAAc,GAC7BK,EAASI,OAAQ,EACjBF,EAAOE,MAAQ,GACf0E,GAAU1E,MAAQ,GAClB2I,GAAc3I,MAAQ,GACtBsC,EAAsBtC,MAAQ,IAE5B8I,GAASA,KAEX7J,EAAQe,MAAM+I,SAAUC,IAChBlJ,EAAOE,MAAMqI,OAAS,EAEtBM,GAAc3I,MAAQ,IAAI0E,GAAU1E,SAAUF,EAAOE,OAErD2I,GAAc3I,MAAQ0E,GAAU1E,MAEpC,MAAMqH,EAAY/E,EAAsBtC,MAAM,GACxCyI,EAAUnG,EAAsBtC,MAAM,GAEtCsH,EAAe,IAAIzC,KAAKwC,GACxBE,EAAqBD,EAAaE,eAAe,QAAS,CAAEC,KAAM,UAAWC,MAAO,UAAWC,IAAK,UAAWsB,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAAavB,QAAQ,MAAO,KAEvLwB,EAAa,IAAIvE,KAAK4D,GACtBY,EAAmBD,EAAW5B,eAAe,QAAS,CAAEC,KAAM,UAAWC,MAAO,UAAWC,IAAK,UAAWsB,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAAavB,QAAQ,MAAO,KAEnL0B,EAAa/B,EAAqB,IAAM8B,EAC9CpI,QAAQC,IAAIoI,GACZjF,GAAUlC,KAAK2B,MAAQ6E,GAAc3I,MAAMuJ,KAAK,KAChDtI,QAAQC,IAAImD,GAAUlC,KAAK2B,OAC3B,MAAM0F,EAAmBhF,GAAcxE,MAAMyJ,KAAKC,GAAQA,EAAKjF,OAASJ,GAAUlC,KAAKoC,oBAAoBD,GACrGjD,EAAc,EACpB,IAAI2H,EA6FA,OAAO,EA3FPvG,EAAmBzC,OAAQ,EAE3BsB,WAAW,KACP,MAAMC,EAAUC,OAAUC,QAAQ,CAC9BC,MAAM,EAENC,KAAM,kBAENC,WAAY,6BAEhBC,OACKC,IAAI,6BACD,CACIC,OAAQ,CACJwE,SAAUlC,GAAUlC,KAAKf,YAGpCR,KAAMqB,IACHJ,eAAQ,CACJ8H,IAAK,iCACLC,OAAQ,OACRzH,KAAM,CACF0H,SAAU5H,EAAIE,KAAK,GACnBf,SAAUiD,GAAUlC,KAAKf,SACzB0I,QAASzF,GAAUlC,KAAK2B,MACxBiG,SAAU1F,GAAUlC,KAAKhD,SACzBK,YAAa6E,GAAUlC,KAAK3C,YAC5BwK,OAAQ3F,GAAUlC,KAAK5C,YACvB+J,WAAYA,EACZE,iBAAkBA,EAClBjF,mBAAoBF,GAAUlC,KAAKoC,mBACnCP,QAASK,GAAUlC,KAAK6B,QACxBC,QAASI,GAAUlC,KAAK8B,WAE7BrD,KAAMqB,IACgB,GAAjBA,EAAIE,KAAK8H,MACTtL,EAAWqB,MAAQiC,EAAIE,KAAKA,KAE5BxD,EAAWqB,MAAMkK,QAAQR,IACrB,MAAMS,EAAMT,EAAK7C,MAAM,MACI,QAAvBsD,EAAIA,EAAI9B,OAAS,GAEjBxJ,EAAcmB,MAAMC,KAAKyJ,GAEzB3K,EAAaiB,MAAMC,KAAKyJ,KAKE,GAA9B7K,EAAcmB,MAAMqI,SACpB+B,eAAe,CACXC,MAAO,OACPhL,QAAS,OAASR,EAAcmB,MAAMqI,OAAS,MAC/C1H,KAAM,UACN2J,OAAQ,MAEZ/I,EAAQW,SAEqB,GAA7BnD,EAAaiB,MAAMqI,SACnB+B,eAAe,CACXC,MAAO,OACPhL,QAAS,OAASN,EAAaiB,MAAMqI,OAAS,MAC9C1H,KAAM,QACN2J,OAAQ,IACRC,SAAU,aAEdhJ,EAAQW,QACRrC,EAAyBG,OAAQ,GAGrCjB,EAAaiB,MAAMkK,QAAQR,IAEvB,MAAMS,EAAMT,EAAK7C,MAAM,MACjB2D,EAAeL,EAAIA,EAAI9B,OAAS,GACtCpH,QAAQC,IAAIiJ,EAAI,IAChBlJ,QAAQC,IAAIsJ,GACZxL,EAAiBgB,MAAMC,KAAK,CACxB6J,QAASK,EAAI,GACbK,aAAcA,MAGtB3J,KACA+H,OAEAnG,EAAmBzC,OAAQ,EAC3Bc,OAAUuC,MAAMpB,EAAIE,KAAKsI,WAI1CpJ,MAOTqJ,GAAoBxC,IACtB/G,EAAM2B,QAAUoF,EAChBrH,MAGE8J,GAAkBC,IACpB3J,QAAQC,IAAI0J,GAEZ3D,GAAYjH,OAAS,EACjBiH,GAAYjH,OAAS,IAEjB4K,EAAEC,OAAS,GAEX5J,QAAQC,IAAI,QACZ+F,GAAYjH,MAAQ,EACpBsD,EAActD,OAAQ,EACtB4C,EAAW5C,MAAQ,MAGnBiB,QAAQC,IAAI,QACZ+F,GAAYjH,MAAQ,EACpBsD,EAActD,OAAQ,EACtB4C,EAAW5C,MAAQ,O,OAI/B8K,uBAAU,KAENnI,EAAS3C,OAAS2C,EAAS3C,MAAM+K,MAAMC,YAAYC,iBAAiB,aAAcN,MAGtFO,yBAAY,KAERvI,EAAS3C,OAAS2C,EAAS3C,MAAM+K,MAAMC,YAAYG,oBAAoB,aAAcR,M,qnoBC9iCzF,MAAMS,GAAc,GAEL,iB,qBCPflN,EAAOC,QAAU,IAA0B,mC,qBCA3CD,EAAOC,QAAU,IAA0B,0B,qBCA3CD,EAAOC,QAAU,IAA0B,kC,qBCA3CD,EAAOC,QAAU,IAA0B", "file": "js/chunk-da2713c2.cfa5ded6.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"img/UserOwner.39a18125.svg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./MonthTicket.vue?vue&type=style&index=0&id=13825d14&lang=scss\"", "module.exports = __webpack_public_path__ + \"img/TicketName.11d93b78.svg\";", "module.exports = __webpack_public_path__ + \"img/AddCarNo.c21ec35a.svg\";", "module.exports = __webpack_public_path__ + \"img/DeletedCarNo.d4c8a72a.svg\";", "module.exports = __webpack_public_path__ + \"img/IsFrozen.663a756a.svg\";", "module.exports = __webpack_public_path__ + \"img/ParkName.9992ed7c.svg\";", "module.exports = __webpack_public_path__ + \"img/UserPhone.aa10d5d3.svg\";", "module.exports = __webpack_public_path__ + \"img/VIPInfo.ecd592f3.svg\";", "module.exports = __webpack_public_path__ + \"img/Remark2.a8ad955d.svg\";", "module.exports = __webpack_public_path__ + \"img/AddBlack.553d8f3b.svg\";", "module.exports = __webpack_public_path__ + \"img/Remark3.25010642.svg\";", "module.exports = __webpack_public_path__ + \"img/ValidStatus.e621a19e.svg\";", "<template>\r\n    <div style=\"\">\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/ReleaseReason.svg\"></i>&nbsp; 月票管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"1060px\">\r\n                <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                    <!-- 将下述的几个选择条件重新排列位置 -->\r\n                    <el-select v-model=\"query.parkName\" style=\"width: 120px;\" clearable @click=\"changeParkName\"\r\n                        @clear=\"query.parkName = ''\">\r\n                        <el-option label=\"万象上东\" value=\"万象上东\" />\r\n                        <el-option label=\"四季上东\" value=\"四季上东\" />\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label-width=\"80px\" label=\"月票状态\">\r\n                    <!-- 将下述的几个选择条件重新排列位置 -->\r\n                    <el-select v-model=\"query.isValid\" style=\"width: 160px;\" placeholder=\"请选择月票状态\" clearable\r\n                        @clear=\"clearValid\">\r\n                        <el-option label=\"生效中\" value=\"1\" />\r\n                        <el-option label=\"已过期\" value=\"4\" />\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label-width=\"80px\" label=\"冻结状态\">\r\n                    <!-- 将下述的几个选择条件重新排列位置 -->\r\n                    <el-select v-model=\"query.isFrozen\" style=\"width: 160px;\" placeholder=\"请选择冻结状态\" clearable\r\n                        @clear=\"clearFrozen\">\r\n                        <el-option label=\"未冻结\" value=\"0\" />\r\n                        <el-option label=\"已冻结\" value=\"2\" />\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label-width=\"80px\" label=\"月票名称\">\r\n                    <el-select v-model=\"query.ticketName\" style=\"width: 210px;\" placeholder=\"请选择月票名称\" clearable\r\n                        @clear=\"clearTicketName\">\r\n                        <el-option v-for=\"item in monthTicketTypeList\" :key=\"item.ticketName\" :label=\"item.ticketName\"\r\n                            :value=\"item.ticketName\">\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-tooltip content=\"搜索\" placement=\"top\">\r\n                    <el-button type=\"primary\" class=\"searchButton\" size=\"small\" icon=\"el-icon-search\"\r\n                        @click=\"handleSearch\"></el-button>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"重置\" placement=\"top\">\r\n                    <el-button type=\"warning\" class=\"onsetButton\" size=\"small\" icon=\"el-icon-refresh\"\r\n                        @click=\"handleOnset\"></el-button>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"同步数据\" placement=\"top\">\r\n                    <el-button type=\"primary\" class=\"sycButton\" size=\"small\" icon=\"el-icon-sort\" @click=\"handleSyc\">\r\n                    </el-button>\r\n                </el-tooltip>\r\n                <el-button type=\"text\" class=\"toggleClass\" @click=\"toggleForm\">{{ isFormVisible ? '收起' : '展开' }}<i\r\n                        :class=\"isFormVisible ? 'el-icon-caret-top' : 'el-icon-caret-bottom'\"></i></el-button>\r\n            </el-form>\r\n            <el-form v-if=\"isFormVisible\" :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"1060px\">\r\n                <!-- 输入车牌号码 -->\r\n                <el-form-item label-width=\"80px\" label=\"车牌号码\" style=\"margin-left: 10px; margin-top: 1px;\">\r\n                    <el-input v-model=\"query.carNo\" style=\"width: 160px;\" placeholder=\"请输入车牌号码\" clearable\r\n                        @clear=\"clearCarNo\" />\r\n                </el-form-item>\r\n                <!-- 车主信息输入框 -->\r\n                <el-form-item label-width=\"80px\" label=\"车主姓名\" style=\"margin-left: 28px;\">\r\n                    <el-input v-model=\"query.userName\" style=\"width: 160px;\" placeholder=\"请输入车主姓名\" clearable\r\n                        @clear=\"clearUserName\" />\r\n                </el-form-item>\r\n                <!-- 手机号码输入框 -->\r\n                <el-form-item label-width=\"80px\" label=\"车主电话\" style=\"margin-left: 42px;\">\r\n                    <el-input v-model=\"query.userPhone\" style=\"width: 160px;\" @input=\"handleInputPhone\"\r\n                        placeholder=\"请输入车主电话\" clearable @clear=\"clearUserPhone\" />\r\n                </el-form-item>\r\n                <!-- 多少天内到期输入框 -->\r\n                <el-form-item label-width=\"100px\" style=\"margin-left: 45px;\">\r\n                    <el-input v-model=\"query.timeDays\" style=\"max-width: 328px\" @input=\"handleInput\"\r\n                        @clear=\"clearTimeDays\" placeholder=\"请输入有效期天数\" clearable>\r\n                        <template #append>天后到期</template>\r\n                        <template #prepend>月票</template>\r\n                    </el-input>\r\n                </el-form-item>\r\n                <!-- 月票有效期时间日期选择器 -->\r\n                <el-form-item label-width=\"80px\" label=\"到期日期\" style=\"margin-left: 8px;\">\r\n                    <el-date-picker :clearable=\"false\" style=\"width: 160px;\" v-model=\"value2\" type=\"date\"\r\n                        placeholder=\"请选择日期\" :shortcuts=\"shortcuts\" />\r\n                </el-form-item>\r\n                <!-- 备注1输入框 -->\r\n                <el-form-item label-width=\"80px\" label=\"备注1\" style=\"margin-left: 10px;\">\r\n                    <el-input v-model=\"query.remark1\" style=\"width: 180px;\" placeholder=\"请输入备注1\" clearable\r\n                        @clear=\"clearRemark1\" />\r\n                </el-form-item>\r\n                <!-- 备注2输入框 -->\r\n                <el-form-item label-width=\"80px\" label=\"备注2\" style=\"margin-left: 45px;\">\r\n                    <el-input v-model=\"query.remark2\" style=\"width: 180px;\" placeholder=\"请输入备注2\" clearable\r\n                        @clear=\"clearRemark2\" />\r\n                </el-form-item>\r\n                <!-- 备注3输入框 -->\r\n                <el-form-item label-width=\"80px\" label=\"备注3\" style=\"margin-left: 15px; margin-bottom: 1px\">\r\n                    <el-input v-model=\"query.remark3\" style=\"width: 180px;\" placeholder=\"请输入备注3\" clearable\r\n                        @clear=\"clearRemark3\" />\r\n                </el-form-item>\r\n            </el-form>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"TableRef\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\" @selection-change=\"selectChanged\"\r\n                style=\"width: 100%\" :height=\"heightData\" @scroll=\"scrollBehavior\">\r\n                <el-table-column label=\"车场名称\" prop=\"parkName\" align=\"center\" fixed=\"left\" width=\"90\">\r\n                </el-table-column>\r\n                <el-table-column label=\"车主姓名\" prop=\"userName\" align=\"center\" fixed=\"left\" width=\"100\">\r\n                </el-table-column>\r\n                <el-table-column label=\"车主手机号\" prop=\"userPhone\" align=\"center\" fixed=\"left\" width=\"130\">\r\n                </el-table-column>\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in props\" :key=\"item.prop\" align=\"center\" width=\"200\">\r\n                </el-table-column>\r\n                <el-table-column label=\"月票状态\" prop=\"validStatus\" align=\"center\" width=\"95\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"success\" v-if=\"scope.row.validStatus === 1\" effect=\"dark\" size=\"large\"\r\n                            :round=\"true\">生效中</el-tag>\r\n                        <el-tag type=\"info\" v-if=\"scope.row.validStatus === 4\" effect=\"dark\" round\r\n                            size=\"large\">已过期</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"冻结状态\" prop=\"isFrozen\" align=\"center\" width=\"95\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"primary\" v-if=\"scope.row.isFrozen === 0\" effect=\"light\" size=\"dark\"\r\n                            round>未冻结</el-tag>\r\n                        <el-tag type=\"warning\" v-else-if=\"scope.row.isFrozen === 2\" effect=\"dark\" round\r\n                            size=\"large\">已冻结</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-tooltip content=\"月票详情\" placement=\"top\">\r\n                            <el-button type=\"primary\" @click=\"handleEdit(scope.row)\" size=\"small\" circle plain>\r\n                                <i><img src=\"..//../icons/svg/VIPInfo.svg\"></i></el-button>\r\n                        </el-tooltip>\r\n                        <el-tooltip content=\"添加黑名单\" placement=\"top\">\r\n                            <el-button type=\"danger\" @click=\"handleAddBlack(scope.row)\" size=\"small\" circle plain>\r\n                                <i><img src=\"..//../icons/svg/AddBlack.svg\"></i></el-button>\r\n                        </el-tooltip>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 弹窗展示月票车辆信息 -->\r\n        <el-dialog title=\"车辆月票信息\" v-model=\"dialogVisibleUpdate\" width=\"60%\">\r\n            <el-descriptions class=\"margin-top\" :column=\"4\" border direction=\"vertical\" :v-model=\"descriptionData.data\">\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"parkName\">\r\n                            <i><img src=\"..//../icons/svg/ParkName.svg\"></i>&nbsp;\r\n                            车场名称\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.parkName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"ownerName\">\r\n                            <i><img src=\"..//../icons/svg/UserOwner.svg\"></i>&nbsp;\r\n                            车主姓名\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.userName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"cell-item\">\r\n                            <i><img src=\"..//../icons/svg/UserPhone.svg\"></i>&nbsp;\r\n                            车主电话\r\n                        </div>\r\n                    </template>\r\n                    <el-tag size=\"large\" type=\"warning\">{{ descriptionData.data.userPhone }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"ticketName\">\r\n                            <i><img src=\"..//../icons/svg/TicketName.svg\"></i>&nbsp;\r\n                            月票名称\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.ticketName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div>\r\n                            <i><img src=\"..//../icons/svg/ValidStatus.svg\"></i>&nbsp;\r\n                            月票状态\r\n                        </div>\r\n                    </template>\r\n                    <div class=\"containerX\">\r\n                        <el-tag type=\"success\" v-if=\"descriptionData.data.isValid === 1\" effect=\"dark\" size=\"large\"\r\n                            :round=\"true\">生效中</el-tag>\r\n                        <el-tag type=\"info\" v-if=\"descriptionData.data.isValid === 4\" effect=\"dark\" round\r\n                            size=\"large\">已过期</el-tag>\r\n                    </div>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div>\r\n                            <i><img src=\"..//../icons/svg/IsFrozen.svg\"></i>&nbsp;\r\n                            冻结状态\r\n                        </div>\r\n                    </template>\r\n                    <div class=\"containerX\">\r\n                        <!-- 根据车牌的长度匹配css样式，长度若是7的话选用containerTag，若是8的话选用containerEnergy -->\r\n                        <el-tag type=\"primary\" v-if=\"descriptionData.data.isFrozen === 0\" effect=\"light\" size=\"dark\"\r\n                            round>未冻结</el-tag>\r\n                        <el-tag type=\"warning\" v-else-if=\"descriptionData.data.isFrozen === 2\" effect=\"dark\" round\r\n                            size=\"large\">已冻结</el-tag>\r\n                    </div>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"cell-item\">\r\n                            <i><img src=\"..//../icons/svg/Remark1.svg\"></i>&nbsp;\r\n                            备注1\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.remark1 }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"cell-item\">\r\n                            <i><img src=\"..//../icons/svg/Remark2.svg\"></i>&nbsp;\r\n                            备注2\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.remark2 }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"cell-item\">\r\n                            <i><img src=\"..//../icons/svg/Remark3.svg\"></i>&nbsp;\r\n                            备注3\r\n                        </div>\r\n                    </template>\r\n                    {{ descriptionData.data.remark3 }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div class=\"cell-item\">\r\n                            <i><img src=\"..//../icons/svg/TimePeriodList.svg\"></i>&nbsp;\r\n                            月票有效期\r\n                        </div>\r\n                    </template>\r\n                    <div class=\"containerdex\">\r\n                        <el-tag size=\"large\" type=\"success\" effect=\"dark\" class=\"containerTagTime\"\r\n                            v-for=\"(time, index) in timePeriodListResult\" :key=\"index\" :class=\"getTagClassTime(time)\">\r\n                            {{ time }}</el-tag>\r\n                    </div>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                    <template #label>\r\n                        <div>\r\n                            <i><img src=\"..//../icons/svg/CarNo.svg\"></i>&nbsp;\r\n                            开通车牌\r\n                        </div>\r\n                    </template>\r\n                    <div class=\"containerX\">\r\n                        <!-- 根据车牌的长度匹配css样式，长度若是7的话选用containerTag，若是8的话选用containerEnergy -->\r\n                        <el-tag size=\"large\" type=\"success\" effect=\"dark\" class=\"containerTag\"\r\n                            v-for=\"(plate, index) in carNoList\" :key=\"index\" :class=\"getTagClass(plate)\">\r\n                            {{ plate }}</el-tag>\r\n                    </div>\r\n                </el-descriptions-item>\r\n            </el-descriptions>\r\n        </el-dialog>\r\n        <!-- 添加黑名单信息 -->\r\n        <el-dialog title=\"添加黑名单车辆信息\" v-model=\"dialogVisibleBlack\" width=\"40%\">\r\n            <el-form :model=\"blackData.data\" :rules=\"rules\" ref=\"formRef\" label-width=\"100px\">\r\n                <el-form-item label=\"车场名称\" prop=\"parkName\">\r\n                    <el-input :style=\"{ width: 150 + 'px' }\" v-model=\"blackData.data.parkName\" disabled></el-input>\r\n                </el-form-item>\r\n                <el-form-item label=\"黑名单名称\" prop=\"name\">\r\n                    <!-- 下拉框选项：1、永久；2、自定义，显示自定义的时候才显示下变得日期时间组件，默认不显示 -->\r\n                    <el-select v-model=\"blackData.data.specialCarTypeName\" placeholder=\"请选择黑名单名称\">\r\n                        <el-option v-for=\"item in specialCarTypeNameList\" :key=\"item.name\" :label=\"item.name\"\r\n                            :value=\"item.id\">\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"车牌号码\" prop=\"carNo\">\r\n                    <div class=\"form-container\">\r\n                        <!-- 根据carNoList的数量进行创建输入框并将carNoList中的每个元素添加到创建的输入框中 -->\r\n                        <el-input v-for=\"(plate, index) in carNoList\" :key=\"index\" style=\"width: 25%;\"\r\n                            v-model=\"carNoList[index]\" disabled> </el-input>\r\n                        <i><img src=\"..//../icons/svg/AddCarNo.svg\" @click=\"addInput\" style=\"margin-left: 15px\"></i>\r\n                        <i><img src=\"..//../icons/svg/DeletedCarNo.svg\" @click=\"removeInput(index)\"></i>\r\n                        <div v-for=\"(input, index) in inputs\" :key=\"index\" class=\"input-container\">\r\n                            <el-input v-model=\"inputs[index]\" style=\"margin-top: 5px; margin-right: -15px\"\r\n                                placeholder=\"请输入车牌\"></el-input>\r\n                        </div>\r\n                    </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"车主姓名\" prop=\"userName\">\r\n                    <el-input v-model=\"blackData.data.userName\" style=\"width: 40%\" placeholder=\"请输入车主姓名\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"黑名单原因\" prop=\"blackReason\">\r\n                    <el-input placeholder=\"请输入黑名单原因\" type=\"textarea\" v-model=\"blackData.data.blackReason\"\r\n                        style=\"width: 70%\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"黑名单生效期\">\r\n                    <!-- 下拉框选项：1、永久；2、自定义，显示自定义的时候才显示下变得日期时间组件，默认不显示 -->\r\n                    <el-select v-model=\"blackData.data.isPermament\" placeholder=\"请选择生效期\">\r\n                        <el-option v-for=\"(item) in isPermamentList\" :key=\"item\" :label=\"item\" :value=\"item\">\r\n                        </el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item v-if=\"blackData.data.isPermament == '自定义'\" style=\"margin-top: 20px\">\r\n                    <el-date-picker v-model=\"timePeriodListResults\" type=\"datetimerange\" :shortcuts=\"shortcuts\"\r\n                        range-separator=\"至\" start-placeholder=\"开始时间\" end-placeholder=\"结束时间\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\" prop=\"blackRemark\">\r\n                    <!-- 默认 -->\r\n                    <el-button class=\"button-remark\" @click=\"changeIcon\"\r\n                        :icon=\"isRemark ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></el-button>\r\n                </el-form-item>\r\n                <div v-if=\"isRemark\">\r\n                    <el-form-item label=\"备注1\" prop=\"blackRemark1\">\r\n                        <el-input placeholder=\"请输入备注1\" type=\"textarea\" v-model=\"blackData.data.remark1\"\r\n                            style=\"width: 70%\" />\r\n                    </el-form-item>\r\n                    <el-form-item label=\"备注2\" prop=\"blackRemark2\">\r\n                        <el-input placeholder=\"请输入备注2\" type=\"textarea\" v-model=\"blackData.data.remark2\"\r\n                            style=\"width: 70%\" />\r\n                    </el-form-item>\r\n                </div>\r\n            </el-form>\r\n            <template #footer>\r\n                <span class=\"dialog-footer\">\r\n                    <el-button @click=\"dialogVisibleBlack = false\">取 消</el-button>\r\n                    <el-button type=\"primary\" @click=\"update\">确 定</el-button>\r\n                </span>\r\n            </template>\r\n        </el-dialog>\r\n        <!-- 添加抽屉面板 -->\r\n        <el-drawer ref=\"drawerRef\" title=\"黑名单添加失败数据\" size=\"550px\" direction=\"rtl\" v-model=\"importNotificationDefeat\"\r\n            :before-close=\"handleClose\" close-on-click-modal=\"false\" close-on-press-escape=\"false\">\r\n            <el-table :data=\"importDefeatData\" border class=\"table\" ref=\"multipleTable\"\r\n                header-cell-class-name=\"table-header\">\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\"\r\n                    v-for=\"item in importProps\" :key=\"item.importProps\" align=\"center\">\r\n                </el-table-column>\r\n            </el-table>\r\n        </el-drawer>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref, onMounted, onUnmounted, } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox, ElNotification, ElLoading } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\nconst root = \"/parking/monthTicket/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    { label: \"月票名称\", prop: \"ticketName\" },\r\n    // { label: \"车主姓名\", prop: \"userName\" },\r\n    // { label: \"车主电话\", prop: \"userPhone\" },\r\n    { label: \"开通车牌\", prop: \"carNo\" },\r\n    { label: \"有效期\", prop: \"timePeriodList\" },\r\n    { label: \"备注1\", prop: \"remark1\" },\r\n    { label: \"备注2\", prop: \"remark2\" },\r\n    { label: \"备注3\", prop: \"remark3\" },\r\n    // { label: \"月票状态\", prop: \"\" },\r\n    // { label: \"冻结状态\", prop: \"\" }\r\n];\r\nconst importData = ref([]);\r\n// 导入成功 提醒\r\nconst importSuccess = ref([]);\r\nconst importProps = [\r\n    { label: \"车牌号码\", prop: \"carCode\" },\r\n    { label: \"导入失败原因\", prop: \"defeatReason\" }\r\n];\r\n// 导入失败 提醒\r\nconst importDefeat = ref([]);\r\nconst importDefeatData = ref([]);\r\nconst formRef = ref(null);\r\nconst rules = {\r\n    userName: [\r\n        {\r\n            required: true,\r\n            message: \"请输入车主姓名\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    blackReason: [\r\n        {\r\n            required: true,\r\n            message: \"请输入黑名单原因\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    isPermament: [\r\n        {\r\n            required: true,\r\n            message: \"请选择黑名单有效期\",\r\n            trigger: \"change\",\r\n        },\r\n    ]\r\n};\r\nconst activeNames = ref([]); // 默认展开的折叠面板\r\nconst applicantUserId = ref(\"\");\r\nconst IconName = ref(\"el-icon-arrow-down\");\r\n// 调用后端接口获取月票类型数据\r\nconst monthTicketTypeList = ref([])\r\nconst isRemark = ref(false)\r\n\r\nconst importNotificationDefeat = ref(false);\r\nconst inputs = ref([]);\r\n// 添加输入框的方法\r\nconst addInput = () => {\r\n    inputs.value.push(''); // 向数组中添加一个新的空字符串\r\n};\r\n// 删除输入框的方法\r\nconst removeInput = (index) => {\r\n    // 如何将全部元素移除，且数据也清空\r\n    inputs.value.splice(index, 1); // 从数组中移除指定索引的元素\r\n};\r\nconst handleClose = (done) => {\r\n    ElMessageBox.confirm(\r\n        '您是否需要关闭窗口?',\r\n        '提示',\r\n        {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning',\r\n        }\r\n    )\r\n        .then(() => {\r\n            // 清空存储的数据\r\n            importData.value = [];\r\n            importSuccess.value = [];\r\n            importDefeatData.value = [];\r\n            importDefeat.value = [];\r\n            importNotificationDefeat.value = false;\r\n            getData()\r\n            ElMessage({\r\n                type: 'success',\r\n                message: '关闭成功',\r\n            })\r\n        })\r\n        .catch(() => {\r\n            getData()\r\n        })\r\n};\r\nconst handleSyc = () => {\r\n    // 同步数据操作\r\n    console.log(query.parkName)\r\n    const loadingTime = 0;\r\n    // dialogVisible.value = false\r\n    // 使用setTimeout模拟异步请求\r\n    // 添加提示框选择更新哪种数据\r\n    ElMessageBox.confirm('请选择更新哪种数据', '提示', {\r\n        confirmButtonText: '生效中的数据',\r\n        cancelButtonText: '已过期的数据',\r\n        type: 'warning',\r\n    }).then(() => {\r\n        setTimeout(() => {\r\n            const loading = ElLoading.service({\r\n                lock: true,\r\n                text: '正在更新数据，请稍后.....',\r\n                background: 'rgba(255, 255, 255, 0.7)',\r\n            })\r\n            request\r\n                .get(\"parking/monthTicket/AKEPage\", {\r\n                    params: {\r\n                        parkName: query.parkName,\r\n                        validStatus: \"1\",\r\n                    },\r\n                })\r\n                .then((res) => {\r\n                    loading.close()\r\n                    console.log(res.data.data)\r\n                    ElMessage.success(\"成功更新\" + res.data.data + \"数据!\");\r\n                    getData()\r\n                });\r\n        }, loadingTime);\r\n    }).catch(() => {\r\n        setTimeout(() => {\r\n            const loading = ElLoading.service({\r\n                lock: true,\r\n                text: '正在更新数据，请稍后.....',\r\n                background: 'rgba(255, 255, 255, 0.7)',\r\n            })\r\n            request\r\n                .get(\"parking/monthTicket/AKEPage\", {\r\n                    params: {\r\n                        parkName: query.parkName,\r\n                        validStatus: \"4\",\r\n                    },\r\n                })\r\n                .then((res) => {\r\n                    loading.close()\r\n                    console.log(res.data.data)\r\n                    ElMessage.success(\"成功更新\" + res.data.data + \"数据!\");\r\n                    getData()\r\n                });\r\n        }, loadingTime);\r\n    })\r\n\r\n\r\n\r\n};\r\nconst timePeriodListResult = ref([])\r\nconst timePeriodListResults = ref([])\r\nconst specialCarTypeNameList = ref([]);\r\n// const heightTable = ref(350)\r\n// const tableMarginTop = ref(0)\r\nconst dialogVisibleUpdate = ref(false)\r\nconst dialogVisibleBlack = ref(false)\r\nconst isPermamentList = ref(['永久', '自定义'])\r\nconst TableRef = ref(null)\r\nconst heightData = ref(350)\r\n// 调用后端接口更新月票数据库数据\r\n// 创建新的备注信息\r\nconst changeParkName = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n}\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n// 月票有效期输入限制\r\nconst handleInput = (value) => {\r\n    const regex = /^[0-9]*$/;\r\n    if (regex.test(value)) {\r\n        query.timeDays = value;\r\n        console.log(value)\r\n    } else {\r\n        // 输入不合法时，清除该输入框内容\r\n        query.timeDays = \"\";\r\n        // 同时提醒用户输入数字\r\n        ElMessage.error(\"请输入数字类型\");\r\n        return;\r\n    }\r\n};\r\nconst isFormVisible = ref(false);\r\n// 打开折叠板\r\nconst toggleForm = () => {\r\n    isFormVisible.value = !isFormVisible.value;\r\n    if (isFormVisible.value) {\r\n        heightData.value = 300; // 减少一定高度\r\n    } else {\r\n        heightData.value = 400; // 增加一定高度\r\n    }\r\n};\r\nconst handleInputPhone = (value) => {\r\n    const regex = /^[0-9]*$/;\r\n    if (regex.test(value)) {\r\n        query.userPhone = value;\r\n        console.log(value)\r\n    } else {\r\n        // 输入不合法时，清除该输入框内容\r\n        query.userPhone = \"\";\r\n        // 同时提醒用户输入数字\r\n        ElMessage.error(\"请输入数字类型\");\r\n        return;\r\n    }\r\n};\r\nconst query = reactive({\r\n    isFrozen: \"\",\r\n    isValid: \"\",\r\n    parkName: \"万象上东\",\r\n    ticketName: \"\",\r\n    userName: \"\",\r\n    userPhone: \"\",\r\n    carNo: \"\",\r\n    timePeriodList: \"\",\r\n    remark1: \"\",\r\n    remark2: \"\",\r\n    remark3: \"\",\r\n    timeDays: \"\", //还有多少天到期\r\n    pageNum: 1,\r\n    pageSize: 10,\r\n});\r\nconst descriptionData = reactive({\r\n    data: {\r\n        isFrozen: -1,\r\n        isValid: -1,\r\n        parkName: \"\",\r\n        ticketName: \"\",\r\n        userName: \"\",\r\n        userPhone: \"\",\r\n        carNo: \"\",\r\n        timePeriodList: \"\",\r\n        remark1: \"\",\r\n        remark2: \"\",\r\n        remark3: \"\",\r\n    }\r\n});\r\nconst blackData = reactive({\r\n    data: {\r\n        id: \"\",\r\n        parkName: \"\",\r\n        // 创建个数组\r\n        specialCarTypeName: \"\",\r\n        userName: \"\",\r\n        userPhone: \"\",\r\n        carNo: \"\",\r\n        isPermament: \"\",\r\n        remark1: \"\",\r\n        remark2: \"\",\r\n    }\r\n});\r\nconst blackNameList = reactive({\r\n    id: \"\",\r\n    name: \"\"\r\n});\r\nconst carNoList = ref([]);\r\nconst shortcuts = [\r\n    {\r\n        text: '后一天',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一天的日期\r\n            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);\r\n            // 设置时间为8:00:00\r\n            const endTime = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 23, 59, 59);\r\n            // 设置时间为7:59:59\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, endTime]\r\n        },\r\n    },\r\n    {\r\n        text: '后一周',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一周的日期\r\n            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000 * 7);\r\n            // 设置时间为8:00:00\r\n            const endTime = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 23, 59, 59);\r\n            // 设置时间为7:59:59\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, endTime]\r\n        },\r\n    },\r\n    {\r\n        text: '后一个月',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一个月的日期\r\n            // 计算后一个月的日期\r\n            const oneMonthLater = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());\r\n            const oneMonthEndTime = new Date(oneMonthLater.getFullYear(), oneMonthLater.getMonth(), oneMonthLater.getDate(), 23, 59, 59);\r\n            const oneMonthStartTime = new Date(oneMonthLater.getFullYear(), oneMonthLater.getMonth(), oneMonthLater.getDate(), 0, 0, 0);\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, oneMonthEndTime]\r\n        },\r\n    },\r\n    {\r\n        text: '后三个月',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一个月的日期\r\n            // 计算后三个月的日期\r\n            const threeMonthsLater = new Date(now.getFullYear(), now.getMonth() + 3, now.getDate());\r\n            const threeMonthsEndTime = new Date(threeMonthsLater.getFullYear(), threeMonthsLater.getMonth(), threeMonthsLater.getDate(), 23, 59, 59);\r\n            const threeMonthsStartTime = new Date(threeMonthsLater.getFullYear(), threeMonthsLater.getMonth(), threeMonthsLater.getDate(), 0, 0, 0);\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, threeMonthsEndTime]\r\n        },\r\n    },\r\n    {\r\n        text: '后一年',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一个月的日期\r\n            // 计算一年后的日期\r\n            const oneYearLater = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\r\n            const oneYearEndTime = new Date(oneYearLater.getFullYear(), oneYearLater.getMonth(), oneYearLater.getDate(), 23, 59, 59);\r\n            const oneYearStartTime = new Date(oneYearLater.getFullYear(), oneYearLater.getMonth(), oneYearLater.getDate(), 0, 0, 0);\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, oneYearEndTime]\r\n        },\r\n    },\r\n    {\r\n        text: '后十年',\r\n        value: () => {\r\n            // 获取当前时间\r\n            const now = new Date();\r\n            // 计算后一个月的日期\r\n            const tenYearsLater = new Date(now.getFullYear() + 10, now.getMonth(), now.getDate());\r\n            const tenYearsEndTime = new Date(tenYearsLater.getFullYear(), tenYearsLater.getMonth(), tenYearsLater.getDate(), 23, 59, 59);\r\n            const tenYearsStartTime = new Date(tenYearsLater.getFullYear(), tenYearsLater.getMonth(), tenYearsLater.getDate(), 0, 0, 0);\r\n            const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);\r\n            return [startTime, tenYearsEndTime]\r\n        },\r\n    }\r\n];\r\nconst clearTicketName = () => {\r\n    query.ticketName = '';\r\n    getData();\r\n}\r\nconst clearCarNo = () => {\r\n    query.carNo = '';\r\n    getData();\r\n};\r\nconst clearUserName = () => {\r\n    query.userName = '';\r\n    getData();\r\n}; const clearUserPhone = () => {\r\n    query.userPhone = '';\r\n    getData();\r\n};\r\nconst clearTimeDays = () => {\r\n    query.timeDays = ''\r\n    getData();\r\n};\r\nconst clearRemark1 = () => {\r\n    query.remark1 = ''\r\n    getData();\r\n};\r\nconst clearRemark2 = () => {\r\n    query.remark2 = ''\r\n    getData();\r\n};\r\nconst clearRemark3 = () => {\r\n    query.remark3 = ''\r\n    getData();\r\n};\r\nconst clearValid = () => {\r\n    query.isValid = ''\r\n    getData();\r\n};\r\nconst clearFrozen = () => {\r\n    query.isFrozen = ''\r\n    getData();\r\n};\r\nrequest\r\n    .get(\"/parking/yardInfo/yardCode\",\r\n        {\r\n            params: {\r\n                yardName: query.parkName\r\n            },\r\n        })\r\n    .then((res) => {\r\n        request.get(\"/parking/monthTicket/getMonthTicketConfigDetailList\",\r\n            {\r\n                params: {\r\n                    parkCodeList: res.data[0]\r\n                },\r\n            })\r\n            .then((res) => {\r\n                monthTicketTypeList.value = res.data.data.recordList;\r\n            })\r\n    });\r\nconst changeIcon = () => {\r\n    isRemark.value = !isRemark.value;\r\n    if (isRemark.value == false) {\r\n        IconName.value = \"el-icon-arrow-up\";\r\n    } else {\r\n        IconName.value = \"el-icon-arrow-up\";\r\n    }\r\n    console.log(isRemark.value)\r\n}\r\n// 弹窗提示用户\r\nconst handleEdit = (row) => {\r\n    dialogVisibleUpdate.value = true\r\n    descriptionData.data.id = row.id\r\n    // 将row中的carNo按照\"，\"进行拆分成数组存储到carNoList中\r\n    carNoList.value = row.carNo.split(\",\")\r\n    console.log(row.userName)\r\n    // 将row中的timePeriodList数据到descriptionData.data中\r\n    timePeriodListResult.value = row.timePeriodList.split(\",\");\r\n    descriptionData.data.parkName = row.parkName\r\n    descriptionData.data.ticketName = row.ticketName\r\n    descriptionData.data.userName = row.userName\r\n    descriptionData.data.userPhone = row.userPhone\r\n    descriptionData.data.remark1 = row.remark1\r\n    descriptionData.data.remark2 = row.remark2\r\n    descriptionData.data.remark3 = row.remark3\r\n    descriptionData.data.carNo = row.carNo\r\n    descriptionData.data.isFrozen = row.isFrozen\r\n    descriptionData.data.isValid = row.validStatus\r\n};\r\nconst handleAddBlack = (row) => {\r\n    dialogVisibleBlack.value = true\r\n    blackData.data.id = row.id\r\n    // 将inputs的值赋值给carNoList\r\n    console.log(row.userName)\r\n    // 将row中的carNo按照\"，\"进行拆分成数组存储到carNoList中\r\n    carNoList.value = row.carNo.split(\",\")\r\n    blackData.data.parkName = row.parkName\r\n    blackData.data.userName = row.userName\r\n    blackData.data.isPermament = isPermamentList.value[0]\r\n    console.log(isPermamentList.value)\r\n    // 调用后端接口\r\n    request\r\n        .get(\"/parking/yardInfo/yardCode\",\r\n            {\r\n                params: {\r\n                    yardName: row.parkName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            request.get(\"/parking/blackList/getSpecialCarTypeList\",\r\n                {\r\n                    params: {\r\n                        parkCodeList: res.data[0]\r\n                    },\r\n                })\r\n                .then((res) => {\r\n                    specialCarTypeNameList.value = res.data.data.recordList\r\n                    blackData.data.specialCarTypeName = specialCarTypeNameList.value[0].name\r\n                    blackNameList.value = res.data.data.recordList;\r\n                })\r\n        });\r\n};\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst scrollCount = ref(0);\r\nconst value2 = ref(\"\");\r\n//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\n// 获取表格数据\r\nconst getData = () => {\r\n    if (value2.value != \"\") {\r\n        const startDate = value2.value\r\n        const newStartDate = new Date(startDate);\r\n        const formattedStartDate = newStartDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\\//g, '-');\r\n        // 将上述格式化后的开始时间和结束时间用\"-\"连接起来，作为查询条件\r\n        query.timePeriodList = formattedStartDate\r\n    } else {\r\n        query.timePeriodList = \"\";\r\n    }\r\n    // query的timePeriodList和timeDays不能同时赋值，若同时赋值的话提示用户\"这两项不可同时进行查询\"，接着调用重置按钮进行重置操作\r\n    if (query.timePeriodList && query.timeDays) {\r\n        ElMessage.error(\"到期日期 和 距离天数 不可同时进行查询\")\r\n        handleOnset()\r\n    }\r\n    request\r\n        .get(root + \"page\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n        });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\nconst handleOnset = () => {\r\n    value2.value = \"\";\r\n    query.userName = \"\",\r\n        query.userPhone = \"\",\r\n        query.ticketName = \"\",\r\n        query.carNo = \"\",\r\n        query.timePeriodList = \"\",\r\n        query.remark1 = \"\",\r\n        query.remark2 = \"\",\r\n        query.remark3 = \"\",\r\n        query.isFrozen = \"\",\r\n        query.isValid = \"\",\r\n        query.timeDays = \"\" //还有多少天到期\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 改变颜色\r\nconst getTagClass = (plate) => {\r\n    if (plate.length === 7) {\r\n        return 'containerTag';\r\n    } else if (plate.length === 8) {\r\n        return 'containerEnergy';\r\n    } else {\r\n        return '';\r\n    }\r\n}\r\nconst getTagClassTime = (time) => {\r\n    // 将每个字符按照\"至\"进行拆分成数组，判断数组中的第二个元素是否比当前日期要大\r\n    const timeArr = time.split(\"至\");\r\n    const endDate = new Date(timeArr[1]);\r\n    const today = new Date();\r\n    if (endDate > today) {\r\n        return 'containerTagTime';\r\n    } else {\r\n        return 'containerTagTimeErr';\r\n    }\r\n}\r\nconst combinedArray = ref([]);\r\nconst setForm = () => {\r\n    blackData.data.blackTimePeriodList = \"\";\r\n    blackData.data.remark1 = \"\";\r\n    blackData.data.remark2 = \"\";\r\n    blackData.data.blackReason = \"\";\r\n    isRemark.value = false;\r\n    inputs.value = [];\r\n    carNoList.value = [];\r\n    combinedArray.value = [];\r\n    timePeriodListResults.value = [];\r\n}\r\nconst update = () => {\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (inputs.value.length > 0) {\r\n            // 将inputs数组中的值赋值到carNoList数组中\r\n            combinedArray.value = [...carNoList.value, ...inputs.value]\r\n        } else {\r\n            combinedArray.value = carNoList.value;\r\n        }\r\n        const startDate = timePeriodListResults.value[0]\r\n        const endDate = timePeriodListResults.value[1]\r\n        //格式化开始时间\r\n        const newStartDate = new Date(startDate);\r\n        const formattedStartDate = newStartDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\\//g, '-');\r\n        //格式化结束时间\r\n        const newEndDate = new Date(endDate);\r\n        const formattedEndDate = newEndDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\\//g, '-');\r\n        //将 formattedEndDate和formattedStartDate用\"-\"连接起来\r\n        const timePeriod = formattedStartDate + \"-\" + formattedEndDate;\r\n        console.log(timePeriod)\r\n        blackData.data.carNo = combinedArray.value.join(\",\");\r\n        console.log(blackData.data.carNo)\r\n        const specialCarTypeId = blackNameList.value.find(item => item.name === blackData.data.specialCarTypeName).id;\r\n        const loadingTime = 0;\r\n        if (valid) {\r\n            // 关闭当前页面的标签页;\r\n            dialogVisibleBlack.value = false\r\n            // 添加加载弹窗\r\n            setTimeout(() => {\r\n                const loading = ElLoading.service({\r\n                    lock: true,\r\n                    // 字体改成黑色\r\n                    text: '正在删除数据，请稍后.....',\r\n                    // 改成白色的背景\r\n                    background: 'rgba(255, 255, 255, 0.7)',\r\n                })\r\n                request\r\n                    .get(\"/parking/yardInfo/yardCode\",\r\n                        {\r\n                            params: {\r\n                                yardName: blackData.data.parkName\r\n                            },\r\n                        })\r\n                    .then((res) => {\r\n                        request({\r\n                            url: \"/parking/blackList/addBlackCar\",\r\n                            method: \"POST\",\r\n                            data: {\r\n                                parkCode: res.data[0],\r\n                                parkName: blackData.data.parkName,\r\n                                carCode: blackData.data.carNo,\r\n                                carOwner: blackData.data.userName,\r\n                                isPermament: blackData.data.isPermament,\r\n                                reason: blackData.data.blackReason,\r\n                                timePeriod: timePeriod,\r\n                                specialCarTypeId: specialCarTypeId,\r\n                                specialCarTypeName: blackData.data.specialCarTypeName,\r\n                                remark1: blackData.data.remark1,\r\n                                remark2: blackData.data.remark2\r\n                            },\r\n                        }).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                importData.value = res.data.data;\r\n                                // 将importData.value中的数据遍历一下，将每个数据按照-进行拆分输出为数组，判断数组的第三个元素是否是\"业务成功\"，若是的话，将这整条数据存储到importSuccess中，否则存储到importDefeat中\r\n                                importData.value.forEach(item => {\r\n                                    const arr = item.split(\"--\");\r\n                                    if (arr[arr.length - 1] == \"业务成功\") {\r\n                                        // 弹窗提示用户进行填写原因\r\n                                        importSuccess.value.push(item);\r\n                                    } else {\r\n                                        importDefeat.value.push(item);\r\n                                    }\r\n                                })\r\n                                // 将导入成功和失败的数据具体通知，哪些车牌导入成功，哪些车牌导入失败，以及失败的原因是什么，采用drawer \r\n                                // 通知成功导入的数据\r\n                                if (importSuccess.value.length != 0) {\r\n                                    ElNotification({\r\n                                        title: '导入成功',\r\n                                        message: '导入成功' + importSuccess.value.length + '条数据',\r\n                                        type: 'success',\r\n                                        offset: 100\r\n                                    })\r\n                                    loading.close()\r\n                                }\r\n                                if (importDefeat.value.length != 0) {\r\n                                    ElNotification({\r\n                                        title: '导入失败',\r\n                                        message: '导入失败' + importDefeat.value.length + '条数据',\r\n                                        type: 'error',\r\n                                        offset: 100,\r\n                                        position: \"top-left\"\r\n                                    })\r\n                                    loading.close()\r\n                                    importNotificationDefeat.value = true\r\n                                }\r\n                                // 遍历导入失败的数据展示出来\r\n                                importDefeat.value.forEach(item => {\r\n                                    // 将item按照-进行拆分输出为数组，判断数组的第三个元素是否是\"业务成功\"，若是的话，将这整条数据存储到importSuccess中，否则存储到importDefeat中\r\n                                    const arr = item.split(\"--\");\r\n                                    const defeatReason = arr[arr.length - 1];\r\n                                    console.log(arr[0])\r\n                                    console.log(defeatReason)\r\n                                    importDefeatData.value.push({\r\n                                        carCode: arr[0],\r\n                                        defeatReason: defeatReason\r\n                                    })\r\n                                })\r\n                                getData();\r\n                                setForm()\r\n                            } else {\r\n                                dialogVisibleBlack.value = false\r\n                                ElMessage.error(res.data.msg);\r\n                            }\r\n                        })\r\n                    })\r\n            }, loadingTime)\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 滚动行为\r\nconst scrollBehavior = (e) => {\r\n    console.log(e)\r\n    // 判断滚动的次数，达到三次就触发加载更多数据\r\n    scrollCount.value += 1;\r\n    if (scrollCount.value >= 3) {\r\n        // 判断是向上滚动还是向下滚动\r\n        if (e.deltaY < 0) {\r\n            // 向上滚动\r\n            console.log(\"向上滚动\")\r\n            scrollCount.value = 0;\r\n            isFormVisible.value = false;\r\n            heightData.value = 400; // 减少一定高度\r\n        } else {\r\n            // 向下滚动\r\n            console.log(\"向下滚动\")\r\n            scrollCount.value = 0;\r\n            isFormVisible.value = true;\r\n            heightData.value = 300; // 减少一定高度\r\n        }\r\n    }\r\n}\r\nonMounted(() => {\r\n    // 挂载\r\n    TableRef.value && TableRef.value.$refs.bodyWrapper.addEventListener('mousewheel', scrollBehavior)\r\n})\r\n\r\nonUnmounted(() => {\r\n    // 卸载\r\n    TableRef.value && TableRef.value.$refs.bodyWrapper.removeEventListener('mousewheel', scrollBehavior)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.odd-row {\r\n    background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n\r\n.ticketName {\r\n    width: 100px;\r\n}\r\n\r\n.cell-item {\r\n    // 设置宽度大一点\r\n    width: 80%;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.item_car_no {\r\n    // 设置宽度大一点\r\n    width: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.containerTag {\r\n    margin-left: -20px;\r\n    margin-right: 30px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n    // 字体往左边点\r\n    width: 82px;\r\n    height: 30px;\r\n    // 将字往左边移动点\r\n    transform: translateX(28px);\r\n    line-height: 30px;\r\n    text-align: center;\r\n    color: #fff;\r\n    // color: black;\r\n    // 去除边框\r\n    border: none;\r\n    border-radius: 5px;\r\n    background-color: #1e68bb;\r\n    // background-image: linear-gradient(to bottom, #c0edc8, #4fcc67); /* 从上到下的绿色渐变 */;\r\n}\r\n\r\n.containerTagTime {\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n    // 字体往左边点\r\n    width: 290px;\r\n    height: 30px;\r\n    // 垂直排列\r\n    display: flex;\r\n    // 将字往左边移动点\r\n    transform: translateX(8px);\r\n    line-height: 30px;\r\n    text-align: center;\r\n    color: #fff;\r\n    // color: black;\r\n    // 去除边框\r\n    border: none;\r\n    border-radius: 5px;\r\n    background-color: rgb(22, 208, 22);\r\n    // background-image: linear-gradient(to bottom, #c0edc8, #4fcc67); /* 从上到下的绿色渐变 */;\r\n}\r\n\r\n.sycButton {\r\n    // 将按钮往右边移动一点\r\n    margin-right: 45px;\r\n    float: right;\r\n    background: #626aef;\r\n    color: #fff;\r\n    // 去除边框颜色\r\n    border: none;\r\n    // 按钮大一点\r\n    font-size: 12px;\r\n    margin-top: -59px;\r\n    // 点击以后颜色加深\r\n}\r\n\r\n.containerTagTimeErr {\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n    // 字体往左边点\r\n    width: 290px;\r\n    height: 30px;\r\n    // 垂直排列\r\n    display: flex;\r\n    // 将字往左边移动点\r\n    transform: translateX(8px);\r\n    line-height: 30px;\r\n    text-align: center;\r\n    color: #fff;\r\n    // color: black;\r\n    // 去除边框\r\n    border: none;\r\n    border-radius: 5px;\r\n    background-color: rgb(255, 0, 0);\r\n    // background-image: linear-gradient(to bottom, #c0edc8, #4fcc67); /* 从上到下的绿色渐变 */;\r\n}\r\n\r\n.ownerName {\r\n    width: 120px;\r\n}\r\n\r\n.containerEnergy {\r\n    margin-left: -20px;\r\n    margin-right: 30px;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n    // 字体往左边点\r\n    width: 90px;\r\n    height: 30px;\r\n    // 将字往左边移动点\r\n    transform: translateX(28px);\r\n    line-height: 30px;\r\n    text-align: center;\r\n    color: black;\r\n    // 去除边框\r\n    border: none;\r\n    border-radius: 5px;\r\n    background-image: linear-gradient(to bottom, #c0edc8, #4fcc67);\r\n    /* 从上到下的绿色渐变 */\r\n    ;\r\n}\r\n\r\n.form-container {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    /* 允许元素换行 */\r\n    gap: 1px;\r\n    /* 设置元素之间的间距 */\r\n}\r\n\r\n.input-container {\r\n    width: 25%;\r\n    display: flex;\r\n    align-items: center;\r\n    // margin-bottom: 5px;\r\n}\r\n\r\n.input-container>* {\r\n    // margin-right: 2px;\r\n}\r\n\r\n.searchButton {\r\n    // 将按钮往右边移动一点\r\n    margin-left: 1020px;\r\n    float: left;\r\n    // 按钮大一点\r\n    font-size: 14px;\r\n    margin-top: -60px;\r\n}\r\n\r\n.onsetButton {\r\n    // 按钮右侧对齐\r\n    float: right;\r\n    // 按钮大一点\r\n    font-size: 14px;\r\n    margin-top: -60px;\r\n    margin-right: 105px;\r\n}\r\n\r\n.button-remark {\r\n    // 去除按钮的边框\r\n    border: none;\r\n    // background-color: #1e68bb;\r\n    // color: white;\r\n    // 左对齐\r\n    text-align: left;\r\n    // margin-left: px;\r\n\r\n    // 去除悬浮的颜色\r\n    /* 去除悬浮状态下的背景色 */\r\n    &:hover {\r\n        // 没有颜色\r\n        background-color: initial;\r\n        /* 或者使用你希望的颜色 */\r\n    }\r\n}\r\n\r\n.container {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    /* 允许换行 */\r\n    // 不显示边框\r\n    border: none;\r\n    // 宽度减少点\r\n    width: 1236px;\r\n    height: 515px;\r\n    // 不显示滚动条\r\n    overflow: hidden;\r\n}\r\n\r\n.parkName {\r\n    width: 120px;\r\n}\r\n\r\n.tagCss {\r\n    flex: 0 0 25%;\r\n    /* 每个标签占据容器宽度的三分之一 */\r\n    // 不显示边框\r\n    margin-bottom: 10px;\r\n    // 标签的宽度自适应内容\r\n    width: 80px;\r\n    margin-right: 10px;\r\n    text-align: center;\r\n}\r\n\r\n.pagination {\r\n    // 往右移动一点\r\n    margin-left: 550px;\r\n    margin-top: 8px;\r\n}\r\n\r\n.icon-ole {\r\n    margin-top: -200px;\r\n    margin-left: 1150px;\r\n    color: #409eff;\r\n    font-size: 15px;\r\n}\r\n\r\n.no-arrow-collapse::part(header) {\r\n    --el-arrow-size: 0;\r\n}\r\n\r\n/* 在你的全局样式文件（如 styles.css）中 */\r\n.el-collapse {\r\n    border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__arrow {\r\n    // 隐藏掉这个\r\n    font-size: 0 !important;\r\n    color: transparent !important;\r\n}\r\n\r\n.el-collapse-item__header {\r\n    // 去除白色背景\r\n    background: transparent;\r\n    border-bottom: none;\r\n}\r\n\r\n.toggleClass {\r\n    // 按钮大一点\r\n    font-size: 16px;\r\n    margin-top: -62px;\r\n    margin-right: -12px;\r\n    float: right;\r\n}\r\n</style>", "import script from \"./MonthTicket.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./MonthTicket.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./MonthTicket.vue?vue&type=style&index=0&id=13825d14&lang=scss\"\n\nconst __exports__ = script;\n\nexport default __exports__", "module.exports = __webpack_public_path__ + \"img/TimePeriodList.ec116cd5.svg\";", "module.exports = __webpack_public_path__ + \"img/CarNo.8e27e591.svg\";", "module.exports = __webpack_public_path__ + \"img/ReleaseReason.31dc9c95.svg\";", "module.exports = __webpack_public_path__ + \"img/Remark1.9133d6c4.svg\";"], "sourceRoot": ""}