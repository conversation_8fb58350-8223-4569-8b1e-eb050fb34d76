{"version": 3, "sources": ["webpack:///./node_modules/qrcode.vue/dist/qrcode.vue.browser.js"], "names": ["global", "factory", "module", "exports", "this", "vue", "qrcodegen", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "SuppressedError", "QrCode", "version", "errorCorrectionLevel", "dataCodewords", "msk", "modules", "isFunction", "MIN_VERSION", "MAX_VERSION", "RangeError", "size", "row", "push", "slice", "drawFunctionPatterns", "allCodewords", "addEccAndInterleave", "drawCodewords", "min<PERSON><PERSON><PERSON><PERSON>", "applyMask", "drawFormatBits", "penalty", "getPenaltyScore", "assert", "mask", "encodeText", "text", "ecl", "segs", "QrSegment", "makeSegments", "encodeSegments", "encodeBinary", "data", "seg", "makeBytes", "minVersion", "maxVersion", "boostEcl", "dataUsedBits", "dataCapacityBits_1", "getNumDataCodewords", "usedBits", "getTotalBits", "_i", "_a", "Ecc", "MEDIUM", "QUARTILE", "HIGH", "newEcl", "bb", "_b", "segs_1", "appendBits", "mode", "modeBits", "numChars", "numCharCountBits", "_c", "_d", "getData", "b", "dataCapacityBits", "Math", "min", "padByte", "for<PERSON>ach", "getModule", "x", "y", "getModules", "setFunctionModule", "drawFinderPattern", "alignPatPos", "getAlignmentPatternPositions", "numAlign", "j", "drawAlignmentPattern", "drawVersion", "formatBits", "rem", "bits", "getBit", "color", "a", "floor", "dy", "dx", "dist", "max", "abs", "xx", "yy", "isDark", "ver", "numBlocks", "NUM_ERROR_CORRECTION_BLOCKS", "ordinal", "blockEccLen", "ECC_CODEWORDS_PER_BLOCK", "rawCodewords", "getNumRawDataModules", "numShortBlocks", "shortBlockLen", "blocks", "rsDiv", "reedSolomonComputeDivisor", "k", "dat", "ecc", "reedSolomonComputeRemainder", "concat", "result", "_loop_1", "block", "right", "vert", "upward", "invert", "Error", "runColor", "runX", "runHistory", "PENALTY_N1", "finderPenaltyAddHistory", "finderPenaltyCountPatterns", "PENALTY_N3", "finderPenaltyTerminateAndCount", "runY", "PENALTY_N2", "dark", "reduce", "sum", "total", "ceil", "PENALTY_N4", "step", "pos", "splice", "degree", "root", "reedSolomonMultiply", "divisor", "map", "_", "_loop_2", "factor", "shift", "coef", "data_1", "z", "core", "currentRunColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "val", "len", "cond", "bitData", "data_2", "Mode", "BYTE", "makeNumeric", "digits", "isNumeric", "parseInt", "substring", "NUMERIC", "makeAlphanumeric", "isAlphanumeric", "temp", "ALPHANUMERIC_CHARSET", "indexOf", "char<PERSON>t", "ALPHANUMERIC", "toUtf8ByteArray", "makeEci", "assignVal", "ECI", "NUMERIC_REGEX", "test", "ALPHANUMERIC_REGEX", "segs_2", "ccbits", "Infinity", "str", "encodeURI", "charCodeAt", "LOW", "numBitsCharCount", "KANJI", "QR", "defaultErrorCorrectLevel", "ErrorCorrectLevelMap", "L", "M", "Q", "H", "SUPPORTS_PATH2D", "Path2D", "addPath", "e", "validErrorCorrectLevel", "level", "generatePath", "margin", "ops", "start", "cell", "join", "QRCodeProps", "value", "type", "String", "required", "default", "Number", "validator", "l", "background", "foreground", "QRCodeVueProps", "renderAs", "as", "QRCodeSvg", "defineComponent", "name", "props", "setup", "num<PERSON>ells", "ref", "fgPath", "generate", "cells", "onUpdated", "h", "width", "height", "xmlns", "viewBox", "fill", "d", "QRCodeCanvas", "canvasEl", "canvas", "ctx", "getContext", "devicePixelRatio", "window", "scale", "fillStyle", "fillRect", "rdx", "cdx", "onMounted", "style", "QrcodeVue", "render", "$props", "_size", "_margin", "_level"], "mappings": ";;;;;;;CAMA,SAAWA,EAAQC,GACgDC,EAAOC,QAAUF,EAAQ,EAAQ,UADpG,CAIGG,GAAM,SAAWC,GAAO,aAmBvB,IAsCIC,EAtCAC,EAAW,WAQX,OAPAA,EAAWC,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE5C,IAAK,IAAII,KADTL,EAAIG,UAAUF,GACAD,EAAOH,OAAOS,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,IAE9E,OAAON,GAEJH,EAASa,MAAMhB,KAAMU,YAGL,oBAApBO,iBAAiCA,gBA4BxC,SAAWf,GAkBP,IAAIgB,EAAwB,WAMxB,SAASA,EAGTC,EAEAC,EAAsBC,EAAeC,GASjC,GARAtB,KAAKmB,QAAUA,EACfnB,KAAKoB,qBAAuBA,EAG5BpB,KAAKuB,QAAU,GAEfvB,KAAKwB,WAAa,GAEdL,EAAUD,EAAOO,aAAeN,EAAUD,EAAOQ,YACjD,MAAM,IAAIC,WAAW,8BACzB,GAAIL,GAAO,GAAKA,EAAM,EAClB,MAAM,IAAIK,WAAW,2BACzB3B,KAAK4B,KAAiB,EAAVT,EAAc,GAG1B,IADA,IAAIU,EAAM,GACDrB,EAAI,EAAGA,EAAIR,KAAK4B,KAAMpB,IAC3BqB,EAAIC,MAAK,GACb,IAAStB,EAAI,EAAGA,EAAIR,KAAK4B,KAAMpB,IAC3BR,KAAKuB,QAAQO,KAAKD,EAAIE,SACtB/B,KAAKwB,WAAWM,KAAKD,EAAIE,SAG7B/B,KAAKgC,uBACL,IAAIC,EAAejC,KAAKkC,oBAAoBb,GAG5C,GAFArB,KAAKmC,cAAcF,IAEP,GAARX,EACA,KAAIc,EAAa,IACjB,IAAS5B,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxBR,KAAKqC,UAAU7B,GACfR,KAAKsC,eAAe9B,GACpB,IAAI+B,EAAUvC,KAAKwC,kBACfD,EAAUH,IACVd,EAAMd,EACN4B,EAAaG,GAEjBvC,KAAKqC,UAAU7B,IAGvBiC,EAAO,GAAKnB,GAAOA,GAAO,GAC1BtB,KAAK0C,KAAOpB,EACZtB,KAAKqC,UAAUf,GACftB,KAAKsC,eAAehB,GACpBtB,KAAKwB,WAAa,GAghBtB,OAxgBAN,EAAOyB,WAAa,SAAUC,EAAMC,GAChC,IAAIC,EAAO5C,EAAU6C,UAAUC,aAAaJ,GAC5C,OAAO1B,EAAO+B,eAAeH,EAAMD,IAMvC3B,EAAOgC,aAAe,SAAUC,EAAMN,GAClC,IAAIO,EAAMlD,EAAU6C,UAAUM,UAAUF,GACxC,OAAOjC,EAAO+B,eAAe,CAACG,GAAMP,IAYxC3B,EAAO+B,eAAiB,SAAUH,EAAMD,EAAKS,EAAYC,EAAYb,EAAMc,GAKvE,QAJmB,IAAfF,IAAyBA,EAAa,QACvB,IAAfC,IAAyBA,EAAa,SAC7B,IAATb,IAAmBA,GAAQ,QACd,IAAbc,IAAuBA,GAAW,KAChCtC,EAAOO,aAAe6B,GAAcA,GAAcC,GAAcA,GAAcrC,EAAOQ,cACpFgB,GAAQ,GAAKA,EAAO,EACvB,MAAM,IAAIf,WAAW,iBAEzB,IAAIR,EACAsC,EACJ,IAAKtC,EAAUmC,GAAanC,IAAW,CACnC,IAAIuC,EAAgE,EAA3CxC,EAAOyC,oBAAoBxC,EAAS0B,GACzDe,EAAWb,EAAUc,aAAaf,EAAM3B,GAC5C,GAAIyC,GAAYF,EAAoB,CAChCD,EAAeG,EACf,MAEJ,GAAIzC,GAAWoC,EACX,MAAM,IAAI5B,WAAW,iBAG7B,IAAK,IAAImC,EAAK,EAAGC,EAAK,CAAC7C,EAAO8C,IAAIC,OAAQ/C,EAAO8C,IAAIE,SAAUhD,EAAO8C,IAAIG,MAAOL,EAAKC,EAAGpD,OAAQmD,IAAM,CACnG,IAAIM,EAASL,EAAGD,GACZN,GAAYC,GAA8D,EAA9CvC,EAAOyC,oBAAoBxC,EAASiD,KAChEvB,EAAMuB,GAId,IADA,IAAIC,EAAK,GACAC,EAAK,EAAGC,EAASzB,EAAMwB,EAAKC,EAAO5D,OAAQ2D,IAAM,CACtD,IAAIlB,EAAMmB,EAAOD,GACjBE,EAAWpB,EAAIqB,KAAKC,SAAU,EAAGL,GACjCG,EAAWpB,EAAIuB,SAAUvB,EAAIqB,KAAKG,iBAAiBzD,GAAUkD,GAC7D,IAAK,IAAIQ,EAAK,EAAGC,EAAK1B,EAAI2B,UAAWF,EAAKC,EAAGnE,OAAQkE,IAAM,CACvD,IAAIG,EAAIF,EAAGD,GACXR,EAAGvC,KAAKkD,IAGhBvC,EAAO4B,EAAG1D,QAAU8C,GAEpB,IAAIwB,EAA8D,EAA3C/D,EAAOyC,oBAAoBxC,EAAS0B,GAC3DJ,EAAO4B,EAAG1D,QAAUsE,GACpBT,EAAW,EAAGU,KAAKC,IAAI,EAAGF,EAAmBZ,EAAG1D,QAAS0D,GACzDG,EAAW,GAAI,EAAIH,EAAG1D,OAAS,GAAK,EAAG0D,GACvC5B,EAAO4B,EAAG1D,OAAS,GAAK,GAExB,IAAK,IAAIyE,EAAU,IAAMf,EAAG1D,OAASsE,EAAkBG,GAAW,IAC9DZ,EAAWY,EAAS,EAAGf,GAE3B,IAAIhD,EAAgB,GACpB,MAA8B,EAAvBA,EAAcV,OAAa0D,EAAG1D,OACjCU,EAAcS,KAAK,GAKvB,OAJAuC,EAAGgB,SAAQ,SAAUL,EAAGxE,GACpB,OAAOa,EAAcb,IAAM,IAAMwE,GAAM,GAAS,EAAJxE,MAGzC,IAAIU,EAAOC,EAAS0B,EAAKxB,EAAeqB,IAMnDxB,EAAOL,UAAUyE,UAAY,SAAUC,EAAGC,GACtC,OAAO,GAAKD,GAAKA,EAAIvF,KAAK4B,MAAQ,GAAK4D,GAAKA,EAAIxF,KAAK4B,MAAQ5B,KAAKuB,QAAQiE,GAAGD,IAEjFrE,EAAOL,UAAU4E,WAAa,WAC1B,OAAOzF,KAAKuB,SAIhBL,EAAOL,UAAUmB,qBAAuB,WAEpC,IAAK,IAAIxB,EAAI,EAAGA,EAAIR,KAAK4B,KAAMpB,IAC3BR,KAAK0F,kBAAkB,EAAGlF,EAAGA,EAAI,GAAK,GACtCR,KAAK0F,kBAAkBlF,EAAG,EAAGA,EAAI,GAAK,GAG1CR,KAAK2F,kBAAkB,EAAG,GAC1B3F,KAAK2F,kBAAkB3F,KAAK4B,KAAO,EAAG,GACtC5B,KAAK2F,kBAAkB,EAAG3F,KAAK4B,KAAO,GAEtC,IAAIgE,EAAc5F,KAAK6F,+BACnBC,EAAWF,EAAYjF,OAC3B,IAASH,EAAI,EAAGA,EAAIsF,EAAUtF,IAC1B,IAAK,IAAIuF,EAAI,EAAGA,EAAID,EAAUC,IAEf,GAALvF,GAAe,GAALuF,GAAe,GAALvF,GAAUuF,GAAKD,EAAW,GAAKtF,GAAKsF,EAAW,GAAU,GAALC,GAC1E/F,KAAKgG,qBAAqBJ,EAAYpF,GAAIoF,EAAYG,IAIlE/F,KAAKsC,eAAe,GACpBtC,KAAKiG,eAIT/E,EAAOL,UAAUyB,eAAiB,SAAUI,GAIxC,IAFA,IAAIS,EAAOnD,KAAKoB,qBAAqB8E,YAAc,EAAIxD,EACnDyD,EAAMhD,EACD3C,EAAI,EAAGA,EAAI,GAAIA,IACpB2F,EAAOA,GAAO,EAAoB,MAAbA,IAAQ,GACjC,IAAIC,EAA4B,OAApBjD,GAAQ,GAAKgD,GACzB1D,EAAO2D,IAAS,IAAM,GAEtB,IAAS5F,EAAI,EAAGA,GAAK,EAAGA,IACpBR,KAAK0F,kBAAkB,EAAGlF,EAAG6F,EAAOD,EAAM5F,IAC9CR,KAAK0F,kBAAkB,EAAG,EAAGW,EAAOD,EAAM,IAC1CpG,KAAK0F,kBAAkB,EAAG,EAAGW,EAAOD,EAAM,IAC1CpG,KAAK0F,kBAAkB,EAAG,EAAGW,EAAOD,EAAM,IAC1C,IAAS5F,EAAI,EAAGA,EAAI,GAAIA,IACpBR,KAAK0F,kBAAkB,GAAKlF,EAAG,EAAG6F,EAAOD,EAAM5F,IAEnD,IAASA,EAAI,EAAGA,EAAI,EAAGA,IACnBR,KAAK0F,kBAAkB1F,KAAK4B,KAAO,EAAIpB,EAAG,EAAG6F,EAAOD,EAAM5F,IAC9D,IAASA,EAAI,EAAGA,EAAI,GAAIA,IACpBR,KAAK0F,kBAAkB,EAAG1F,KAAK4B,KAAO,GAAKpB,EAAG6F,EAAOD,EAAM5F,IAC/DR,KAAK0F,kBAAkB,EAAG1F,KAAK4B,KAAO,GAAG,IAI7CV,EAAOL,UAAUoF,YAAc,WAC3B,KAAIjG,KAAKmB,QAAU,GAAnB,CAIA,IADA,IAAIgF,EAAMnG,KAAKmB,QACNX,EAAI,EAAGA,EAAI,GAAIA,IACpB2F,EAAOA,GAAO,EAAqB,MAAdA,IAAQ,IACjC,IAAIC,EAAOpG,KAAKmB,SAAW,GAAKgF,EAChC1D,EAAO2D,IAAS,IAAM,GAEtB,IAAS5F,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,IAAI8F,EAAQD,EAAOD,EAAM5F,GACrB+F,EAAIvG,KAAK4B,KAAO,GAAKpB,EAAI,EACzBwE,EAAIE,KAAKsB,MAAMhG,EAAI,GACvBR,KAAK0F,kBAAkBa,EAAGvB,EAAGsB,GAC7BtG,KAAK0F,kBAAkBV,EAAGuB,EAAGD,MAKrCpF,EAAOL,UAAU8E,kBAAoB,SAAUJ,EAAGC,GAC9C,IAAK,IAAIiB,GAAM,EAAGA,GAAM,EAAGA,IACvB,IAAK,IAAIC,GAAM,EAAGA,GAAM,EAAGA,IAAM,CAC7B,IAAIC,EAAOzB,KAAK0B,IAAI1B,KAAK2B,IAAIH,GAAKxB,KAAK2B,IAAIJ,IACvCK,EAAKvB,EAAImB,EACTK,EAAKvB,EAAIiB,EACT,GAAKK,GAAMA,EAAK9G,KAAK4B,MAAQ,GAAKmF,GAAMA,EAAK/G,KAAK4B,MAClD5B,KAAK0F,kBAAkBoB,EAAIC,EAAY,GAARJ,GAAqB,GAARA,KAM5DzF,EAAOL,UAAUmF,qBAAuB,SAAUT,EAAGC,GACjD,IAAK,IAAIiB,GAAM,EAAGA,GAAM,EAAGA,IACvB,IAAK,IAAIC,GAAM,EAAGA,GAAM,EAAGA,IACvB1G,KAAK0F,kBAAkBH,EAAImB,EAAIlB,EAAIiB,EAA4C,GAAxCvB,KAAK0B,IAAI1B,KAAK2B,IAAIH,GAAKxB,KAAK2B,IAAIJ,MAKnFvF,EAAOL,UAAU6E,kBAAoB,SAAUH,EAAGC,EAAGwB,GACjDhH,KAAKuB,QAAQiE,GAAGD,GAAKyB,EACrBhH,KAAKwB,WAAWgE,GAAGD,IAAK,GAK5BrE,EAAOL,UAAUqB,oBAAsB,SAAUiB,GAC7C,IAAI8D,EAAMjH,KAAKmB,QACX0B,EAAM7C,KAAKoB,qBACf,GAAI+B,EAAKxC,QAAUO,EAAOyC,oBAAoBsD,EAAKpE,GAC/C,MAAM,IAAIlB,WAAW,oBAUzB,IARA,IAAIuF,EAAYhG,EAAOiG,4BAA4BtE,EAAIuE,SAASH,GAC5DI,EAAcnG,EAAOoG,wBAAwBzE,EAAIuE,SAASH,GAC1DM,EAAerC,KAAKsB,MAAMtF,EAAOsG,qBAAqBP,GAAO,GAC7DQ,EAAiBP,EAAYK,EAAeL,EAC5CQ,EAAgBxC,KAAKsB,MAAMe,EAAeL,GAE1CS,EAAS,GACTC,EAAQ1G,EAAO2G,0BAA0BR,GACpC7G,EAAI,EAAGsH,EAAI,EAAGtH,EAAI0G,EAAW1G,IAAK,CACvC,IAAIuH,EAAM5E,EAAKpB,MAAM+F,EAAGA,EAAIJ,EAAgBL,GAAe7G,EAAIiH,EAAiB,EAAI,IACpFK,GAAKC,EAAIpH,OACT,IAAIqH,EAAM9G,EAAO+G,4BAA4BF,EAAKH,GAC9CpH,EAAIiH,GACJM,EAAIjG,KAAK,GACb6F,EAAO7F,KAAKiG,EAAIG,OAAOF,IAG3B,IAAIG,EAAS,GACTC,EAAU,SAAU5H,GACpBmH,EAAOtC,SAAQ,SAAUgD,EAAOtC,IAExBvF,GAAKkH,EAAgBL,GAAetB,GAAK0B,IACzCU,EAAOrG,KAAKuG,EAAM7H,QAG9B,IAASA,EAAI,EAAGA,EAAImH,EAAO,GAAGhH,OAAQH,IAClC4H,EAAQ5H,GAGZ,OADAiC,EAAO0F,EAAOxH,QAAU4G,GACjBY,GAIXjH,EAAOL,UAAUsB,cAAgB,SAAUgB,GACvC,GAAIA,EAAKxC,QAAUuE,KAAKsB,MAAMtF,EAAOsG,qBAAqBxH,KAAKmB,SAAW,GACtE,MAAM,IAAIQ,WAAW,oBAGzB,IAFA,IAAInB,EAAI,EAEC8H,EAAQtI,KAAK4B,KAAO,EAAG0G,GAAS,EAAGA,GAAS,EAAG,CACvC,GAATA,IACAA,EAAQ,GACZ,IAAK,IAAIC,EAAO,EAAGA,EAAOvI,KAAK4B,KAAM2G,IACjC,IAAK,IAAIxC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,IAAIR,EAAI+C,EAAQvC,EACZyC,EAA8B,IAAnBF,EAAQ,EAAK,GACxB9C,EAAIgD,EAASxI,KAAK4B,KAAO,EAAI2G,EAAOA,GACnCvI,KAAKwB,WAAWgE,GAAGD,IAAM/E,EAAkB,EAAd2C,EAAKxC,SACnCX,KAAKuB,QAAQiE,GAAGD,GAAKc,EAAOlD,EAAK3C,IAAM,GAAI,GAAS,EAAJA,IAChDA,MAOhBiC,EAAOjC,GAAmB,EAAd2C,EAAKxC,SAOrBO,EAAOL,UAAUwB,UAAY,SAAUK,GACnC,GAAIA,EAAO,GAAKA,EAAO,EACnB,MAAM,IAAIf,WAAW,2BACzB,IAAK,IAAI6D,EAAI,EAAGA,EAAIxF,KAAK4B,KAAM4D,IAC3B,IAAK,IAAID,EAAI,EAAGA,EAAIvF,KAAK4B,KAAM2D,IAAK,CAChC,IAAIkD,OAAS,EACb,OAAQ/F,GACJ,KAAK,EACD+F,GAAUlD,EAAIC,GAAK,GAAK,EACxB,MACJ,KAAK,EACDiD,EAASjD,EAAI,GAAK,EAClB,MACJ,KAAK,EACDiD,EAASlD,EAAI,GAAK,EAClB,MACJ,KAAK,EACDkD,GAAUlD,EAAIC,GAAK,GAAK,EACxB,MACJ,KAAK,EACDiD,GAAUvD,KAAKsB,MAAMjB,EAAI,GAAKL,KAAKsB,MAAMhB,EAAI,IAAM,GAAK,EACxD,MACJ,KAAK,EACDiD,EAASlD,EAAIC,EAAI,EAAID,EAAIC,EAAI,GAAK,EAClC,MACJ,KAAK,EACDiD,GAAUlD,EAAIC,EAAI,EAAID,EAAIC,EAAI,GAAK,GAAK,EACxC,MACJ,KAAK,EACDiD,IAAWlD,EAAIC,GAAK,EAAID,EAAIC,EAAI,GAAK,GAAK,EAC1C,MACJ,QAAS,MAAM,IAAIkD,MAAM,gBAExB1I,KAAKwB,WAAWgE,GAAGD,IAAMkD,IAC1BzI,KAAKuB,QAAQiE,GAAGD,IAAMvF,KAAKuB,QAAQiE,GAAGD,MAMtDrE,EAAOL,UAAU2B,gBAAkB,WAG/B,IAFA,IAAI2F,EAAS,EAEJ3C,EAAI,EAAGA,EAAIxF,KAAK4B,KAAM4D,IAAK,CAIhC,IAHA,IAAImD,GAAW,EACXC,EAAO,EACPC,EAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC3BtD,EAAI,EAAGA,EAAIvF,KAAK4B,KAAM2D,IACvBvF,KAAKuB,QAAQiE,GAAGD,IAAMoD,GACtBC,IACY,GAARA,EACAT,GAAUjH,EAAO4H,WACZF,EAAO,GACZT,MAGJnI,KAAK+I,wBAAwBH,EAAMC,GAC9BF,IACDR,GAAUnI,KAAKgJ,2BAA2BH,GAAc3H,EAAO+H,YACnEN,EAAW3I,KAAKuB,QAAQiE,GAAGD,GAC3BqD,EAAO,GAGfT,GAAUnI,KAAKkJ,+BAA+BP,EAAUC,EAAMC,GAAc3H,EAAO+H,WAGvF,IAAS1D,EAAI,EAAGA,EAAIvF,KAAK4B,KAAM2D,IAAK,CAC5BoD,GAAW,EAAf,IACIQ,EAAO,EAEX,IADIN,EAAa,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC3BrD,EAAI,EAAGA,EAAIxF,KAAK4B,KAAM4D,IACvBxF,KAAKuB,QAAQiE,GAAGD,IAAMoD,GACtBQ,IACY,GAARA,EACAhB,GAAUjH,EAAO4H,WACZK,EAAO,GACZhB,MAGJnI,KAAK+I,wBAAwBI,EAAMN,GAC9BF,IACDR,GAAUnI,KAAKgJ,2BAA2BH,GAAc3H,EAAO+H,YACnEN,EAAW3I,KAAKuB,QAAQiE,GAAGD,GAC3B4D,EAAO,GAGfhB,GAAUnI,KAAKkJ,+BAA+BP,EAAUQ,EAAMN,GAAc3H,EAAO+H,WAGvF,IAASzD,EAAI,EAAGA,EAAIxF,KAAK4B,KAAO,EAAG4D,IAC/B,IAASD,EAAI,EAAGA,EAAIvF,KAAK4B,KAAO,EAAG2D,IAAK,CACpC,IAAIe,EAAQtG,KAAKuB,QAAQiE,GAAGD,GACxBe,GAAStG,KAAKuB,QAAQiE,GAAGD,EAAI,IAC7Be,GAAStG,KAAKuB,QAAQiE,EAAI,GAAGD,IAC7Be,GAAStG,KAAKuB,QAAQiE,EAAI,GAAGD,EAAI,KACjC4C,GAAUjH,EAAOkI,YAK7B,IADA,IAAIC,EAAO,EACFvF,EAAK,EAAGC,EAAK/D,KAAKuB,QAASuC,EAAKC,EAAGpD,OAAQmD,IAAM,CACtD,IAAIjC,EAAMkC,EAAGD,GACbuF,EAAOxH,EAAIyH,QAAO,SAAUC,EAAKjD,GAAS,OAAOiD,GAAOjD,EAAQ,EAAI,KAAO+C,GAE/E,IAAIG,EAAQxJ,KAAK4B,KAAO5B,KAAK4B,KAEzBkG,EAAI5C,KAAKuE,KAAKvE,KAAK2B,IAAW,GAAPwC,EAAoB,GAARG,GAAcA,GAAS,EAI9D,OAHA/G,EAAO,GAAKqF,GAAKA,GAAK,GACtBK,GAAUL,EAAI5G,EAAOwI,WACrBjH,EAAO,GAAK0F,GAAUA,GAAU,SACzBA,GAMXjH,EAAOL,UAAUgF,6BAA+B,WAC5C,GAAoB,GAAhB7F,KAAKmB,QACL,MAAO,GAMP,IAJA,IAAI2E,EAAWZ,KAAKsB,MAAMxG,KAAKmB,QAAU,GAAK,EAC1CwI,EAAwB,IAAhB3J,KAAKmB,QAAiB,GAC2B,EAAzD+D,KAAKuE,MAAqB,EAAfzJ,KAAKmB,QAAc,IAAiB,EAAX2E,EAAe,IACnDqC,EAAS,CAAC,GACLyB,EAAM5J,KAAK4B,KAAO,EAAGuG,EAAOxH,OAASmF,EAAU8D,GAAOD,EAC3DxB,EAAO0B,OAAO,EAAG,EAAGD,GACxB,OAAOzB,GAMfjH,EAAOsG,qBAAuB,SAAUP,GACpC,GAAIA,EAAM/F,EAAOO,aAAewF,EAAM/F,EAAOQ,YACzC,MAAM,IAAIC,WAAW,+BACzB,IAAIwG,GAAU,GAAKlB,EAAM,KAAOA,EAAM,GACtC,GAAIA,GAAO,EAAG,CACV,IAAInB,EAAWZ,KAAKsB,MAAMS,EAAM,GAAK,EACrCkB,IAAW,GAAKrC,EAAW,IAAMA,EAAW,GACxCmB,GAAO,IACPkB,GAAU,IAGlB,OADA1F,EAAO,KAAO0F,GAAUA,GAAU,OAC3BA,GAKXjH,EAAOyC,oBAAsB,SAAUsD,EAAKpE,GACxC,OAAOqC,KAAKsB,MAAMtF,EAAOsG,qBAAqBP,GAAO,GACjD/F,EAAOoG,wBAAwBzE,EAAIuE,SAASH,GACxC/F,EAAOiG,4BAA4BtE,EAAIuE,SAASH,IAI5D/F,EAAO2G,0BAA4B,SAAUiC,GACzC,GAAIA,EAAS,GAAKA,EAAS,IACvB,MAAM,IAAInI,WAAW,uBAIzB,IADA,IAAIwG,EAAS,GACJ3H,EAAI,EAAGA,EAAIsJ,EAAS,EAAGtJ,IAC5B2H,EAAOrG,KAAK,GAChBqG,EAAOrG,KAAK,GAIZ,IAAIiI,EAAO,EACX,IAASvJ,EAAI,EAAGA,EAAIsJ,EAAQtJ,IAAK,CAE7B,IAAK,IAAIuF,EAAI,EAAGA,EAAIoC,EAAOxH,OAAQoF,IAC/BoC,EAAOpC,GAAK7E,EAAO8I,oBAAoB7B,EAAOpC,GAAIgE,GAC9ChE,EAAI,EAAIoC,EAAOxH,SACfwH,EAAOpC,IAAMoC,EAAOpC,EAAI,IAEhCgE,EAAO7I,EAAO8I,oBAAoBD,EAAM,GAE5C,OAAO5B,GAGXjH,EAAO+G,4BAA8B,SAAU9E,EAAM8G,GASjD,IARA,IAAI9B,EAAS8B,EAAQC,KAAI,SAAUC,GAAK,OAAO,KAC3CC,EAAU,SAAUpF,GACpB,IAAIqF,EAASrF,EAAImD,EAAOmC,QACxBnC,EAAOrG,KAAK,GACZmI,EAAQ5E,SAAQ,SAAUkF,EAAM/J,GAC5B,OAAO2H,EAAO3H,IAAMU,EAAO8I,oBAAoBO,EAAMF,OAGpDvG,EAAK,EAAG0G,EAASrH,EAAMW,EAAK0G,EAAO7J,OAAQmD,IAAM,CACtD,IAAIkB,EAAIwF,EAAO1G,GACfsG,EAAQpF,GAEZ,OAAOmD,GAIXjH,EAAO8I,oBAAsB,SAAUzE,EAAGC,GACtC,GAAID,IAAM,GAAK,GAAKC,IAAM,GAAK,EAC3B,MAAM,IAAI7D,WAAW,qBAGzB,IADA,IAAI8I,EAAI,EACCjK,EAAI,EAAGA,GAAK,EAAGA,IACpBiK,EAAKA,GAAK,EAAkB,KAAXA,IAAM,GACvBA,IAAOjF,IAAMhF,EAAK,GAAK+E,EAG3B,OADA9C,EAAOgI,IAAM,GAAK,GACXA,GAIXvJ,EAAOL,UAAUmI,2BAA6B,SAAUH,GACpD,IAAIpI,EAAIoI,EAAW,GACnBpG,EAAOhC,GAAiB,EAAZT,KAAK4B,MACjB,IAAI8I,EAAOjK,EAAI,GAAKoI,EAAW,IAAMpI,GAAKoI,EAAW,IAAU,EAAJpI,GAASoI,EAAW,IAAMpI,GAAKoI,EAAW,IAAMpI,EAC3G,OAAQiK,GAAQ7B,EAAW,IAAU,EAAJpI,GAASoI,EAAW,IAAMpI,EAAI,EAAI,IAC5DiK,GAAQ7B,EAAW,IAAU,EAAJpI,GAASoI,EAAW,IAAMpI,EAAI,EAAI,IAGtES,EAAOL,UAAUqI,+BAAiC,SAAUyB,EAAiBC,EAAkB/B,GAO3F,OANI8B,IACA3K,KAAK+I,wBAAwB6B,EAAkB/B,GAC/C+B,EAAmB,GAEvBA,GAAoB5K,KAAK4B,KACzB5B,KAAK+I,wBAAwB6B,EAAkB/B,GACxC7I,KAAKgJ,2BAA2BH,IAG3C3H,EAAOL,UAAUkI,wBAA0B,SAAU6B,EAAkB/B,GAC9C,GAAjBA,EAAW,KACX+B,GAAoB5K,KAAK4B,MAC7BiH,EAAWgC,MACXhC,EAAWiC,QAAQF,IAIvB1J,EAAOO,YAAc,EAErBP,EAAOQ,YAAc,GAErBR,EAAO4H,WAAa,EACpB5H,EAAOkI,WAAa,EACpBlI,EAAO+H,WAAa,GACpB/H,EAAOwI,WAAa,GACpBxI,EAAOoG,wBAA0B,CAG7B,EAAE,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAChK,EAAE,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjK,EAAE,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjK,EAAE,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAErKpG,EAAOiG,4BAA8B,CAGjC,EAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC1I,EAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACnJ,EAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACtJ,EAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAEpJjG,EAvkBgB,GA4kB3B,SAASsD,EAAWuG,EAAKC,EAAK3G,GAC1B,GAAI2G,EAAM,GAAKA,EAAM,IAAMD,IAAQC,GAAO,EACtC,MAAM,IAAIrJ,WAAW,sBACzB,IAAK,IAAInB,EAAIwK,EAAM,EAAGxK,GAAK,EAAGA,IAC1B6D,EAAGvC,KAAMiJ,IAAQvK,EAAK,GAG9B,SAAS6F,EAAOd,EAAG/E,GACf,OAA0B,IAAjB+E,IAAM/E,EAAK,GAGxB,SAASiC,EAAOwI,GACZ,IAAKA,EACD,MAAM,IAAIvC,MAAM,mBAhBxBxI,EAAUgB,OAASA,EA8BnB,IAAI6B,EAA2B,WAK3B,SAASA,EAET0B,EAIAE,EAEAuG,GAII,GAHAlL,KAAKyE,KAAOA,EACZzE,KAAK2E,SAAWA,EAChB3E,KAAKkL,QAAUA,EACXvG,EAAW,EACX,MAAM,IAAIhD,WAAW,oBACzB3B,KAAKkL,QAAUA,EAAQnJ,QA+H3B,OAzHAgB,EAAUM,UAAY,SAAUF,GAE5B,IADA,IAAIkB,EAAK,GACAP,EAAK,EAAGqH,EAAShI,EAAMW,EAAKqH,EAAOxK,OAAQmD,IAAM,CACtD,IAAIkB,EAAImG,EAAOrH,GACfU,EAAWQ,EAAG,EAAGX,GAErB,OAAO,IAAItB,EAAUA,EAAUqI,KAAKC,KAAMlI,EAAKxC,OAAQ0D,IAG3DtB,EAAUuI,YAAc,SAAUC,GAC9B,IAAKxI,EAAUyI,UAAUD,GACrB,MAAM,IAAI5J,WAAW,0CAEzB,IADA,IAAI0C,EAAK,GACA7D,EAAI,EAAGA,EAAI+K,EAAO5K,QAAS,CAChC,IAAIF,EAAIyE,KAAKC,IAAIoG,EAAO5K,OAASH,EAAG,GACpCgE,EAAWiH,SAASF,EAAOG,UAAUlL,EAAGA,EAAIC,GAAI,IAAS,EAAJA,EAAQ,EAAG4D,GAChE7D,GAAKC,EAET,OAAO,IAAIsC,EAAUA,EAAUqI,KAAKO,QAASJ,EAAO5K,OAAQ0D,IAKhEtB,EAAU6I,iBAAmB,SAAUhJ,GACnC,IAAKG,EAAU8I,eAAejJ,GAC1B,MAAM,IAAIjB,WAAW,+DACzB,IACInB,EADA6D,EAAK,GAET,IAAK7D,EAAI,EAAGA,EAAI,GAAKoC,EAAKjC,OAAQH,GAAK,EAAG,CACtC,IAAIsL,EAAgE,GAAzD/I,EAAUgJ,qBAAqBC,QAAQpJ,EAAKqJ,OAAOzL,IAC9DsL,GAAQ/I,EAAUgJ,qBAAqBC,QAAQpJ,EAAKqJ,OAAOzL,EAAI,IAC/DgE,EAAWsH,EAAM,GAAIzH,GAIzB,OAFI7D,EAAIoC,EAAKjC,QACT6D,EAAWzB,EAAUgJ,qBAAqBC,QAAQpJ,EAAKqJ,OAAOzL,IAAK,EAAG6D,GACnE,IAAItB,EAAUA,EAAUqI,KAAKc,aAActJ,EAAKjC,OAAQ0D,IAInEtB,EAAUC,aAAe,SAAUJ,GAE/B,MAAY,IAARA,EACO,GACFG,EAAUyI,UAAU5I,GAClB,CAACG,EAAUuI,YAAY1I,IACzBG,EAAU8I,eAAejJ,GACvB,CAACG,EAAU6I,iBAAiBhJ,IAE5B,CAACG,EAAUM,UAAUN,EAAUoJ,gBAAgBvJ,MAI9DG,EAAUqJ,QAAU,SAAUC,GAC1B,IAAIhI,EAAK,GACT,GAAIgI,EAAY,EACZ,MAAM,IAAI1K,WAAW,qCACpB,GAAI0K,EAAY,IACjB7H,EAAW6H,EAAW,EAAGhI,QACxB,GAAIgI,EAAY,MACjB7H,EAAW,EAAG,EAAGH,GACjBG,EAAW6H,EAAW,GAAIhI,OAEzB,MAAIgI,EAAY,KAKjB,MAAM,IAAI1K,WAAW,qCAJrB6C,EAAW,EAAG,EAAGH,GACjBG,EAAW6H,EAAW,GAAIhI,GAI9B,OAAO,IAAItB,EAAUA,EAAUqI,KAAKkB,IAAK,EAAGjI,IAIhDtB,EAAUyI,UAAY,SAAU5I,GAC5B,OAAOG,EAAUwJ,cAAcC,KAAK5J,IAKxCG,EAAU8I,eAAiB,SAAUjJ,GACjC,OAAOG,EAAU0J,mBAAmBD,KAAK5J,IAI7CG,EAAUlC,UAAUkE,QAAU,WAC1B,OAAO/E,KAAKkL,QAAQnJ,SAIxBgB,EAAUc,aAAe,SAAUf,EAAM3B,GAErC,IADA,IAAIgH,EAAS,EACJrE,EAAK,EAAG4I,EAAS5J,EAAMgB,EAAK4I,EAAO/L,OAAQmD,IAAM,CACtD,IAAIV,EAAMsJ,EAAO5I,GACb6I,EAASvJ,EAAIqB,KAAKG,iBAAiBzD,GACvC,GAAIiC,EAAIuB,UAAa,GAAKgI,EACtB,OAAOC,IACXzE,GAAU,EAAIwE,EAASvJ,EAAI8H,QAAQvK,OAEvC,OAAOwH,GAGXpF,EAAUoJ,gBAAkB,SAAUU,GAClCA,EAAMC,UAAUD,GAEhB,IADA,IAAI1E,EAAS,GACJ3H,EAAI,EAAGA,EAAIqM,EAAIlM,OAAQH,IACP,KAAjBqM,EAAIZ,OAAOzL,GACX2H,EAAOrG,KAAK+K,EAAIE,WAAWvM,KAE3B2H,EAAOrG,KAAK2J,SAASoB,EAAInB,UAAUlL,EAAI,EAAGA,EAAI,GAAI,KAClDA,GAAK,GAGb,OAAO2H,GAIXpF,EAAUwJ,cAAgB,WAE1BxJ,EAAU0J,mBAAqB,wBAG/B1J,EAAUgJ,qBAAuB,gDAC1BhJ,EAlJmB,GAoJ9B7C,EAAU6C,UAAYA,EA7wB1B,CA8wBG7C,IAAcA,EAAY,KAE7B,SAAWA,IACP,SAAWgB,GAIP,IAAI8C,EAAqB,WAErB,SAASA,EAEToD,EAEAlB,GACIlG,KAAKoH,QAAUA,EACfpH,KAAKkG,WAAaA,EAOtB,OAJAlC,EAAIgJ,IAAM,IAAIhJ,EAAI,EAAG,GACrBA,EAAIC,OAAS,IAAID,EAAI,EAAG,GACxBA,EAAIE,SAAW,IAAIF,EAAI,EAAG,GAC1BA,EAAIG,KAAO,IAAIH,EAAI,EAAG,GACfA,EAfa,GAiBxB9C,EAAO8C,IAAMA,GArBjB,CAsBG9D,EAAUgB,SAAWhB,EAAUgB,OAAS,KAvB/C,CAwBGhB,IAAcA,EAAY,KAE7B,SAAWA,IACP,SAAW6C,GAIP,IAAIqI,EAAsB,WAEtB,SAASA,EAET1G,EAEAuI,GACIjN,KAAK0E,SAAWA,EAChB1E,KAAKiN,iBAAmBA,EAc5B,OATA7B,EAAKvK,UAAU+D,iBAAmB,SAAUqC,GACxC,OAAOjH,KAAKiN,iBAAiB/H,KAAKsB,OAAOS,EAAM,GAAK,MAGxDmE,EAAKO,QAAU,IAAIP,EAAK,EAAK,CAAC,GAAI,GAAI,KACtCA,EAAKc,aAAe,IAAId,EAAK,EAAK,CAAC,EAAG,GAAI,KAC1CA,EAAKC,KAAO,IAAID,EAAK,EAAK,CAAC,EAAG,GAAI,KAClCA,EAAK8B,MAAQ,IAAI9B,EAAK,EAAK,CAAC,EAAG,GAAI,KACnCA,EAAKkB,IAAM,IAAIlB,EAAK,EAAK,CAAC,EAAG,EAAG,IACzBA,EAtBc,GAwBzBrI,EAAUqI,KAAOA,GA5BrB,CA6BGlL,EAAU6C,YAAc7C,EAAU6C,UAAY,KA9BrD,CA+BG7C,IAAcA,EAAY,KAC7B,IAAIiN,EAAKjN,EAELkN,EAA2B,IAC3BC,EAAuB,CACvBC,EAAGH,EAAGjM,OAAO8C,IAAIgJ,IACjBO,EAAGJ,EAAGjM,OAAO8C,IAAIC,OACjBuJ,EAAGL,EAAGjM,OAAO8C,IAAIE,SACjBuJ,EAAGN,EAAGjM,OAAO8C,IAAIG,MAGjBuJ,EAAkB,WAClB,KACI,IAAIC,QAASC,QAAQ,IAAID,QAE7B,MAAOE,GACH,OAAO,EAEX,OAAO,EAPW,GAStB,SAASC,EAAuBC,GAC5B,OAAOA,KAASV,EAEpB,SAASW,EAAazM,EAAS0M,QACZ,IAAXA,IAAqBA,EAAS,GAClC,IAAIC,EAAM,GAiCV,OAhCA3M,EAAQ8D,SAAQ,SAAUxD,EAAK2D,GAC3B,IAAI2I,EAAQ,KACZtM,EAAIwD,SAAQ,SAAU+I,EAAM7I,GACxB,IAAK6I,GAAkB,OAAVD,EAKT,OAFAD,EAAIpM,KAAK,IAAIoG,OAAOiG,EAAQF,EAAQ,KAAK/F,OAAO1C,EAAIyI,EAAQ,KAAK/F,OAAO3C,EAAI4I,EAAO,OAAOjG,OAAOiG,EAAQF,EAAQ,WACjHE,EAAQ,MAIZ,GAAI5I,IAAM1D,EAAIlB,OAAS,EAgBnByN,GAAkB,OAAVD,IACRA,EAAQ5I,OAjBZ,CACI,IAAK6I,EAGD,OAEU,OAAVD,EAEAD,EAAIpM,KAAK,IAAIoG,OAAO3C,EAAI0I,EAAQ,KAAK/F,OAAO1C,EAAIyI,EAAQ,UAAU/F,OAAO3C,EAAI0I,EAAQ,MAIrFC,EAAIpM,KAAK,IAAIoG,OAAOiG,EAAQF,EAAQ,KAAK/F,OAAO1C,EAAIyI,EAAQ,MAAM/F,OAAO3C,EAAI,EAAI4I,EAAO,OAAOjG,OAAOiG,EAAQF,EAAQ,aAS/HC,EAAIG,KAAK,IAEpB,IAAIC,EAAc,CACdC,MAAO,CACHC,KAAMC,OACNC,UAAU,EACVC,QAAS,IAEb/M,KAAM,CACF4M,KAAMI,OACND,QAAS,KAEbZ,MAAO,CACHS,KAAMC,OACNE,QAASvB,EACTyB,UAAW,SAAUC,GAAK,OAAOhB,EAAuBgB,KAE5DC,WAAY,CACRP,KAAMC,OACNE,QAAS,QAEbK,WAAY,CACRR,KAAMC,OACNE,QAAS,QAEbV,OAAQ,CACJO,KAAMI,OACNF,UAAU,EACVC,QAAS,IAGbM,EAAiB9O,EAASA,EAAS,GAAImO,GAAc,CAAEY,SAAU,CAC7DV,KAAMC,OACNC,UAAU,EACVC,QAAS,SACTE,UAAW,SAAUM,GAAM,MAAO,CAAC,SAAU,OAAOnD,QAAQmD,IAAO,MAEvEC,EAAYnP,EAAIoP,gBAAgB,CAChCC,KAAM,YACNC,MAAOjB,EACPkB,MAAO,SAAUD,GACb,IAAIE,EAAWxP,EAAIyP,IAAI,GACnBC,EAAS1P,EAAIyP,IAAI,IACjBE,EAAW,WACX,IAAIrB,EAAQgB,EAAMhB,MAAOR,EAAQwB,EAAMxB,MAAOE,EAASsB,EAAMtB,OACzD4B,EAAQ1C,EAAGjM,OAAOyB,WAAW4L,EAAOlB,EAAqBU,IAAQtI,aACrEgK,EAASlB,MAAQsB,EAAMlP,OAAkB,EAATsN,EAOhC0B,EAAOpB,MAAQP,EAAa6B,EAAO5B,IAIvC,OAFA2B,IACA3P,EAAI6P,UAAUF,GACP,WAAc,OAAO3P,EAAI8P,EAAE,MAAO,CACrCC,MAAOT,EAAM3N,KACbqO,OAAQV,EAAM3N,KACd,kBAAmB,aACnBsO,MAAO,6BACPC,QAAS,OAAOjI,OAAOuH,EAASlB,MAAO,KAAKrG,OAAOuH,EAASlB,QAC7D,CACCtO,EAAI8P,EAAE,OAAQ,CACVK,KAAMb,EAAMR,WACZsB,EAAG,SAASnI,OAAOuH,EAASlB,MAAO,KAAKrG,OAAOuH,EAASlB,MAAO,SAEnEtO,EAAI8P,EAAE,OAAQ,CAAEK,KAAMb,EAAMP,WAAYqB,EAAGV,EAAOpB,cAI1D+B,EAAerQ,EAAIoP,gBAAgB,CACnCC,KAAM,eACNC,MAAOjB,EACPkB,MAAO,SAAUD,GACb,IAAIgB,EAAWtQ,EAAIyP,IAAI,MACnBE,EAAW,WACX,IAAIrB,EAAQgB,EAAMhB,MAAOR,EAAQwB,EAAMxB,MAAOnM,EAAO2N,EAAM3N,KAAMqM,EAASsB,EAAMtB,OAAQc,EAAaQ,EAAMR,WAAYC,EAAaO,EAAMP,WACtIwB,EAASD,EAAShC,MACtB,GAAKiC,EAAL,CAGA,IAAIC,EAAMD,EAAOE,WAAW,MAC5B,GAAKD,EAAL,CAGA,IAAIZ,EAAQ1C,EAAGjM,OAAOyB,WAAW4L,EAAOlB,EAAqBU,IAAQtI,aACjEgK,EAAWI,EAAMlP,OAAkB,EAATsN,EAC1B0C,EAAmBC,OAAOD,kBAAoB,EAC9CE,EAASjP,EAAO6N,EAAYkB,EAChCH,EAAOP,OAASO,EAAOR,MAAQpO,EAAO+O,EACtCF,EAAII,MAAMA,EAAOA,GACjBJ,EAAIK,UAAY/B,EAChB0B,EAAIM,SAAS,EAAG,EAAGtB,EAAUA,GAC7BgB,EAAIK,UAAY9B,EACZtB,EACA+C,EAAIL,KAAK,IAAIzC,OAAOK,EAAa6B,EAAO5B,KAGxC4B,EAAMxK,SAAQ,SAAUxD,EAAKmP,GACzBnP,EAAIwD,SAAQ,SAAU+I,EAAM6C,GACpB7C,GACAqC,EAAIM,SAASE,EAAMhD,EAAQ+C,EAAM/C,EAAQ,EAAG,YAQhE,OAFAhO,EAAIiR,UAAUtB,GACd3P,EAAI6P,UAAUF,GACP,WAAc,OAAO3P,EAAI8P,EAAE,SAAU,CACxCL,IAAKa,EACLY,MAAO,CAAEnB,MAAO,GAAG9H,OAAOqH,EAAM3N,KAAM,MAAOqO,OAAQ,GAAG/H,OAAOqH,EAAM3N,KAAM,aAInFwP,EAAYnR,EAAIoP,gBAAgB,CAChCC,KAAM,SACN+B,OAAQ,WACJ,IAAItN,EAAK/D,KAAKsR,OAAQpC,EAAWnL,EAAGmL,SAAUX,EAAQxK,EAAGwK,MAAOgD,EAAQxN,EAAGnC,KAAM4P,EAAUzN,EAAGkK,OAAQwD,EAAS1N,EAAGgK,MAAOgB,EAAahL,EAAGgL,WAAYC,EAAajL,EAAGiL,WACjKpN,EAAO2P,IAAU,EACjBtD,EAASuD,IAAY,EACrBzD,EAAQD,EAAuB2D,GAAUA,EAASrE,EACtD,OAAOnN,EAAI8P,EAAe,QAAbb,EAAqBE,EAAYkB,EAAc,CAAE/B,MAAOA,EAAO3M,KAAMA,EAAMqM,OAAQA,EAAQF,MAAOA,EAAOgB,WAAYA,EAAYC,WAAYA,KAE9JO,MAAON,IAGX,OAAOmC", "file": "js/chunk-229c2e5e.6fae31f8.js", "sourcesContent": ["/*!\n * qrcode.vue v3.4.1\n * A Vue.js component to generate QRCode.\n * © 2017-2023 @scopewu(https://github.com/scopewu)\n * MIT License.\n */\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('vue')) :\n    typeof define === 'function' && define.amd ? define(['vue'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.QrcodeVue = factory(global.Vue));\n})(this, (function (vue) { 'use strict';\n\n    /******************************************************************************\r\n    Copyright (c) Microsoft Corporation.\r\n\r\n    Permission to use, copy, modify, and/or distribute this software for any\r\n    purpose with or without fee is hereby granted.\r\n\r\n    THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\n    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\n    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\n    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\n    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\n    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\n    PERFORMANCE OF THIS SOFTWARE.\r\n    ***************************************************************************** */\r\n    /* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\n    var __assign = function() {\r\n        __assign = Object.assign || function __assign(t) {\r\n            for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n                s = arguments[i];\r\n                for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n            }\r\n            return t;\r\n        };\r\n        return __assign.apply(this, arguments);\r\n    };\r\n\r\n    typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n        var e = new Error(message);\r\n        return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n    };\n\n    /*\n     * QR Code generator library (TypeScript)\n     *\n     * Copyright (c) Project Nayuki. (MIT License)\n     * https://www.nayuki.io/page/qr-code-generator-library\n     *\n     * Permission is hereby granted, free of charge, to any person obtaining a copy of\n     * this software and associated documentation files (the \"Software\"), to deal in\n     * the Software without restriction, including without limitation the rights to\n     * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n     * the Software, and to permit persons to whom the Software is furnished to do so,\n     * subject to the following conditions:\n     * - The above copyright notice and this permission notice shall be included in\n     *   all copies or substantial portions of the Software.\n     * - The Software is provided \"as is\", without warranty of any kind, express or\n     *   implied, including but not limited to the warranties of merchantability,\n     *   fitness for a particular purpose and noninfringement. In no event shall the\n     *   authors or copyright holders be liable for any claim, damages or other\n     *   liability, whether in an action of contract, tort or otherwise, arising from,\n     *   out of or in connection with the Software or the use or other dealings in the\n     *   Software.\n     */\n    var qrcodegen;\n    (function (qrcodegen) {\n        /*---- QR Code symbol class ----*/\n        /*\n         * A QR Code symbol, which is a type of two-dimension barcode.\n         * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n         * Instances of this class represent an immutable square grid of dark and light cells.\n         * The class provides static factory functions to create a QR Code from text or binary data.\n         * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n         * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n         *\n         * Ways to create a QR Code object:\n         * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n         * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n         * - Low level: Custom-make the array of data codeword bytes (including\n         *   segment headers and final padding, excluding error correction codewords),\n         *   supply the appropriate version number, and call the QrCode() constructor.\n         * (Note that all ways require supplying the desired error correction level.)\n         */\n        var QrCode = /** @class */ (function () {\n            /*-- Constructor (low level) and fields --*/\n            // Creates a new QR Code with the given version number,\n            // error correction level, data codeword bytes, and mask number.\n            // This is a low-level API that most users should not use directly.\n            // A mid-level API is the encodeSegments() function.\n            function QrCode(\n            // The version number of this QR Code, which is between 1 and 40 (inclusive).\n            // This determines the size of this barcode.\n            version, \n            // The error correction level used in this QR Code.\n            errorCorrectionLevel, dataCodewords, msk) {\n                this.version = version;\n                this.errorCorrectionLevel = errorCorrectionLevel;\n                // The modules of this QR Code (false = light, true = dark).\n                // Immutable after constructor finishes. Accessed through getModule().\n                this.modules = [];\n                // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n                this.isFunction = [];\n                // Check scalar arguments\n                if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION)\n                    throw new RangeError(\"Version value out of range\");\n                if (msk < -1 || msk > 7)\n                    throw new RangeError(\"Mask value out of range\");\n                this.size = version * 4 + 17;\n                // Initialize both grids to be size*size arrays of Boolean false\n                var row = [];\n                for (var i = 0; i < this.size; i++)\n                    row.push(false);\n                for (var i = 0; i < this.size; i++) {\n                    this.modules.push(row.slice()); // Initially all light\n                    this.isFunction.push(row.slice());\n                }\n                // Compute ECC, draw modules\n                this.drawFunctionPatterns();\n                var allCodewords = this.addEccAndInterleave(dataCodewords);\n                this.drawCodewords(allCodewords);\n                // Do masking\n                if (msk == -1) { // Automatically choose best mask\n                    var minPenalty = 1000000000;\n                    for (var i = 0; i < 8; i++) {\n                        this.applyMask(i);\n                        this.drawFormatBits(i);\n                        var penalty = this.getPenaltyScore();\n                        if (penalty < minPenalty) {\n                            msk = i;\n                            minPenalty = penalty;\n                        }\n                        this.applyMask(i); // Undoes the mask due to XOR\n                    }\n                }\n                assert(0 <= msk && msk <= 7);\n                this.mask = msk;\n                this.applyMask(msk); // Apply the final choice of mask\n                this.drawFormatBits(msk); // Overwrite old format bits\n                this.isFunction = [];\n            }\n            /*-- Static factory functions (high level) --*/\n            // Returns a QR Code representing the given Unicode text string at the given error correction level.\n            // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n            // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n            // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n            // ecl argument if it can be done without increasing the version.\n            QrCode.encodeText = function (text, ecl) {\n                var segs = qrcodegen.QrSegment.makeSegments(text);\n                return QrCode.encodeSegments(segs, ecl);\n            };\n            // Returns a QR Code representing the given binary data at the given error correction level.\n            // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n            // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n            // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n            QrCode.encodeBinary = function (data, ecl) {\n                var seg = qrcodegen.QrSegment.makeBytes(data);\n                return QrCode.encodeSegments([seg], ecl);\n            };\n            /*-- Static factory functions (mid level) --*/\n            // Returns a QR Code representing the given segments with the given encoding parameters.\n            // The smallest possible QR Code version within the given range is automatically\n            // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n            // may be higher than the ecl argument if it can be done without increasing the\n            // version. The mask number is either between 0 to 7 (inclusive) to force that\n            // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n            // This function allows the user to create a custom sequence of segments that switches\n            // between modes (such as alphanumeric and byte) to encode text in less space.\n            // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n            QrCode.encodeSegments = function (segs, ecl, minVersion, maxVersion, mask, boostEcl) {\n                if (minVersion === void 0) { minVersion = 1; }\n                if (maxVersion === void 0) { maxVersion = 40; }\n                if (mask === void 0) { mask = -1; }\n                if (boostEcl === void 0) { boostEcl = true; }\n                if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION)\n                    || mask < -1 || mask > 7)\n                    throw new RangeError(\"Invalid value\");\n                // Find the minimal version number to use\n                var version;\n                var dataUsedBits;\n                for (version = minVersion;; version++) {\n                    var dataCapacityBits_1 = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n                    var usedBits = QrSegment.getTotalBits(segs, version);\n                    if (usedBits <= dataCapacityBits_1) {\n                        dataUsedBits = usedBits;\n                        break; // This version number is found to be suitable\n                    }\n                    if (version >= maxVersion) // All versions in the range could not fit the given data\n                        throw new RangeError(\"Data too long\");\n                }\n                // Increase the error correction level while the data still fits in the current version number\n                for (var _i = 0, _a = [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]; _i < _a.length; _i++) { // From low to high\n                    var newEcl = _a[_i];\n                    if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8)\n                        ecl = newEcl;\n                }\n                // Concatenate all segments to create the data bit string\n                var bb = [];\n                for (var _b = 0, segs_1 = segs; _b < segs_1.length; _b++) {\n                    var seg = segs_1[_b];\n                    appendBits(seg.mode.modeBits, 4, bb);\n                    appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n                    for (var _c = 0, _d = seg.getData(); _c < _d.length; _c++) {\n                        var b = _d[_c];\n                        bb.push(b);\n                    }\n                }\n                assert(bb.length == dataUsedBits);\n                // Add terminator and pad up to a byte if applicable\n                var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n                assert(bb.length <= dataCapacityBits);\n                appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n                appendBits(0, (8 - bb.length % 8) % 8, bb);\n                assert(bb.length % 8 == 0);\n                // Pad with alternating bytes until data capacity is reached\n                for (var padByte = 0xEC; bb.length < dataCapacityBits; padByte ^= 0xEC ^ 0x11)\n                    appendBits(padByte, 8, bb);\n                // Pack bits into bytes in big endian\n                var dataCodewords = [];\n                while (dataCodewords.length * 8 < bb.length)\n                    dataCodewords.push(0);\n                bb.forEach(function (b, i) {\n                    return dataCodewords[i >>> 3] |= b << (7 - (i & 7));\n                });\n                // Create the QR Code object\n                return new QrCode(version, ecl, dataCodewords, mask);\n            };\n            /*-- Accessor methods --*/\n            // Returns the color of the module (pixel) at the given coordinates, which is false\n            // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n            // If the given coordinates are out of bounds, then false (light) is returned.\n            QrCode.prototype.getModule = function (x, y) {\n                return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n            };\n            QrCode.prototype.getModules = function () {\n                return this.modules;\n            };\n            /*-- Private helper methods for constructor: Drawing function modules --*/\n            // Reads this object's version field, and draws and marks all function modules.\n            QrCode.prototype.drawFunctionPatterns = function () {\n                // Draw horizontal and vertical timing patterns\n                for (var i = 0; i < this.size; i++) {\n                    this.setFunctionModule(6, i, i % 2 == 0);\n                    this.setFunctionModule(i, 6, i % 2 == 0);\n                }\n                // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n                this.drawFinderPattern(3, 3);\n                this.drawFinderPattern(this.size - 4, 3);\n                this.drawFinderPattern(3, this.size - 4);\n                // Draw numerous alignment patterns\n                var alignPatPos = this.getAlignmentPatternPositions();\n                var numAlign = alignPatPos.length;\n                for (var i = 0; i < numAlign; i++) {\n                    for (var j = 0; j < numAlign; j++) {\n                        // Don't draw on the three finder corners\n                        if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n                            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n                    }\n                }\n                // Draw configuration data\n                this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n                this.drawVersion();\n            };\n            // Draws two copies of the format bits (with its own error correction code)\n            // based on the given mask and this object's error correction level field.\n            QrCode.prototype.drawFormatBits = function (mask) {\n                // Calculate error correction code and pack bits\n                var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is uint2, mask is uint3\n                var rem = data;\n                for (var i = 0; i < 10; i++)\n                    rem = (rem << 1) ^ ((rem >>> 9) * 0x537);\n                var bits = (data << 10 | rem) ^ 0x5412; // uint15\n                assert(bits >>> 15 == 0);\n                // Draw first copy\n                for (var i = 0; i <= 5; i++)\n                    this.setFunctionModule(8, i, getBit(bits, i));\n                this.setFunctionModule(8, 7, getBit(bits, 6));\n                this.setFunctionModule(8, 8, getBit(bits, 7));\n                this.setFunctionModule(7, 8, getBit(bits, 8));\n                for (var i = 9; i < 15; i++)\n                    this.setFunctionModule(14 - i, 8, getBit(bits, i));\n                // Draw second copy\n                for (var i = 0; i < 8; i++)\n                    this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n                for (var i = 8; i < 15; i++)\n                    this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n                this.setFunctionModule(8, this.size - 8, true); // Always dark\n            };\n            // Draws two copies of the version bits (with its own error correction code),\n            // based on this object's version field, iff 7 <= version <= 40.\n            QrCode.prototype.drawVersion = function () {\n                if (this.version < 7)\n                    return;\n                // Calculate error correction code and pack bits\n                var rem = this.version; // version is uint6, in the range [7, 40]\n                for (var i = 0; i < 12; i++)\n                    rem = (rem << 1) ^ ((rem >>> 11) * 0x1F25);\n                var bits = this.version << 12 | rem; // uint18\n                assert(bits >>> 18 == 0);\n                // Draw two copies\n                for (var i = 0; i < 18; i++) {\n                    var color = getBit(bits, i);\n                    var a = this.size - 11 + i % 3;\n                    var b = Math.floor(i / 3);\n                    this.setFunctionModule(a, b, color);\n                    this.setFunctionModule(b, a, color);\n                }\n            };\n            // Draws a 9*9 finder pattern including the border separator,\n            // with the center module at (x, y). Modules can be out of bounds.\n            QrCode.prototype.drawFinderPattern = function (x, y) {\n                for (var dy = -4; dy <= 4; dy++) {\n                    for (var dx = -4; dx <= 4; dx++) {\n                        var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n                        var xx = x + dx;\n                        var yy = y + dy;\n                        if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n                            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n                    }\n                }\n            };\n            // Draws a 5*5 alignment pattern, with the center module\n            // at (x, y). All modules must be in bounds.\n            QrCode.prototype.drawAlignmentPattern = function (x, y) {\n                for (var dy = -2; dy <= 2; dy++) {\n                    for (var dx = -2; dx <= 2; dx++)\n                        this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n                }\n            };\n            // Sets the color of a module and marks it as a function module.\n            // Only used by the constructor. Coordinates must be in bounds.\n            QrCode.prototype.setFunctionModule = function (x, y, isDark) {\n                this.modules[y][x] = isDark;\n                this.isFunction[y][x] = true;\n            };\n            /*-- Private helper methods for constructor: Codewords and masking --*/\n            // Returns a new byte string representing the given data with the appropriate error correction\n            // codewords appended to it, based on this object's version and error correction level.\n            QrCode.prototype.addEccAndInterleave = function (data) {\n                var ver = this.version;\n                var ecl = this.errorCorrectionLevel;\n                if (data.length != QrCode.getNumDataCodewords(ver, ecl))\n                    throw new RangeError(\"Invalid argument\");\n                // Calculate parameter numbers\n                var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n                var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n                var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n                var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n                var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n                // Split data into blocks and append ECC to each block\n                var blocks = [];\n                var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n                for (var i = 0, k = 0; i < numBlocks; i++) {\n                    var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n                    k += dat.length;\n                    var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n                    if (i < numShortBlocks)\n                        dat.push(0);\n                    blocks.push(dat.concat(ecc));\n                }\n                // Interleave (not concatenate) the bytes from every block into a single sequence\n                var result = [];\n                var _loop_1 = function (i) {\n                    blocks.forEach(function (block, j) {\n                        // Skip the padding byte in short blocks\n                        if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n                            result.push(block[i]);\n                    });\n                };\n                for (var i = 0; i < blocks[0].length; i++) {\n                    _loop_1(i);\n                }\n                assert(result.length == rawCodewords);\n                return result;\n            };\n            // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n            // data area of this QR Code. Function modules need to be marked off before this is called.\n            QrCode.prototype.drawCodewords = function (data) {\n                if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8))\n                    throw new RangeError(\"Invalid argument\");\n                var i = 0; // Bit index into the data\n                // Do the funny zigzag scan\n                for (var right = this.size - 1; right >= 1; right -= 2) { // Index of right column in each column pair\n                    if (right == 6)\n                        right = 5;\n                    for (var vert = 0; vert < this.size; vert++) { // Vertical counter\n                        for (var j = 0; j < 2; j++) {\n                            var x = right - j; // Actual x coordinate\n                            var upward = ((right + 1) & 2) == 0;\n                            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n                            if (!this.isFunction[y][x] && i < data.length * 8) {\n                                this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n                                i++;\n                            }\n                            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n                            // 0/false/light by the constructor and are left unchanged by this method\n                        }\n                    }\n                }\n                assert(i == data.length * 8);\n            };\n            // XORs the codeword modules in this QR Code with the given mask pattern.\n            // The function modules must be marked and the codeword bits must be drawn\n            // before masking. Due to the arithmetic of XOR, calling applyMask() with\n            // the same mask value a second time will undo the mask. A final well-formed\n            // QR Code needs exactly one (not zero, two, etc.) mask applied.\n            QrCode.prototype.applyMask = function (mask) {\n                if (mask < 0 || mask > 7)\n                    throw new RangeError(\"Mask value out of range\");\n                for (var y = 0; y < this.size; y++) {\n                    for (var x = 0; x < this.size; x++) {\n                        var invert = void 0;\n                        switch (mask) {\n                            case 0:\n                                invert = (x + y) % 2 == 0;\n                                break;\n                            case 1:\n                                invert = y % 2 == 0;\n                                break;\n                            case 2:\n                                invert = x % 3 == 0;\n                                break;\n                            case 3:\n                                invert = (x + y) % 3 == 0;\n                                break;\n                            case 4:\n                                invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n                                break;\n                            case 5:\n                                invert = x * y % 2 + x * y % 3 == 0;\n                                break;\n                            case 6:\n                                invert = (x * y % 2 + x * y % 3) % 2 == 0;\n                                break;\n                            case 7:\n                                invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n                                break;\n                            default: throw new Error(\"Unreachable\");\n                        }\n                        if (!this.isFunction[y][x] && invert)\n                            this.modules[y][x] = !this.modules[y][x];\n                    }\n                }\n            };\n            // Calculates and returns the penalty score based on state of this QR Code's current modules.\n            // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n            QrCode.prototype.getPenaltyScore = function () {\n                var result = 0;\n                // Adjacent modules in row having same color, and finder-like patterns\n                for (var y = 0; y < this.size; y++) {\n                    var runColor = false;\n                    var runX = 0;\n                    var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                    for (var x = 0; x < this.size; x++) {\n                        if (this.modules[y][x] == runColor) {\n                            runX++;\n                            if (runX == 5)\n                                result += QrCode.PENALTY_N1;\n                            else if (runX > 5)\n                                result++;\n                        }\n                        else {\n                            this.finderPenaltyAddHistory(runX, runHistory);\n                            if (!runColor)\n                                result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                            runColor = this.modules[y][x];\n                            runX = 1;\n                        }\n                    }\n                    result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n                }\n                // Adjacent modules in column having same color, and finder-like patterns\n                for (var x = 0; x < this.size; x++) {\n                    var runColor = false;\n                    var runY = 0;\n                    var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                    for (var y = 0; y < this.size; y++) {\n                        if (this.modules[y][x] == runColor) {\n                            runY++;\n                            if (runY == 5)\n                                result += QrCode.PENALTY_N1;\n                            else if (runY > 5)\n                                result++;\n                        }\n                        else {\n                            this.finderPenaltyAddHistory(runY, runHistory);\n                            if (!runColor)\n                                result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                            runColor = this.modules[y][x];\n                            runY = 1;\n                        }\n                    }\n                    result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n                }\n                // 2*2 blocks of modules having same color\n                for (var y = 0; y < this.size - 1; y++) {\n                    for (var x = 0; x < this.size - 1; x++) {\n                        var color = this.modules[y][x];\n                        if (color == this.modules[y][x + 1] &&\n                            color == this.modules[y + 1][x] &&\n                            color == this.modules[y + 1][x + 1])\n                            result += QrCode.PENALTY_N2;\n                    }\n                }\n                // Balance of dark and light modules\n                var dark = 0;\n                for (var _i = 0, _a = this.modules; _i < _a.length; _i++) {\n                    var row = _a[_i];\n                    dark = row.reduce(function (sum, color) { return sum + (color ? 1 : 0); }, dark);\n                }\n                var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n                // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n                var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n                assert(0 <= k && k <= 9);\n                result += k * QrCode.PENALTY_N4;\n                assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n                return result;\n            };\n            /*-- Private helper functions --*/\n            // Returns an ascending list of positions of alignment patterns for this version number.\n            // Each position is in the range [0,177), and are used on both the x and y axes.\n            // This could be implemented as lookup table of 40 variable-length lists of integers.\n            QrCode.prototype.getAlignmentPatternPositions = function () {\n                if (this.version == 1)\n                    return [];\n                else {\n                    var numAlign = Math.floor(this.version / 7) + 2;\n                    var step = (this.version == 32) ? 26 :\n                        Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n                    var result = [6];\n                    for (var pos = this.size - 7; result.length < numAlign; pos -= step)\n                        result.splice(1, 0, pos);\n                    return result;\n                }\n            };\n            // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n            // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n            // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n            QrCode.getNumRawDataModules = function (ver) {\n                if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION)\n                    throw new RangeError(\"Version number out of range\");\n                var result = (16 * ver + 128) * ver + 64;\n                if (ver >= 2) {\n                    var numAlign = Math.floor(ver / 7) + 2;\n                    result -= (25 * numAlign - 10) * numAlign - 55;\n                    if (ver >= 7)\n                        result -= 36;\n                }\n                assert(208 <= result && result <= 29648);\n                return result;\n            };\n            // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n            // QR Code of the given version number and error correction level, with remainder bits discarded.\n            // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n            QrCode.getNumDataCodewords = function (ver, ecl) {\n                return Math.floor(QrCode.getNumRawDataModules(ver) / 8) -\n                    QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] *\n                        QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n            };\n            // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n            // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n            QrCode.reedSolomonComputeDivisor = function (degree) {\n                if (degree < 1 || degree > 255)\n                    throw new RangeError(\"Degree out of range\");\n                // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n                // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n                var result = [];\n                for (var i = 0; i < degree - 1; i++)\n                    result.push(0);\n                result.push(1); // Start off with the monomial x^0\n                // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n                // and drop the highest monomial term which is always 1x^degree.\n                // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n                var root = 1;\n                for (var i = 0; i < degree; i++) {\n                    // Multiply the current product by (x - r^i)\n                    for (var j = 0; j < result.length; j++) {\n                        result[j] = QrCode.reedSolomonMultiply(result[j], root);\n                        if (j + 1 < result.length)\n                            result[j] ^= result[j + 1];\n                    }\n                    root = QrCode.reedSolomonMultiply(root, 0x02);\n                }\n                return result;\n            };\n            // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n            QrCode.reedSolomonComputeRemainder = function (data, divisor) {\n                var result = divisor.map(function (_) { return 0; });\n                var _loop_2 = function (b) {\n                    var factor = b ^ result.shift();\n                    result.push(0);\n                    divisor.forEach(function (coef, i) {\n                        return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n                    });\n                };\n                for (var _i = 0, data_1 = data; _i < data_1.length; _i++) {\n                    var b = data_1[_i];\n                    _loop_2(b);\n                }\n                return result;\n            };\n            // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n            // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n            QrCode.reedSolomonMultiply = function (x, y) {\n                if (x >>> 8 != 0 || y >>> 8 != 0)\n                    throw new RangeError(\"Byte out of range\");\n                // Russian peasant multiplication\n                var z = 0;\n                for (var i = 7; i >= 0; i--) {\n                    z = (z << 1) ^ ((z >>> 7) * 0x11D);\n                    z ^= ((y >>> i) & 1) * x;\n                }\n                assert(z >>> 8 == 0);\n                return z;\n            };\n            // Can only be called immediately after a light run is added, and\n            // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n            QrCode.prototype.finderPenaltyCountPatterns = function (runHistory) {\n                var n = runHistory[1];\n                assert(n <= this.size * 3);\n                var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n                return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0)\n                    + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n            };\n            // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n            QrCode.prototype.finderPenaltyTerminateAndCount = function (currentRunColor, currentRunLength, runHistory) {\n                if (currentRunColor) { // Terminate dark run\n                    this.finderPenaltyAddHistory(currentRunLength, runHistory);\n                    currentRunLength = 0;\n                }\n                currentRunLength += this.size; // Add light border to final run\n                this.finderPenaltyAddHistory(currentRunLength, runHistory);\n                return this.finderPenaltyCountPatterns(runHistory);\n            };\n            // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n            QrCode.prototype.finderPenaltyAddHistory = function (currentRunLength, runHistory) {\n                if (runHistory[0] == 0)\n                    currentRunLength += this.size; // Add light border to initial run\n                runHistory.pop();\n                runHistory.unshift(currentRunLength);\n            };\n            /*-- Constants and tables --*/\n            // The minimum version number supported in the QR Code Model 2 standard.\n            QrCode.MIN_VERSION = 1;\n            // The maximum version number supported in the QR Code Model 2 standard.\n            QrCode.MAX_VERSION = 40;\n            // For use in getPenaltyScore(), when evaluating which mask is best.\n            QrCode.PENALTY_N1 = 3;\n            QrCode.PENALTY_N2 = 3;\n            QrCode.PENALTY_N3 = 40;\n            QrCode.PENALTY_N4 = 10;\n            QrCode.ECC_CODEWORDS_PER_BLOCK = [\n                // Version: (note that index 0 is for padding, and is set to an illegal value)\n                //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n                [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n                [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n                [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n                [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], // High\n            ];\n            QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n                // Version: (note that index 0 is for padding, and is set to an illegal value)\n                //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n                [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n                [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n                [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n                [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81], // High\n            ];\n            return QrCode;\n        }());\n        qrcodegen.QrCode = QrCode;\n        // Appends the given number of low-order bits of the given value\n        // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n        function appendBits(val, len, bb) {\n            if (len < 0 || len > 31 || val >>> len != 0)\n                throw new RangeError(\"Value out of range\");\n            for (var i = len - 1; i >= 0; i--) // Append bit by bit\n                bb.push((val >>> i) & 1);\n        }\n        // Returns true iff the i'th bit of x is set to 1.\n        function getBit(x, i) {\n            return ((x >>> i) & 1) != 0;\n        }\n        // Throws an exception if the given condition is false.\n        function assert(cond) {\n            if (!cond)\n                throw new Error(\"Assertion error\");\n        }\n        /*---- Data segment class ----*/\n        /*\n         * A segment of character/binary/control data in a QR Code symbol.\n         * Instances of this class are immutable.\n         * The mid-level way to create a segment is to take the payload data\n         * and call a static factory function such as QrSegment.makeNumeric().\n         * The low-level way to create a segment is to custom-make the bit buffer\n         * and call the QrSegment() constructor with appropriate values.\n         * This segment class imposes no length restrictions, but QR Codes have restrictions.\n         * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n         * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n         */\n        var QrSegment = /** @class */ (function () {\n            /*-- Constructor (low level) and fields --*/\n            // Creates a new QR Code segment with the given attributes and data.\n            // The character count (numChars) must agree with the mode and the bit buffer length,\n            // but the constraint isn't checked. The given bit buffer is cloned and stored.\n            function QrSegment(\n            // The mode indicator of this segment.\n            mode, \n            // The length of this segment's unencoded data. Measured in characters for\n            // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n            // Always zero or positive. Not the same as the data's bit length.\n            numChars, \n            // The data bits of this segment. Accessed through getData().\n            bitData) {\n                this.mode = mode;\n                this.numChars = numChars;\n                this.bitData = bitData;\n                if (numChars < 0)\n                    throw new RangeError(\"Invalid argument\");\n                this.bitData = bitData.slice(); // Make defensive copy\n            }\n            /*-- Static factory functions (mid level) --*/\n            // Returns a segment representing the given binary data encoded in\n            // byte mode. All input byte arrays are acceptable. Any text string\n            // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n            QrSegment.makeBytes = function (data) {\n                var bb = [];\n                for (var _i = 0, data_2 = data; _i < data_2.length; _i++) {\n                    var b = data_2[_i];\n                    appendBits(b, 8, bb);\n                }\n                return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n            };\n            // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n            QrSegment.makeNumeric = function (digits) {\n                if (!QrSegment.isNumeric(digits))\n                    throw new RangeError(\"String contains non-numeric characters\");\n                var bb = [];\n                for (var i = 0; i < digits.length;) { // Consume up to 3 digits per iteration\n                    var n = Math.min(digits.length - i, 3);\n                    appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n                    i += n;\n                }\n                return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n            };\n            // Returns a segment representing the given text string encoded in alphanumeric mode.\n            // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n            // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n            QrSegment.makeAlphanumeric = function (text) {\n                if (!QrSegment.isAlphanumeric(text))\n                    throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n                var bb = [];\n                var i;\n                for (i = 0; i + 2 <= text.length; i += 2) { // Process groups of 2\n                    var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n                    temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n                    appendBits(temp, 11, bb);\n                }\n                if (i < text.length) // 1 character remaining\n                    appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n                return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n            };\n            // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n            // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n            QrSegment.makeSegments = function (text) {\n                // Select the most efficient segment encoding automatically\n                if (text == \"\")\n                    return [];\n                else if (QrSegment.isNumeric(text))\n                    return [QrSegment.makeNumeric(text)];\n                else if (QrSegment.isAlphanumeric(text))\n                    return [QrSegment.makeAlphanumeric(text)];\n                else\n                    return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n            };\n            // Returns a segment representing an Extended Channel Interpretation\n            // (ECI) designator with the given assignment value.\n            QrSegment.makeEci = function (assignVal) {\n                var bb = [];\n                if (assignVal < 0)\n                    throw new RangeError(\"ECI assignment value out of range\");\n                else if (assignVal < (1 << 7))\n                    appendBits(assignVal, 8, bb);\n                else if (assignVal < (1 << 14)) {\n                    appendBits(2, 2, bb);\n                    appendBits(assignVal, 14, bb);\n                }\n                else if (assignVal < 1000000) {\n                    appendBits(6, 3, bb);\n                    appendBits(assignVal, 21, bb);\n                }\n                else\n                    throw new RangeError(\"ECI assignment value out of range\");\n                return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n            };\n            // Tests whether the given string can be encoded as a segment in numeric mode.\n            // A string is encodable iff each character is in the range 0 to 9.\n            QrSegment.isNumeric = function (text) {\n                return QrSegment.NUMERIC_REGEX.test(text);\n            };\n            // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n            // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n            // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n            QrSegment.isAlphanumeric = function (text) {\n                return QrSegment.ALPHANUMERIC_REGEX.test(text);\n            };\n            /*-- Methods --*/\n            // Returns a new copy of the data bits of this segment.\n            QrSegment.prototype.getData = function () {\n                return this.bitData.slice(); // Make defensive copy\n            };\n            // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n            // the given version. The result is infinity if a segment has too many characters to fit its length field.\n            QrSegment.getTotalBits = function (segs, version) {\n                var result = 0;\n                for (var _i = 0, segs_2 = segs; _i < segs_2.length; _i++) {\n                    var seg = segs_2[_i];\n                    var ccbits = seg.mode.numCharCountBits(version);\n                    if (seg.numChars >= (1 << ccbits))\n                        return Infinity; // The segment's length doesn't fit the field's bit width\n                    result += 4 + ccbits + seg.bitData.length;\n                }\n                return result;\n            };\n            // Returns a new array of bytes representing the given string encoded in UTF-8.\n            QrSegment.toUtf8ByteArray = function (str) {\n                str = encodeURI(str);\n                var result = [];\n                for (var i = 0; i < str.length; i++) {\n                    if (str.charAt(i) != \"%\")\n                        result.push(str.charCodeAt(i));\n                    else {\n                        result.push(parseInt(str.substring(i + 1, i + 3), 16));\n                        i += 2;\n                    }\n                }\n                return result;\n            };\n            /*-- Constants --*/\n            // Describes precisely all strings that are encodable in numeric mode.\n            QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n            // Describes precisely all strings that are encodable in alphanumeric mode.\n            QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n            // The set of all legal characters in alphanumeric mode,\n            // where each character value maps to the index in the string.\n            QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n            return QrSegment;\n        }());\n        qrcodegen.QrSegment = QrSegment;\n    })(qrcodegen || (qrcodegen = {}));\n    /*---- Public helper enumeration ----*/\n    (function (qrcodegen) {\n        (function (QrCode) {\n            /*\n             * The error correction level in a QR Code symbol. Immutable.\n             */\n            var Ecc = /** @class */ (function () {\n                /*-- Constructor and fields --*/\n                function Ecc(\n                // In the range 0 to 3 (unsigned 2-bit integer).\n                ordinal, \n                // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n                formatBits) {\n                    this.ordinal = ordinal;\n                    this.formatBits = formatBits;\n                }\n                /*-- Constants --*/\n                Ecc.LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n                Ecc.MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n                Ecc.QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n                Ecc.HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n                return Ecc;\n            }());\n            QrCode.Ecc = Ecc;\n        })(qrcodegen.QrCode || (qrcodegen.QrCode = {}));\n    })(qrcodegen || (qrcodegen = {}));\n    /*---- Public helper enumeration ----*/\n    (function (qrcodegen) {\n        (function (QrSegment) {\n            /*\n             * Describes how a segment's data bits are interpreted. Immutable.\n             */\n            var Mode = /** @class */ (function () {\n                /*-- Constructor and fields --*/\n                function Mode(\n                // The mode indicator bits, which is a uint4 value (range 0 to 15).\n                modeBits, \n                // Number of character count bits for three different version ranges.\n                numBitsCharCount) {\n                    this.modeBits = modeBits;\n                    this.numBitsCharCount = numBitsCharCount;\n                }\n                /*-- Method --*/\n                // (Package-private) Returns the bit width of the character count field for a segment in\n                // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n                Mode.prototype.numCharCountBits = function (ver) {\n                    return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n                };\n                /*-- Constants --*/\n                Mode.NUMERIC = new Mode(0x1, [10, 12, 14]);\n                Mode.ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n                Mode.BYTE = new Mode(0x4, [8, 16, 16]);\n                Mode.KANJI = new Mode(0x8, [8, 10, 12]);\n                Mode.ECI = new Mode(0x7, [0, 0, 0]);\n                return Mode;\n            }());\n            QrSegment.Mode = Mode;\n        })(qrcodegen.QrSegment || (qrcodegen.QrSegment = {}));\n    })(qrcodegen || (qrcodegen = {}));\n    var QR = qrcodegen;\n\n    var defaultErrorCorrectLevel = 'H';\n    var ErrorCorrectLevelMap = {\n        L: QR.QrCode.Ecc.LOW,\n        M: QR.QrCode.Ecc.MEDIUM,\n        Q: QR.QrCode.Ecc.QUARTILE,\n        H: QR.QrCode.Ecc.HIGH,\n    };\n    // Thanks the `qrcode.react`\n    var SUPPORTS_PATH2D = (function () {\n        try {\n            new Path2D().addPath(new Path2D());\n        }\n        catch (e) {\n            return false;\n        }\n        return true;\n    })();\n    function validErrorCorrectLevel(level) {\n        return level in ErrorCorrectLevelMap;\n    }\n    function generatePath(modules, margin) {\n        if (margin === void 0) { margin = 0; }\n        var ops = [];\n        modules.forEach(function (row, y) {\n            var start = null;\n            row.forEach(function (cell, x) {\n                if (!cell && start !== null) {\n                    // M0 0h7v1H0z injects the space with the move and drops the comma,\n                    // saving a char per operation\n                    ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n                    start = null;\n                    return;\n                }\n                // end of row, clean up or skip\n                if (x === row.length - 1) {\n                    if (!cell) {\n                        // We would have closed the op above already so this can only mean\n                        // 2+ light modules in a row.\n                        return;\n                    }\n                    if (start === null) {\n                        // Just a single dark module.\n                        ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n                    }\n                    else {\n                        // Otherwise finish the current line.\n                        ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n                    }\n                    return;\n                }\n                if (cell && start === null) {\n                    start = x;\n                }\n            });\n        });\n        return ops.join('');\n    }\n    var QRCodeProps = {\n        value: {\n            type: String,\n            required: true,\n            default: '',\n        },\n        size: {\n            type: Number,\n            default: 100,\n        },\n        level: {\n            type: String,\n            default: defaultErrorCorrectLevel,\n            validator: function (l) { return validErrorCorrectLevel(l); },\n        },\n        background: {\n            type: String,\n            default: '#fff',\n        },\n        foreground: {\n            type: String,\n            default: '#000',\n        },\n        margin: {\n            type: Number,\n            required: false,\n            default: 0,\n        },\n    };\n    var QRCodeVueProps = __assign(__assign({}, QRCodeProps), { renderAs: {\n            type: String,\n            required: false,\n            default: 'canvas',\n            validator: function (as) { return ['canvas', 'svg'].indexOf(as) > -1; },\n        } });\n    var QRCodeSvg = vue.defineComponent({\n        name: 'QRCodeSvg',\n        props: QRCodeProps,\n        setup: function (props) {\n            var numCells = vue.ref(0);\n            var fgPath = vue.ref('');\n            var generate = function () {\n                var value = props.value, level = props.level, margin = props.margin;\n                var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n                numCells.value = cells.length + margin * 2;\n                // Drawing strategy: instead of a rect per module, we're going to create a\n                // single path for the dark modules and layer that on top of a light rect,\n                // for a total of 2 DOM nodes. We pay a bit more in string concat but that's\n                // way faster than DOM ops.\n                // For level 1, 441 nodes -> 2\n                // For level 40, 31329 -> 2\n                fgPath.value = generatePath(cells, margin);\n            };\n            generate();\n            vue.onUpdated(generate);\n            return function () { return vue.h('svg', {\n                width: props.size,\n                height: props.size,\n                'shape-rendering': \"crispEdges\",\n                xmlns: 'http://www.w3.org/2000/svg',\n                viewBox: \"0 0 \".concat(numCells.value, \" \").concat(numCells.value),\n            }, [\n                vue.h('path', {\n                    fill: props.background,\n                    d: \"M0,0 h\".concat(numCells.value, \"v\").concat(numCells.value, \"H0z\"),\n                }),\n                vue.h('path', { fill: props.foreground, d: fgPath.value }),\n            ]); };\n        },\n    });\n    var QRCodeCanvas = vue.defineComponent({\n        name: 'QRCodeCanvas',\n        props: QRCodeProps,\n        setup: function (props) {\n            var canvasEl = vue.ref(null);\n            var generate = function () {\n                var value = props.value, level = props.level, size = props.size, margin = props.margin, background = props.background, foreground = props.foreground;\n                var canvas = canvasEl.value;\n                if (!canvas) {\n                    return;\n                }\n                var ctx = canvas.getContext('2d');\n                if (!ctx) {\n                    return;\n                }\n                var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n                var numCells = cells.length + margin * 2;\n                var devicePixelRatio = window.devicePixelRatio || 1;\n                var scale = (size / numCells) * devicePixelRatio;\n                canvas.height = canvas.width = size * devicePixelRatio;\n                ctx.scale(scale, scale);\n                ctx.fillStyle = background;\n                ctx.fillRect(0, 0, numCells, numCells);\n                ctx.fillStyle = foreground;\n                if (SUPPORTS_PATH2D) {\n                    ctx.fill(new Path2D(generatePath(cells, margin)));\n                }\n                else {\n                    cells.forEach(function (row, rdx) {\n                        row.forEach(function (cell, cdx) {\n                            if (cell) {\n                                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n                            }\n                        });\n                    });\n                }\n            };\n            vue.onMounted(generate);\n            vue.onUpdated(generate);\n            return function () { return vue.h('canvas', {\n                ref: canvasEl,\n                style: { width: \"\".concat(props.size, \"px\"), height: \"\".concat(props.size, \"px\") },\n            }); };\n        },\n    });\n    var QrcodeVue = vue.defineComponent({\n        name: 'Qrcode',\n        render: function () {\n            var _a = this.$props, renderAs = _a.renderAs, value = _a.value, _size = _a.size, _margin = _a.margin, _level = _a.level, background = _a.background, foreground = _a.foreground;\n            var size = _size >>> 0;\n            var margin = _margin >>> 0;\n            var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n            return vue.h(renderAs === 'svg' ? QRCodeSvg : QRCodeCanvas, { value: value, size: size, margin: margin, level: level, background: background, foreground: foreground });\n        },\n        props: QRCodeVueProps,\n    });\n\n    return QrcodeVue;\n\n}));\n"], "sourceRoot": ""}