{"version": 3, "sources": ["webpack:///./src/views/admin/Permission.vue?b767", "webpack:///./src/views/admin/Permission.vue", "webpack:///./src/views/admin/Permission.vue?50c0", "webpack:///./src/icons/svg-black/LimitManage.svg"], "names": ["class", "_createElementVNode", "src", "_imports_0", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_form", "_component_el_form_item", "label", "_component_el_select", "$setup", "roleId", "$event", "onChange", "handleSearch", "_Fragment", "_renderList", "roleList", "list", "r", "_createBlock", "_component_el_option", "key", "id", "name", "value", "rolePerm", "perms", "p", "index", "_component_el_checkbox", "checkAll", "indeterminate", "isIndeterminate", "handleCheckAllChange", "title", "_component_el_checkbox_group", "checkedList", "handleCheckedChange", "subs", "sub", "_component_el_button", "type", "onClick", "saveEdit", "_component_el_dialog", "addVisible", "width", "onClose", "getData", "footer", "_withCtx", "_hoisted_5", "_cache", "addRole", "model", "form", "data", "label-width", "prop", "_component_el_input", "setup", "reactive", "request", "get", "then", "res", "ref", "val", "post", "JSON", "stringify", "code", "ElMessage", "success", "error", "map", "checkedCount", "length", "handleAdd", "url", "method", "onMounted", "__exports__", "render", "module", "exports"], "mappings": "2IAAA,W,4KCESA,MAAM,U,QAGLC,gCAAyD,UAAtDA,gCAAkD,OAA7CC,IAAAC,Q,OAITH,MAAM,a,GACJA,MAAM,c,GA2CHA,MAAM,iB,okBApDlBI,gCA0DM,YAzDJH,gCAMM,MANNI,EAMM,CALJC,yBAIgBC,EAAA,CAJDC,UAAU,KAAG,C,6BAC1B,IAEqB,CAFrBF,yBAEqBG,EAAA,M,6BADnB,IAAyD,CAAzDC,E,6BAAyD,Y,gBAI/DT,gCAoCM,MApCNU,EAoCM,CAnCJV,gCAwBM,MAxBNW,EAwBM,CAvBJN,yBASUO,EAAA,M,6BARR,IAOe,CAPfP,yBAOeQ,EAAA,CAPDC,MAAM,MAAI,C,6BACtB,IAKY,CALZT,yBAKYU,EAAA,C,WALQC,EAAAC,O,qCAAAD,EAAAC,OAAMC,GAAGC,SAAQH,EAAAI,aAAcrB,MAAM,sB,8BAC5C,IAA0B,E,2BAArCI,gCAA4FkB,cAAA,KAAAC,wBAArEN,EAAAO,SAASC,KAAdC,I,yBAAlBC,yBAA4FC,EAAA,CAArDC,IAAKH,EAAEI,GAAKf,MAAOW,EAAEK,KAAOC,MAAON,EAAEI,I,oHAqBpF1B,gCAQMkB,cAAA,KAAAC,wBARyCN,EAAAgB,SAASC,MAAK,CAA3BC,EAAGC,K,yBAArChC,gCAQM,OARDJ,MAAM,eAAqD6B,IAAKM,G,CACnE7B,yBAC2E+B,EAAA,C,WADrDF,EAAEG,S,yBAAFH,EAAEG,SAAQnB,EAAGoB,cAAeJ,EAAEK,gBACjDpB,SAAMD,GAAEF,EAAAwB,qBAAqBtB,EAAQiB,I,8BAAQ,IAAa,C,0DAAVD,EAAEO,OAAK,K,2EAC1DpC,yBAIoBqC,EAAA,C,WAJQR,EAAES,Y,yBAAFT,EAAES,YAAWzB,EAAGC,SAAMD,GAAEF,EAAA4B,oBAAoB1B,EAAQiB,I,8BACjE,IAAqB,E,2BAAlChC,gCAEgBkB,cAAA,KAAAC,wBAFWY,EAAEW,KAATC,I,yBAApBpB,yBAEgBU,EAAA,CAFoBR,IAAKkB,EAAMhC,MAAOgC,EAAIjB,I,8BAAI,IAE5D,C,0DADAiB,EAAIL,OAAK,K,oGAIfpC,yBAA0D0C,EAAA,CAA/CC,KAAK,UAAWC,QAAOjC,EAAAkC,U,8BAAU,IAAE,C,6BAAF,Q,sBAE9C7C,yBAYY8C,EAAA,CAZDV,MAAM,O,WAAgBzB,EAAAoC,W,qCAAApC,EAAAoC,WAAUlC,GAAEmC,MAAM,MAAOC,QAAOtC,EAAAuC,S,CAMpDC,OAAMC,qBACf,IAGO,CAHPzD,gCAGO,OAHP0D,EAGO,CAFLrD,yBAAqD0C,EAAA,CAAzCE,QAAKU,EAAA,KAAAA,EAAA,GAAAzC,GAAEF,EAAAoC,YAAa,I,8BAAO,IAAE,C,6BAAF,Q,MACvC/C,yBAAyD0C,EAAA,CAA9CC,KAAK,UAAWC,QAAOjC,EAAA4C,S,8BAAS,IAAE,C,6BAAF,Q,qDAR/C,IAIU,CAJVvD,yBAIUO,EAAA,CAJAiD,MAAO7C,EAAA8C,KAAKC,KAAMC,cAAY,Q,8BACtC,IAEe,CAFf3D,yBAEeQ,EAAA,CAFDC,MAAM,OAAOmD,KAAK,Q,8BAC9B,IAAwE,CAAxE5D,yBAAwE6D,EAAA,C,WAArDlD,EAAA8C,KAAKC,KAAKjC,K,qCAAVd,EAAA8C,KAAKC,KAAKjC,KAAIZ,GAAEnB,MAAM,qB,oHAkBpC,GACb+B,KAAM,aACNqC,QACE,MAAM5C,EAAW6C,sBAAS,CACxB5C,KAAM,KAIF+B,EAAUA,IACPc,OAAQC,IAAI,yBAAyBC,KAAMC,IAChDjD,EAASC,KAAOgD,EAAIT,OAIlB/B,EAAWoC,sBAAS,CACxBnC,MAAO,CACL,CACEI,UAAU,EACVE,iBAAiB,EACjBI,YAAa,GACbd,GAAI,GACJY,MAAO,GACPI,KAAM,CACJ,CACEJ,MAAO,GACPZ,GAAI,SAORZ,EAASwD,iBAAI,KAGbrD,EAAgBsD,IACpBL,OAAQC,IAAI,sBAAwBI,GAAKH,KAAMC,IAC7CxC,EAASC,MAAQuC,EAAIT,QAGnBb,EAAWA,KACfmB,OACGM,KACC,sBAAwB1D,EAAOc,MAC/B,cAAgB6C,KAAKC,UAAU7C,EAASC,QAEzCsC,KAAMC,IACY,MAAbA,EAAIM,KACNC,OAAUC,QAAQ,SAElBD,OAAUE,MAAM,YAIlBzC,EAAuBA,CAACkC,EAAKvC,KACjCH,EAASC,MAAME,GAAOQ,YAAc+B,EAChC1C,EAASC,MAAME,GAAOU,KAAKqC,IAAKpC,GAAQA,EAAIjB,IAC5C,GACJG,EAASC,MAAME,GAAOI,iBAAkB,GAEpCK,EAAsBA,CAACb,EAAOI,KAClC,MAAMgD,EAAepD,EAAMqD,OAC3BpD,EAASC,MAAME,GAAOE,SACpB8C,IAAiBnD,EAASC,MAAME,GAAOU,KAAKuC,OAC9CpD,EAASC,MAAME,GAAOI,gBACpB4C,EAAe,GAAKA,EAAenD,EAASC,MAAME,GAAOU,KAAKuC,QAE5DhC,EAAaqB,kBAAI,GACvB,IAAIX,EAAOM,sBAAS,CAClBL,KAAM,CACJjC,KAAM,MAKV,MAAMuD,EAAYA,KAChBjC,EAAWrB,OAAQ,GAEf6B,EAAUA,KACdS,eAAQ,CACNiB,IAAK,gBACLC,OAAQ,OACRxB,KAAMD,EAAKC,OACVQ,KAAMC,IACPO,OAAUC,QAAQ,SAClBlB,EAAKC,KAAKjC,KAAO,GACjBsB,EAAWrB,OAAQ,KAWvB,OAPAyD,uBAAU,KACRjC,IAAUgB,KAAK,KACbtD,EAAOc,MAAQR,EAASC,KAAK4D,OAAS,EAAI7D,EAASC,KAAK,GAAGK,GAAK,GAChET,EAAaH,EAAOc,WAIjB,CACLR,WACAS,WACAf,SACA6C,OACAV,aACAG,UACAK,UACAxC,eACA8B,WACAV,uBACAI,sBACAyC,e,iCCzKN,MAAMI,EAA2B,IAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAE1E,gB,qBCTfC,EAAOC,QAAU,IAA0B", "file": "js/chunk-49b25783.6cbbe20c.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./Permission.vue?vue&type=style&index=0&id=d035c5ec&scoped=true&lang=css\"", "<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/LimitManage.svg\"></i> 权限管理\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form>\r\n          <el-form-item label=\"角色\">\r\n            <el-select v-model=\"roleId\" @change=\"handleSearch\" class=\"handle-select mr10\">\r\n              <el-option v-for=\"r in roleList.list\" :key=\"r.id\" :label=\"r.name\" :value=\"r.id\"></el-option>\r\n              <!-- <el-option key=\"2\" label=\"辅导员\" value=\"2\"></el-option>\r\n              <el-option key=\"3\" label=\"财务管理员\" value=\"3\"></el-option>\r\n              <el-option key=\"4\" label=\"后勤部\" value=\"4\"></el-option> -->\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <!--        <el-button-->\r\n        <!--          type=\"primary\"-->\r\n        <!--          icon=\"el-icon-circle-plus-outline\"-->\r\n        <!--          @click=\"handleAdd\"-->\r\n        <!--          >新增角色</el-button-->\r\n        <!--        >-->\r\n        <!--        <el-button-->\r\n        <!--          type=\"primary\"-->\r\n        <!--          icon=\"el-icon-circle-plus-outline\"-->\r\n        <!--          @click=\"handleDelete\"-->\r\n        <!--          >删除角色</el-button-->\r\n        <!--        >-->\r\n      </div>\r\n      <div class=\"handle-check\" v-for=\"(p, index) in rolePerm.perms\" :key=\"p\">\r\n        <el-checkbox v-model=\"p.checkAll\" :indeterminate=\"p.isIndeterminate\"\r\n          @change=\"handleCheckAllChange($event, index)\">{{ p.title }}</el-checkbox>\r\n        <el-checkbox-group v-model=\"p.checkedList\" @change=\"handleCheckedChange($event, index)\">\r\n          <el-checkbox v-for=\"sub in p.subs\" :key=\"sub\" :label=\"sub.id\">{{\r\n            sub.title\r\n          }}</el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <el-button type=\"primary\" @click=\"saveEdit\">保存</el-button>\r\n    </div>\r\n    <el-dialog title=\"添加角色\" v-model=\"addVisible\" width=\"30%\" @close=\"getData\">\r\n      <el-form :model=\"form.data\" label-width=\"80px\">\r\n        <el-form-item label=\"角色名称\" prop=\"name\">\r\n          <el-input v-model=\"form.data.name\" class=\"handle-input mr10\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"addVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"addRole\">保存</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, onMounted } from \"vue\";\r\nimport request from \"../../utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\n\r\nexport default {\r\n  name: \"Permission\",\r\n  setup() {\r\n    const roleList = reactive({\r\n      list: [],\r\n\r\n    });\r\n    // 查询操作\r\n    const getData = () => {\r\n      return request.get(\"/parking/role/listAll\").then((res) => {\r\n        roleList.list = res.data;\r\n      });\r\n    };\r\n\r\n    const rolePerm = reactive({\r\n      perms: [\r\n        {\r\n          checkAll: false,\r\n          isIndeterminate: false,\r\n          checkedList: [],\r\n          id: \"\",\r\n          title: \"\",\r\n          subs: [\r\n            {\r\n              title: \"\",\r\n              id: \"\",\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n    });\r\n\r\n    const roleId = ref(\"1\");\r\n\r\n    // 查询操作\r\n    const handleSearch = (val) => {\r\n      request.get(\"/parking/role/perm/\" + val).then((res) => {\r\n        rolePerm.perms = res.data;\r\n      });\r\n    };\r\n    const saveEdit = () => {\r\n      request\r\n        .post(\r\n          \"/parking/role/perm/\" + roleId.value,\r\n          \"permission=\" + JSON.stringify(rolePerm.perms)\r\n        )\r\n        .then((res) => {\r\n          if (res.code === \"0\") {\r\n            ElMessage.success(\"保存成功！\");\r\n          } else {\r\n            ElMessage.error(\"保存失败！\");\r\n          }\r\n        });\r\n    };\r\n    const handleCheckAllChange = (val, index) => {\r\n      rolePerm.perms[index].checkedList = val\r\n        ? rolePerm.perms[index].subs.map((sub) => sub.id)\r\n        : [];\r\n      rolePerm.perms[index].isIndeterminate = false;\r\n    };\r\n    const handleCheckedChange = (value, index) => {\r\n      const checkedCount = value.length;\r\n      rolePerm.perms[index].checkAll =\r\n        checkedCount === rolePerm.perms[index].subs.length;\r\n      rolePerm.perms[index].isIndeterminate =\r\n        checkedCount > 0 && checkedCount < rolePerm.perms[index].subs.length;\r\n    };\r\n    const addVisible = ref(false);\r\n    var form = reactive({\r\n      data: {\r\n        name: \"\",\r\n      },\r\n    });\r\n\r\n    // 新增操作\r\n    const handleAdd = () => {\r\n      addVisible.value = true;\r\n    };\r\n    const addRole = () => {\r\n      request({\r\n        url: \"/parking/role\",\r\n        method: \"POST\",\r\n        data: form.data,\r\n      }).then((res) => {\r\n        ElMessage.success(\"保存成功！\");\r\n        form.data.name = \"\";\r\n        addVisible.value = false;\r\n      });\r\n    };\r\n\r\n    onMounted(() => {\r\n      getData().then(() => {\r\n        roleId.value = roleList.list.length > 0 ? roleList.list[0].id : \"\";\r\n        handleSearch(roleId.value);\r\n      });\r\n    });\r\n\r\n    return {\r\n      roleList,\r\n      rolePerm,\r\n      roleId,\r\n      form,\r\n      addVisible,\r\n      getData,\r\n      addRole,\r\n      handleSearch,\r\n      saveEdit,\r\n      handleCheckAllChange,\r\n      handleCheckedChange,\r\n      handleAdd,\r\n    };\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.handle-box {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.handle-select {\r\n  width: 120px;\r\n}\r\n\r\n.handle-check {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table {\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n\r\n.red {\r\n  color: #ff0000;\r\n}\r\n\r\n.mr10 {\r\n  margin-right: 10px;\r\n}\r\n\r\n.table-td-thumb {\r\n  display: block;\r\n  margin: auto;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n</style>\r\n", "import { render } from \"./Permission.vue?vue&type=template&id=d035c5ec&scoped=true\"\nimport script from \"./Permission.vue?vue&type=script&lang=js\"\nexport * from \"./Permission.vue?vue&type=script&lang=js\"\n\nimport \"./Permission.vue?vue&type=style&index=0&id=d035c5ec&scoped=true&lang=css\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-d035c5ec\"]])\n\nexport default __exports__", "module.exports = __webpack_public_path__ + \"img/LimitManage.7230bdde.svg\";"], "sourceRoot": ""}