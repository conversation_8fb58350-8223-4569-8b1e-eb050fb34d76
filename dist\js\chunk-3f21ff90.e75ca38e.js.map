{"version": 3, "sources": ["webpack:///./src/views/admin/VehicleReservation.vue?29be", "webpack:///./src/icons/svg-black/VehicleReservation.svg", "webpack:///./src/views/admin/VehicleReservation.vue", "webpack:///./src/views/admin/VehicleReservation.vue?51e3"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "rules", "yardCode", "required", "message", "trigger", "yardName", "channelName", "plateNumber", "vehicleClassification", "merchantName", "notifierName", "releaseReason", "appointmentTime", "remark", "form", "reactive", "data", "id", "enterTime", "leaveTime", "appointmentFlag", "reserveFlag", "tableRowClassName", "row", "rowIndex", "console", "log", "cellStyle", "column", "columnIndex", "style", "padding", "onReset", "applicantUserId", "ref", "value", "localStorage", "getItem", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "getData", "request", "get", "params", "then", "res", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "handleReservation", "post", "handleAdd", "handleEdit", "yardCodeList", "yardNameList", "channelNameList", "vehicleClassificationList", "merchantNameList", "releaseReasonList", "notifierNameList", "changeYardCode", "changeYardName", "parkCode", "changeMerchantName", "formRef", "save", "validate", "valid", "method", "url", "code", "msg", "__exports__"], "mappings": "yIAAA,W,qBCAAA,EAAOC,QAAU,IAA0B,uC,ycC0JrCC,EAAO,+B,4CACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACV,CAAEC,MAAO,OAAQC,KAAM,YACvB,CAAED,MAAO,OAAQC,KAAM,YACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,OAAQC,KAAM,yBACvB,CAAED,MAAO,OAAQC,KAAM,gBACvB,CAAED,MAAO,QAASC,KAAM,gBACxB,CAAED,MAAO,OAAQC,KAAM,aACvB,CAAED,MAAO,OAAQC,KAAM,aACvB,CAAED,MAAO,KAAMC,KAAM,UACrB,CAACD,MAAO,OAAQC,KAAM,cACtB,CAACD,MAAO,OAAQC,KAAM,eAEpBC,EAAQ,CACVC,SAAU,CACN,CACIC,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBC,SAAU,CACN,CACIH,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBE,YAAa,CACT,CACIJ,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBG,YAAa,CACT,CACIL,UAAU,EACVC,QAAS,SACTC,QAAS,SAGjBI,sBAAuB,CACnB,CACIN,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBK,aAAc,CACV,CACIP,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBM,aAAc,CACV,CACIR,UAAU,EACVC,QAAS,WACTC,QAAS,WAGjBO,cAAe,CACX,CACIT,UAAU,EACVC,QAAS,UACTC,QAAS,WAGjBQ,gBAAiB,CACb,CACIV,UAAU,EACVC,QAAS,QACTC,QAAS,WAGjBS,OAAQ,CACJ,CACIX,UAAU,EACVC,QAAS,UACTC,QAAS,UAIfU,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJhB,SAAU,GACVI,SAAU,GACVC,YAAa,GACbC,YAAa,GACbC,sBAAuB,GACvBC,aAAc,GACdE,cAAe,GACfD,aAAc,GACdQ,UAAW,GACXC,UAAW,GACXN,OAAQ,GACRO,iBAAkB,EAClBC,aAAc,KAShBC,EAAoBA,EAAEC,MAAKC,eAE3BA,EAAW,GAAK,GAAK,GACnBC,QAAQC,IAAIF,GACX,YACGA,EAAW,GAAK,GAAK,GACzBC,QAAQC,IAAIF,GACX,iBAFF,EAMDG,EAAaA,EAAEJ,MAAKK,SAAQJ,WAASK,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,GAGLE,EAAUA,KACZlB,EAAKE,KAAKC,GAAK,GACfH,EAAKE,KAAKf,SAAW,GACrBa,EAAKE,KAAKX,SAAW,GACrBS,EAAKE,KAAKV,YAAc,GACxBQ,EAAKE,KAAKT,YAAc,GACxBO,EAAKE,KAAKR,sBAAwB,GAClCM,EAAKE,KAAKP,aAAe,GACzBK,EAAKE,KAAKL,cAAgB,GAC1BG,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKH,OAAS,IAKjBoB,GAHWC,kBAAI,GACLA,iBAAI,IAEIA,iBAAI,KAC5BD,EAAgBE,MAAQC,aAAaC,QAAQ,UAE7C,MAAMC,EAAQvB,sBAAS,CACnBR,YAAa,GACbF,SAAU,GACVkC,QAAS,EACTC,SAAU,KAERC,EAAYP,iBAAI,IAChBQ,EAAYR,iBAAI,GAEhBS,GADSP,aAAaC,QAAQ,UACdH,kBAAI,IAIpBU,GAH2BV,kBAAI,GAGrBU,KACZC,OACKC,IAAIrD,EAAO,OAAQ,CAChBsD,OAAQT,IAEXU,KAAMC,IACHR,EAAUN,MAAQc,EAAIjC,KAAKkC,QAC3BR,EAAUP,MAAQc,EAAIjC,KAAKmC,MAC3B1B,QAAQC,IAAIuB,EAAIjC,UAG5B4B,IAEA,MAAMQ,EAAeA,KACjBd,EAAMC,QAAU,EAChBK,KAGES,EAAoBC,IACtBhB,EAAME,SAAWc,EACjBV,KAGEW,EAAoBD,IACtBhB,EAAMC,QAAUe,EAChBV,KAGEY,EAAeA,CAACC,EAAOC,KAEzBC,OAAaC,QAAQ,UAAW,KAAM,CAClCC,KAAM,YAELb,KAAK,KACFH,OAAQiB,OAAOrE,EAAOiE,GAAKV,KAAMC,IACzBA,EAAIjC,MACJ+C,OAAUC,QAAQ,QAClB1B,EAAMC,QAAU,EAChBK,IACAH,EAAUN,MAAM8B,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAI3BC,MAAM,SAITC,EAAqB7C,IAEvBoC,OAAaC,QAAQ,iBAAkB,KAAM,CACzCC,KAAM,YAELb,KAAK,KACFH,OAAQwB,KAAK,6CAA8C9C,GAAKyB,KAAMC,IAC7DA,EAAIjC,KAIL+C,OAAUG,MAAM,WAHhBH,OAAUC,QAAQ,UAClBpB,SAMXuB,MAAM,SAITG,EAAYA,KACdtC,IACAW,EAAcR,OAAQ,GAIpBoC,GADcrC,kBAAI,GACJX,IAChBoB,EAAcR,OAAQ,EACtBrB,EAAKE,KAAKC,GAAKM,EAAIN,GACnBH,EAAKE,KAAKf,SAAWsB,EAAItB,SACzBa,EAAKE,KAAKX,SAAWkB,EAAIlB,SACzBS,EAAKE,KAAKV,YAAciB,EAAIjB,YAC5BQ,EAAKE,KAAKT,YAAcgB,EAAIhB,YAC5BO,EAAKE,KAAKR,sBAAwBe,EAAIf,sBACtCM,EAAKE,KAAKP,aAAec,EAAId,aAC7BK,EAAKE,KAAKL,cAAgBY,EAAIZ,cAC9BG,EAAKE,KAAKN,aAAea,EAAIb,aAC7BI,EAAKE,KAAKJ,gBAAkBW,EAAIX,gBAChCE,EAAKE,KAAKH,OAASU,EAAIV,SAErB2D,EAAetC,iBAAI,IACnBuC,EAAevC,iBAAI,IACnBwC,EAAkBxC,iBAAI,IACtByC,EAA4BzC,iBAAI,IAChC0C,EAAmB1C,iBAAI,IACvB2C,EAAoB3C,iBAAI,IACxB4C,EAAmB5C,iBAAI,IACDA,iBAAI,IAChCW,OAAQC,IAAI,8BAA8BE,KAAMC,IAC5CuB,EAAarC,MAAQc,EAAIjC,OAE7B6B,OAAQC,IAAI,wDAAwDE,KAC/DC,IACG0B,EAA0BxC,MAAQc,EAAIjC,OAE9C6B,OAAQC,IAAI,sCAAsCE,KAC7CC,IACG2B,EAAiBzC,MAAQc,EAAIjC,OAErC6B,OAAQC,IAAI,wCAAwCE,KAC/CC,IACG4B,EAAkB1C,MAAQc,EAAIjC,OAEtC,MAAM+D,EAAiBA,KACnBlC,OACKC,IAAI,6BACD,CACIC,OAAQ,CACJ9C,SAAUa,EAAKE,KAAKf,YAG/B+C,KAAMC,IACHnC,EAAKE,KAAKX,SAAW,GACrBS,EAAKE,KAAKV,YAAc,GACxBQ,EAAKE,KAAKR,sBAAwB,GAClCM,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKP,aAAe,GACzBK,EAAKE,KAAKL,cAAgB,GAC1B8D,EAAatC,MAAQc,EAAIjC,QAI/BgE,EAAiBA,KACnBvD,QAAQC,IAAIZ,EAAKE,KAAKf,UACtB4C,OACKC,IAAI,mCACD,CACIC,OAAQ,CACJkC,SAAUnE,EAAKE,KAAKf,YAG/B+C,KAAMC,IACHnC,EAAKE,KAAKV,YAAc,GACxBQ,EAAKE,KAAKR,sBAAwB,GAClCM,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKP,aAAe,GACzBK,EAAKE,KAAKL,cAAgB,GAC1B+D,EAAgBvC,MAAQc,EAAIjC,QAIlCkE,EAAqBA,KACvBrC,OACKC,IAAI,qCACD,CACIC,OAAQ,CACJtC,aAAcK,EAAKE,KAAKP,gBAGnCuC,KAAMC,IACHnC,EAAKE,KAAKN,aAAe,GACzBI,EAAKE,KAAKL,cAAgB,GAC1BmE,EAAiB3C,MAAQc,EAAIjC,QAInCmE,EAAUjD,iBAAI,MACdkD,EAAOA,KAETD,EAAQhD,MAAMkD,SAAUC,IACpB,IAAIA,EA+BA,OAAO,EA9BP,IAAIC,EAA0B,KAAjBzE,EAAKE,KAAKC,GAAY,OAAS,MAC5C4B,eAAQ,CACJ2C,IAAK,8BACLD,OAAQA,EACRvE,KAAM,CACFC,GAAIH,EAAKE,KAAKC,GACdhB,SAAUa,EAAKE,KAAKf,SACpBI,SAAUS,EAAKE,KAAKX,SACpBC,YAAaQ,EAAKE,KAAKV,YACvBC,YAAaO,EAAKE,KAAKT,YACvBC,sBAAuBM,EAAKE,KAAKR,sBACjCC,aAAcK,EAAKE,KAAKP,aACxBE,cAAeG,EAAKE,KAAKL,cACzBD,aAAcI,EAAKE,KAAKN,aACxBE,gBAAiBE,EAAKE,KAAKJ,gBAC3BC,OAAQC,EAAKE,KAAKH,UAEvBmC,KAAMC,IACLnC,EAAKE,KAAO,GACK,OAAbiC,EAAIwC,MACJ7C,IACAmB,OAAUC,QAAQ,SAElBrB,EAAcR,OAAQ,IAEtBQ,EAAcR,OAAQ,EACtB4B,OAAUG,MAAMjB,EAAIyC,W,qnTCxfxC,MAAMC,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD", "file": "js/chunk-3f21ff90.e75ca38e.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./VehicleReservation.vue?vue&type=style&index=0&id=649a41f3&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/VehicleReservation.63e19717.svg\";", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/VehicleReservation.svg\"></i> 外来车辆信息管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                        <el-input v-model=\"query.yardName\" placeholder=\"车场名称\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"车牌号码\">\r\n                        <el-input v-model=\"query.plateNumber\" placeholder=\"车牌号码\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\">搜索\r\n                    </el-button>\r\n                    <el-button type=\"primary\" class=\"addButton\" @click=\"handleAdd\">新增预约车辆\r\n                    </el-button>\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\"  border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\" :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\" v-for=\"item in props\"\r\n                    :key=\"item.prop\" align=\"center\" width=\"110px\" height=\"10px\">\r\n                </el-table-column>\r\n                <el-table-column label=\"车牌识别\" prop=\"appointmentFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 0\" effect=\"dark\" size=\"large\">成功\r\n                        </el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"入场状态\" prop=\"reserveFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"danger\" v-if=\"scope.row.reserveFlag === 0\" effect=\"dark\" size=\"large\">未入场\r\n                        </el-tag>\r\n                        <el-tag type=\"success\" v-else-if=\"scope.row.reserveFlag === 1\" effect=\"dark\" size=\"large\">已入场\r\n                        </el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"250px\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"text\" icon=\"el-icon-edit\" @click=\"handleEdit(scope.row)\">修改信息\r\n                        </el-button>\r\n                        <el-button type=\"text\" icon=\"el-icon-delete\" \r\n                            @click=\"handleDelete(scope.$index, scope.row.id)\">删除信息\r\n                        </el-button>\r\n                        <el-button type=\"text\" icon=\"el-icon-position\" \r\n                            @click=\"handleReservation(scope.row)\">添加入场\r\n                        </el-button>\r\n\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"外来车辆预约信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"车场编号\" prop=\"yardCode\">\r\n                        <el-select v-model=\"form.data.yardCode\" placeholder=\"请选择车场编号\">\r\n                            <el-option v-for=\"item in yardCodeList\" :key=\"item.yardCode\" :label=\"item.yardCode\"\r\n                                :value=\"item.yardCode\" @click=\"changeYardCode\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\" @click=\"changeYardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"入口通道\" prop=\"channelName\">\r\n                        <el-select v-model=\"form.data.channelName\" placeholder=\"请选择入口通道\">\r\n                            <el-option v-for=\"item in channelNameList\" :key=\"item.channelName\"\r\n                                :label=\"item.channelName\" :value=\"item.channelName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车牌号码\" prop=\"plateNumber\">\r\n                        <el-input v-model=\"form.data.plateNumber\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车辆分类\" prop=\"vehicleClassification\">\r\n                        <el-select v-model=\"form.data.vehicleClassification\" placeholder=\"请选择车辆分类\">\r\n                            <el-option v-for=\"item in vehicleClassificationList\" :key=\"item.vehicleClassification\"\r\n                                :label=\"item.vehicleClassification\" :value=\"item.vehicleClassification\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"商户名称\" prop=\"merchantName\">\r\n                        <el-select v-model=\"form.data.merchantName\" placeholder=\"请选择商户名称\">\r\n                            <el-option v-for=\"item in merchantNameList\" :key=\"item.merchantName\" :label=\"item.merchantName\"\r\n                                :value=\"item.merchantName\" @click=\"changeMerchantName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"通知人姓名\" prop=\"notifierName\">\r\n                        <el-select v-model=\"form.data.notifierName\" placeholder=\"请选择通知人\">\r\n                            <el-option v-for=\"item in notifierNameList\" :key=\"item.notifierName\" :label=\"item.notifierName\"\r\n                                :value=\"item.notifierName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"放行原因\" prop=\"releaseReason\">\r\n                        <el-select v-model=\"form.data.releaseReason\" placeholder=\"请选择放行原因\">\r\n                            <el-option v-for=\"item in releaseReasonList\" :key=\"item.releaseReason\"\r\n                                :label=\"item.releaseReason\" :value=\"item.releaseReason\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                     <!-- <el-form-item label=\"预约时间\" prop=\"notifierName\">\r\n                        <el-date-picker v-model=\"form.data.appointmentTime\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n                            value-format=\"YYYY-MM-DD HH:mm:ss\" type=\"datetime\" placeholder=\"选择日期\">\r\n                            <el-option v-for=\"item in appointmentTimeList\" :key=\"item.appointmentTime\"\r\n                                :label=\"item.appointmentTime\" :value=\"item.appointmentTime\">\r\n                            </el-option>\r\n                        </el-date-picker>\r\n                    </el-form-item> -->\r\n                    <el-form-item label=\"备注\" prop=\"remark\">\r\n                        <el-input type=\"textarea\" v-model=\"form.data.remark\" style=\"width: 80%\"></el-input>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\n\r\nconst root = \"/parking/vehicleReservation/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    { label: \"车场编码\", prop: \"yardCode\" },\r\n    { label: \"车场名称\", prop: \"yardName\" },\r\n    { label: \"入场通道\", prop: \"channelName\" },\r\n    { label: \"车牌号码\", prop: \"plateNumber\" },\r\n    { label: \"车辆分类\", prop: \"vehicleClassification\" },\r\n    { label: \"商户名称\", prop: \"merchantName\" },\r\n    { label: \"通知人姓名\", prop: \"notifierName\" },\r\n    { label: \"进场时间\", prop: \"enterTime\" },\r\n    { label: \"离场原因\", prop: \"leaveTime\" },\r\n    { label: \"备注\", prop: \"remark\" },\r\n    {label: \"创建时间\", prop: \"createTime\"},\r\n    {label: \"修改时间\", prop: \"updateTime\"},\r\n];\r\nconst rules = {\r\n    yardCode: [\r\n        {\r\n            required: true,\r\n            message: \"请选择车辆编号\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    yardName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择车场名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    channelName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择入场通道\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    plateNumber: [\r\n        {\r\n            required: true,\r\n            message: \"请输入车牌号\",\r\n            trigger: \"blur\",\r\n        },\r\n    ],\r\n    vehicleClassification: [\r\n        {\r\n            required: true,\r\n            message: \"请选择车辆分类\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    merchantName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择商户名称\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    notifierName: [\r\n        {\r\n            required: true,\r\n            message: \"请选择通知人姓名\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    releaseReason: [\r\n        {\r\n            required: true,\r\n            message: \"请选择放行原因\",\r\n            trigger: \"change\",\r\n        },\r\n    ],\r\n    appointmentTime: [\r\n        {\r\n            required: true,\r\n            message: \"请选择时间\",\r\n            trigger: \"change\"\r\n        },\r\n    ],\r\n    remark: [\r\n        {\r\n            required: true,\r\n            message: \"请输入备注信息\",\r\n            trigger: \"blur\"\r\n        },\r\n    ],\r\n};\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        yardCode: '',\r\n        yardName: '',\r\n        channelName: '',\r\n        plateNumber: '',\r\n        vehicleClassification: '',\r\n        merchantName: '',\r\n        releaseReason: '',\r\n        notifierName: '',\r\n        enterTime: '',\r\n        leaveTime: '',\r\n        remark: '',\r\n        appointmentFlag: -1,\r\n        reserveFlag: -1\r\n    },\r\n\r\n});                                                                                                                                                 \r\n\r\n// const handleExport = () => {\r\n//     window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n// };\r\n//指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.yardCode = ''\r\n    form.data.yardName = ''\r\n    form.data.channelName = ''\r\n    form.data.plateNumber = ''\r\n    form.data.vehicleClassification = ''\r\n    form.data.merchantName = ''\r\n    form.data.releaseReason = ''\r\n    form.data.notifierName = ''\r\n    form.data.remark = ''\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\n\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\n\r\nconst query = reactive({\r\n    plateNumber: \"\",\r\n    yardName: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\nconst dialogVisibleReservation = ref(false);\r\n\r\n// 获取表格数据\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"page\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n            console.log(res.data);\r\n        });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n        type: \"warning\",\r\n    })\r\n        .then(() => {\r\n            request.delete(root + sid).then((res) => {\r\n                if (res.data) {\r\n                    ElMessage.success(\"删除成功\");\r\n                    query.pageNum = 1;\r\n                    getData();\r\n                    tableData.value.splice(index, 1);\r\n                } else {\r\n                    ElMessage.error(\"删除失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n//添加入场操作 添加入场弹窗打开\r\nconst handleReservation = (row) => {\r\n    // 二次确认添加入场\r\n    ElMessageBox.confirm(\"确定要将此条数据添加入场吗？\", \"提示\", {\r\n        type: \"success\",\r\n    })\r\n        .then(() => {\r\n            request.post(\"/parking/vehicleReservation/addReservation\", row).then((res) => {\r\n                if (!res.data) {\r\n                    ElMessage.success(\"添加入场成功\");\r\n                    getData();\r\n                } else {\r\n                    ElMessage.error(\"添加入场失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n// 新增操作\r\nconst handleAdd = () => {\r\n    onReset();\r\n    dialogVisible.value = true;\r\n};\r\n// 表格编辑时弹窗和保存\r\nconst editVisible = ref(false);\r\nconst handleEdit = (row) => {\r\n    dialogVisible.value = true\r\n    form.data.id = row.id\r\n    form.data.yardCode = row.yardCode\r\n    form.data.yardName = row.yardName\r\n    form.data.channelName = row.channelName\r\n    form.data.plateNumber = row.plateNumber\r\n    form.data.vehicleClassification = row.vehicleClassification\r\n    form.data.merchantName = row.merchantName\r\n    form.data.releaseReason = row.releaseReason\r\n    form.data.notifierName = row.notifierName\r\n    form.data.appointmentTime = row.appointmentTime\r\n    form.data.remark = row.remark\r\n};\r\nconst yardCodeList = ref([]);\r\nconst yardNameList = ref([]);\r\nconst channelNameList = ref([]);\r\nconst vehicleClassificationList = ref([]);\r\nconst merchantNameList = ref([]);\r\nconst releaseReasonList = ref([]);\r\nconst notifierNameList = ref([]);\r\nconst appointmentTimeList = ref([]);\r\nrequest.get(\"/parking/yardInfo/yardCode\").then((res) => {\r\n    yardCodeList.value = res.data;\r\n});\r\nrequest.get(\"/parking/vehicleClassification/vehicleClassification\").then(\r\n    (res) => {\r\n        vehicleClassificationList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/notifierInfo/merchantName\").then(\r\n    (res) => {\r\n        merchantNameList.value = res.data;\r\n    });\r\nrequest.get(\"/parking/releaseReason/releaseReason\").then(\r\n    (res) => {\r\n        releaseReasonList.value = res.data;\r\n    });\r\nconst changeYardCode = () => {\r\n    request\r\n        .get(\"/parking/yardInfo/yardName\",\r\n            {\r\n                params: {\r\n                    yardCode: form.data.yardCode,\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.yardName = \"\";\r\n            form.data.channelName = \"\";\r\n            form.data.vehicleClassification = \"\";\r\n            form.data.notifierName = \"\";\r\n            form.data.merchantName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            yardNameList.value = res.data;\r\n        });\r\n\r\n};\r\nconst changeYardName = () => {\r\n    console.log(form.data.yardCode);\r\n    request\r\n        .get(\"/parking/channelInfo/channelName\",\r\n            {\r\n                params: {\r\n                    parkCode: form.data.yardCode\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.channelName = \"\";\r\n            form.data.vehicleClassification = \"\";\r\n            form.data.notifierName = \"\";\r\n            form.data.merchantName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            channelNameList.value = res.data         \r\n        });\r\n\r\n};\r\nconst changeMerchantName = () => {\r\n    request\r\n        .get(\"/parking/notifierInfo/notifierName\",\r\n            {\r\n                params: {\r\n                    merchantName: form.data.merchantName\r\n                },\r\n            })\r\n        .then((res) => {\r\n            form.data.notifierName = \"\";\r\n            form.data.releaseReason = \"\";\r\n            notifierNameList.value = res.data;\r\n            \r\n        });\r\n};\r\nconst formRef = ref(null);\r\nconst save = () => {\r\n    // 表单校验\r\n    formRef.value.validate((valid) => {\r\n        if (valid) {\r\n            var method = form.data.id === \"\" ? \"POST\" : \"PUT\";\r\n            request({\r\n                url: \"/parking/vehicleReservation\",\r\n                method: method,\r\n                data: {\r\n                    id: form.data.id,\r\n                    yardCode: form.data.yardCode,\r\n                    yardName: form.data.yardName,\r\n                    channelName: form.data.channelName,\r\n                    plateNumber: form.data.plateNumber,\r\n                    vehicleClassification: form.data.vehicleClassification,\r\n                    merchantName: form.data.merchantName,\r\n                    releaseReason: form.data.releaseReason,\r\n                    notifierName: form.data.notifierName,\r\n                    appointmentTime: form.data.appointmentTime,\r\n                    remark: form.data.remark\r\n                },\r\n            }).then((res) => {\r\n                form.data = {}\r\n                if (res.code === null) {\r\n                    getData()\r\n                    ElMessage.success(\"提交成功！\");\r\n                    // 关闭当前页面的标签页;\r\n                    dialogVisible.value = false\r\n                } else {\r\n                    dialogVisible.value = false\r\n                    ElMessage.error(res.msg);\r\n                }\r\n            });\r\n        } else {\r\n            return false;\r\n        }\r\n    });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n\tbackground-color: rgb(245, 247, 250) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>", "import script from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VehicleReservation.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VehicleReservation.vue?vue&type=style&index=0&id=649a41f3&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-649a41f3\"]])\n\nexport default __exports__"], "sourceRoot": ""}