{"remainingRequest": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\桌面\\ParkingManageDemo\\manage-front\\src\\views\\admin\\Department.vue?vue&type=template&id=52f7a543&scoped=true", "dependencies": [{"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\src\\views\\admin\\Department.vue", "mtime": 1700016961802}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementVNode", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_component_el_form", "inline", "model", "$setup", "query", "_component_el_form_item", "label", "_component_el_input", "name", "$event", "placeholder", "clearable", "leader", "_component_el_button", "type", "icon", "onClick", "handleSearch", "handleAdd", "_component_el_table", "data", "tableData", "border", "ref", "_Fragment", "_renderList", "props", "item", "_createBlock", "_component_el_table_column", "prop", "key", "width", "align", "fixed", "default", "_withCtx", "scope", "handleEdit", "row", "departmentId", "handleDelete", "$index", "_hoisted_5", "_component_el_pagination", "currentPage", "pageNum", "pageSize", "layout", "total", "pageTotal", "onSizeChange", "handleSizeChange", "onCurrentChange", "handlePageChange"], "sources": ["F:\\桌面\\ParkingManageDemo\\manage-front\\src\\views\\admin\\Department.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i class=\"el-icon-location\"></i> 部门管理\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n            :inline=\"true\"\r\n            :model=\"query\"\r\n            class=\"demo-form-inline\"\r\n            label-width=\"60px\"\r\n        >\r\n          <el-form-item label-width=\"60px\" label=\"部门\">\r\n            <el-input\r\n                v-model=\"query.name\"\r\n                placeholder=\"部门名称\"\r\n                class=\"handle-input mr10\"\r\n                  clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"60px\" label=\"负责人\">\r\n            <el-input\r\n                v-model=\"query.leader\"\r\n                placeholder=\"负责人\"\r\n                class=\"handle-input mr10\"\r\n                  clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <el-button\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n          >新增\r\n          </el-button\r\n          >\r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        class=\"table\"\r\n        ref=\"multipleTable\"\r\n        header-cell-class-name=\"table-header\"\r\n      >\r\n        <el-table-column\r\n          :show-overflow-tooltip=\"true\"\r\n          :prop=\"item.prop\"\r\n          :label=\"item.label\"\r\n          v-for=\"item in props\"\r\n          :key=\"item.prop\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column>\r\n\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row.departmentId)\"\r\n              >编辑\r\n            </el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              class=\"red\"\r\n              @click=\"handleDelete(scope.$index, scope.row.departmentId)\"\r\n              >删除</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n        </el-table-column>\r\n\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n          :currentPage=\"query.pageNum\"\r\n          :page-sizes=\"[10, 20, 40]\"\r\n          :page-size=\"query.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"pageTotal\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive } from \"vue\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useRouter } from \"vue-router\";\r\nimport request from \"../../utils/request\";\r\n\r\nexport default {\r\n  name: \"Department\",\r\n  setup() {\r\n    const root = \"/parking/department/\";\r\n    const router = useRouter();\r\n\r\n    const props = [\r\n      { label: \"部门名称\", prop: \"departmentName\" },\r\n      { label: \"部门地址\", prop: \"departmentAddress\" },\r\n      { label: \"联系人\", prop: \"leader\" },\r\n      { label: \"联系电话\", prop: \"leaderPhone\" },\r\n      // { label: \"联系电话\", prop: \"president\" },\r\n    ];\r\n\r\n    const query = reactive({\r\n      name: \"\",\r\n      leader:\"\",\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n    });\r\n    const tableData = ref([]);\r\n    const pageTotal = ref(0);\r\n    // 获取表格数据\r\n\r\n    const getData = () => {\r\n      request\r\n        .get(root + \"page\", {\r\n          params: query,\r\n        })\r\n        .then((res) => {\r\n          tableData.value = res.data.records;\r\n          pageTotal.value = res.data.total;\r\n        });\r\n    };\r\n    getData();\r\n    // 查询操作\r\n    const handleSearch = () => {\r\n      query.pageNum = 1;\r\n      getData();\r\n    };\r\n    // 分页大小\r\n    const handleSizeChange = (val) => {\r\n      query.pageSize = val;\r\n      getData();\r\n    };\r\n    // 分页导航\r\n    const handlePageChange = (val) => {\r\n      query.pageNum = val;\r\n      getData();\r\n    };\r\n    // 删除操作\r\n    const handleDelete = (index, sid) => {\r\n      // 二次确认删除\r\n      ElMessageBox.confirm(\"确定要删除吗？\", \"提示\", {\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          request.delete(root + sid).then((res) => {\r\n            if (res.data) {\r\n              ElMessage.success(\"删除成功\");\r\n              tableData.value.splice(index, 1);\r\n            } else {\r\n              ElMessage.error(\"删除失败\");\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    };\r\n\r\n    // 新增操作\r\n    const handleAdd = () => {\r\n      router.push(\"/admin/parking/addDepartment\");\r\n    };\r\n\r\n    // 表格编辑时弹窗和保存\r\n    const editVisible = ref(false);\r\n    let form = reactive({\r\n      name: \"\",\r\n      address: \"\",\r\n    });\r\n    const handleEdit = (id) => {\r\n      console.log(id)\r\n      router.push({ path: \"/admin/parking/addDepartment\", query: { id: id } });\r\n    };\r\n\r\n    return {\r\n      props,\r\n      query,\r\n      tableData,\r\n      pageTotal,\r\n      editVisible,\r\n      form,\r\n      handleSearch,\r\n      handleSizeChange,\r\n      handlePageChange,\r\n      handleAdd,\r\n      handleDelete,\r\n      handleEdit,\r\n    };\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.handle-box {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.handle-select {\r\n  width: 120px;\r\n}\r\n\r\n.handle-input {\r\n  width: 300px;\r\n  display: inline-block;\r\n}\r\n.table {\r\n  width: 100%;\r\n  font-size: 14px;\r\n}\r\n.red {\r\n  color: #ff0000;\r\n}\r\n.mr10 {\r\n  margin-right: 10px;\r\n}\r\n.table-td-thumb {\r\n  display: block;\r\n  margin: auto;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n</style>\r\n"], "mappings": ";;;EAESA,KAAK,EAAC;AAAQ;gEAGbC,mBAAA,CAAgC;EAA7BD,KAAK,EAAC;AAAkB;;EAI5BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAwElBA,KAAK,EAAC;AAAY;;;;;;;;;;;uBAjF3BE,mBAAA,CA8FM,cA7FJD,mBAAA,CAMM,OANNE,UAMM,GALJC,YAAA,CAIgBC,wBAAA;IAJDC,SAAS,EAAC;EAAG;sBAC1B,MAEqB,CAFrBF,YAAA,CAEqBG,6BAAA;wBADnB,MAAgC,CAAhCC,UAAgC,E,iBAAA,QAClC,E;;;;QAGJP,mBAAA,CAqFM,OArFNQ,UAqFM,GApFJR,mBAAA,CAkCM,OAlCNS,UAkCM,GAjCJN,YAAA,CAgCUO,kBAAA;IA/BLC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAEC,MAAA,CAAAC,KAAK;IACbf,KAAK,EAAC,kBAAkB;IACxB,aAAW,EAAC;;sBAEd,MAOe,CAPfI,YAAA,CAOeY,uBAAA;MAPD,aAAW,EAAC,MAAM;MAACC,KAAK,EAAC;;wBACrC,MAKY,CALZb,YAAA,CAKYc,mBAAA;oBAJCJ,MAAA,CAAAC,KAAK,CAACI,IAAI;mEAAVL,MAAA,CAAAC,KAAK,CAACI,IAAI,GAAAC,MAAA;QACnBC,WAAW,EAAC,MAAM;QAClBrB,KAAK,EAAC,mBAAmB;QACvBsB,SAAS,EAAT;;;QAGRlB,YAAA,CAOeY,uBAAA;MAPD,aAAW,EAAC,MAAM;MAACC,KAAK,EAAC;;wBACrC,MAKY,CALZb,YAAA,CAKYc,mBAAA;oBAJCJ,MAAA,CAAAC,KAAK,CAACQ,MAAM;mEAAZT,MAAA,CAAAC,KAAK,CAACQ,MAAM,GAAAH,MAAA;QACrBC,WAAW,EAAC,KAAK;QACjBrB,KAAK,EAAC,mBAAmB;QACvBsB,SAAS,EAAT;;;QAGRlB,YAAA,CAEYoB,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,gBAAgB;MAAEC,OAAK,EAAEb,MAAA,CAAAc;;wBACvD,MACD,C,iBADC,KACD,E;;wBAEAxB,YAAA,CAIYoB,oBAAA;MAHRC,IAAI,EAAC,SAAS;MACbE,OAAK,EAAEb,MAAA,CAAAe;;wBACX,MACD,C,iBADC,KACD,E;;;;sBAIJzB,YAAA,CAoCW0B,mBAAA;IAnCRC,IAAI,EAAEjB,MAAA,CAAAkB,SAAS;IAChBC,MAAM,EAAN,EAAM;IACNjC,KAAK,EAAC,OAAO;IACbkC,GAAG,EAAC,eAAe;IACnB,wBAAsB,EAAC;;sBAMrB,MAAqB,E,kBAJvBhC,mBAAA,CAOkBiC,SAAA,QAAAC,WAAA,CAHDtB,MAAA,CAAAuB,KAAK,EAAbC,IAAI;2BAJbC,YAAA,CAOkBC,0BAAA;QANf,uBAAqB,EAAE,IAAI;QAC3BC,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfxB,KAAK,EAAEqB,IAAI,CAACrB,KAAK;QAEjByB,GAAG,EAAEJ,IAAI,CAACG;;eAGbrC,YAAA,CAmBkBoC,0BAAA;wBAjBlB,MAgBkB,CAhBlBpC,YAAA,CAgBkBoC,0BAAA;QAhBDvB,KAAK,EAAC,IAAI;QAAC0B,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC,QAAQ;QAACC,KAAK,EAAC;;QAChDC,OAAO,EAAAC,QAAA,CAAEC,KAAK,KACvB5C,YAAA,CAKYoB,oBAAA;UAJVC,IAAI,EAAC,MAAM;UACXC,IAAI,EAAC,cAAc;UAClBC,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAAmC,UAAU,CAACD,KAAK,CAACE,GAAG,CAACC,YAAY;;4BACxC,MACH,C,iBADG,KACH,E;;+BACA/C,YAAA,CAKiBoB,oBAAA;UAJfC,IAAI,EAAC,MAAM;UACXC,IAAI,EAAC,gBAAgB;UACrB1B,KAAK,EAAC,KAAK;UACV2B,OAAK,EAAAP,MAAA,IAAEN,MAAA,CAAAsC,YAAY,CAACJ,KAAK,CAACK,MAAM,EAAEL,KAAK,CAACE,GAAG,CAACC,YAAY;;4BACxD,MAAE,C,iBAAF,IAAE,E;;;;;;;;mBAOXlD,mBAAA,CAWM,OAXNqD,UAWM,GAVJlD,YAAA,CASgBmD,wBAAA;IARbC,WAAW,EAAE1C,MAAA,CAAAC,KAAK,CAAC0C,OAAO;IAC1B,YAAU,EAAE,YAAY;IACxB,WAAS,EAAE3C,MAAA,CAAAC,KAAK,CAAC2C,QAAQ;IAC1BC,MAAM,EAAC,yCAAyC;IAC/CC,KAAK,EAAE9C,MAAA,CAAA+C,SAAS;IAChBC,YAAW,EAAEhD,MAAA,CAAAiD,gBAAgB;IAC7BC,eAAc,EAAElD,MAAA,CAAAmD"}]}