{"remainingRequest": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!F:\\桌面\\ParkingManageDemo\\manage-front\\src\\views\\admin\\IllegalRegiste.vue?vue&type=script&setup=true&lang=js", "dependencies": [{"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\src\\views\\admin\\IllegalRegiste.vue", "mtime": 1721716820607}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\babel.config.js", "mtime": 1646360471255}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\桌面\\ParkingManageDemo\\manage-front\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "useRouter", "reactive", "ref", "request", "ElMessage", "ElMessageBox", "useStore", "root", "router", "route", "store", "props", "label", "prop", "viewShow", "content1", "query", "community", "plateNumber", "operatordate", "pageNum", "pageSize", "tableData", "pageTotal", "petImage", "index", "row", "url", "imgurl", "console", "log", "previewImage", "imageUrl", "alert", "dangerouslyUseHTMLString", "showClose", "closeOnClickModal", "dialogClass", "getData", "get", "params", "then", "res", "value", "data", "records", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "tableRowClassName", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding"], "sources": ["F:/桌面/ParkingManageDemo/manage-front/src/views/admin/IllegalRegiste.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/IllegalRegiste.svg\"></i>&nbsp; 违规查询\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n          <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n            <el-input v-model=\"query.community\" placeholder=\"部门名称\" class=\"handle-input mr10\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"80px\" label=\"违规车牌\">\r\n            <el-input v-model=\"query.plateNumber\" placeholder=\"违规车牌\" class=\"handle-input mr10\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"70px\" label=\"违规日期\">\r\n            <el-date-picker v-model=\"query.operatordate\" type=\"date\" placeholder=\"选择一个日期\" format=\"YYYY-MM-DD\"\r\n              value-format=\"YYYY-MM-DD\" clearable>\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索\r\n          </el-button>\r\n        </el-form>\r\n      </div>\r\n      <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n        :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n        <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\" v-for=\"item in props\"\r\n          :key=\"item.prop\" align=\"center\">\r\n        </el-table-column>\r\n          <el-table-column align=\"center\" label=\"图片\" prop=\"imgurl\">\r\n            <template #default=\"scope\">\r\n              <div class=\"demo-image__preview\">\r\n                <img style=\"width: 100%; height: 100%\"\r\n                :src=\"petImage(scope.$index, scope.row)\" @click=\"previewImage(petImage(scope.$index, scope.row))\"/>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n          @current-change=\"handlePageChange\">\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\nconst root = \"/parking/illegalregiste/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  { label: \"省份\", prop: \"province\" },\r\n  { label: \"地市\", prop: \"city\" },\r\n  { label: \"区县\", prop: \"district\" },\r\n  { label: \"小区\", prop: \"community\" },\r\n  { label: \"栋号\", prop: \"building\" },\r\n  { label: \"单元\", prop: \"units\" },\r\n  { label: \"车辆类别\", prop: \"cartype\" },\r\n  { label: \"车牌号码\", prop: \"platenumber\" },\r\n  // { label: \"违规位置\", prop: \"location\" },\r\n  { label: \"违规日期\", prop: \"operatordate\" },\r\n  // { label: \"违规图片\", prop: \"imgurl\" },\r\n];\r\nconst viewShow = ref(false)\r\nconst content1 = ref(\"\");\r\nconst query = reactive({\r\n  community: \"\",\r\n  plateNumber: \"\",\r\n  operatordate: \"\",\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\n//图片路径\r\nconst petImage = (index, row) => {\r\n  var url = \"https://www.xuerparking.cn:8543/uploadfile/images\" + row.imgurl\r\n  console.log(url)\r\n  return url\r\n};\r\nconst previewImage = (imageUrl) =>{\r\n  ElMessageBox.alert('<img src=\"' + imageUrl + '\" style=\"max-width: 100%; max-height: 100%;\">', '违规停车图片预览', {\r\n        dangerouslyUseHTMLString: true,\r\n        showClose: true,\r\n        closeOnClickModal: true,\r\n        dialogClass: 'preview-dialog'\r\n      })\r\n}\r\n// 获取表格数据\r\nconst getData = () => {\r\n  request\r\n    .get(root + \"allpage\", {\r\n      params: query,\r\n    })\r\n    .then((res) => {\r\n      tableData.value = res.data.records;\r\n      // tableData.value.imgurl = baseImg + res.data.records.imgurl;\r\n      console.log(res.data.records)\r\n      //将查询到的结果中的imgurl遍历拼接\r\n      pageTotal.value = res.data.total;\r\n    });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n  // console.log(rowIndex)\r\n  if ((rowIndex + 1) % 2 == 0) {\r\n    console.log(rowIndex)\r\n    return 'odd-row';\r\n  } else if ((rowIndex + 1) % 2 != 0) {\r\n    console.log(rowIndex)\r\n    return 'even-row';\r\n  }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n  let style = { padding: '8px 3px' }\r\n  return style\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n  background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n  background-color: rgb(255, 255, 255) !important;\r\n}\r\n\r\n.demo-image__error .image-slot {\r\n  font-size: 30px;\r\n}\r\n\r\n.demo-image__error .image-slot .el-icon {\r\n  font-size: 30px;\r\n}\r\n\r\n.demo-image__error .el-image {\r\n  width: 100%;\r\n  height: 200px;\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoDA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SAASC,QAAQ,EAAEC,GAAG,QAAQ,KAAK;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,QAAQ,QAAQ,MAAM;AAE/B,MAAMC,IAAI,GAAG,0BAA0B;;;;IACvC,MAAMC,MAAM,GAAGR,SAAS,CAAC,CAAC;IAC1B,MAAMS,KAAK,GAAGV,QAAQ,CAAC,CAAC;IACxB,MAAMW,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,MAAMK,KAAK,GAAG,CACZ;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAW,CAAC,EACjC;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAO,CAAC,EAC7B;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAW,CAAC,EACjC;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAY,CAAC,EAClC;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAW,CAAC,EACjC;MAAED,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC9B;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU,CAAC,EAClC;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAc,CAAC;IACtC;IACA;MAAED,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAe;IACtC;IAAA,CACD;IACD,MAAMC,QAAQ,GAAGZ,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAMa,QAAQ,GAAGb,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMc,KAAK,GAAGf,QAAQ,CAAC;MACrBgB,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,SAAS,GAAGpB,GAAG,CAAC,EAAE,CAAC;IACzB,MAAMqB,SAAS,GAAGrB,GAAG,CAAC,CAAC,CAAC;IACxB;IACA,MAAMsB,QAAQ,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;MAC/B,IAAIC,GAAG,GAAG,mDAAmD,GAAGD,GAAG,CAACE,MAAM;MAC1EC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;MAChB,OAAOA,GAAG;IACZ,CAAC;IACD,MAAMI,YAAY,GAAIC,QAAQ,IAAI;MAChC3B,YAAY,CAAC4B,KAAK,CAAC,YAAY,GAAGD,QAAQ,GAAG,+CAA+C,EAAE,UAAU,EAAE;QACpGE,wBAAwB,EAAE,IAAI;QAC9BC,SAAS,EAAE,IAAI;QACfC,iBAAiB,EAAE,IAAI;QACvBC,WAAW,EAAE;MACf,CAAC,CAAC;IACR,CAAC;IACD;IACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpBnC,OAAO,CACJoC,GAAG,CAAChC,IAAI,GAAG,SAAS,EAAE;QACrBiC,MAAM,EAAExB;MACV,CAAC,CAAC,CACDyB,IAAI,CAAEC,GAAG,IAAK;QACbpB,SAAS,CAACqB,KAAK,GAAGD,GAAG,CAACE,IAAI,CAACC,OAAO;QAClC;QACAhB,OAAO,CAACC,GAAG,CAACY,GAAG,CAACE,IAAI,CAACC,OAAO,CAAC;QAC7B;QACAtB,SAAS,CAACoB,KAAK,GAAGD,GAAG,CAACE,IAAI,CAACE,KAAK;MAClC,CAAC,CAAC;IACN,CAAC;IACDR,OAAO,CAAC,CAAC;IACT;IACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;MACzB/B,KAAK,CAACI,OAAO,GAAG,CAAC;MACjBkB,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMU,gBAAgB,GAAIC,GAAG,IAAK;MAChCjC,KAAK,CAACK,QAAQ,GAAG4B,GAAG;MACpBX,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMY,gBAAgB,GAAID,GAAG,IAAK;MAChCjC,KAAK,CAACI,OAAO,GAAG6B,GAAG;MACnBX,OAAO,CAAC,CAAC;IACX,CAAC;IACD;IACA,MAAMa,iBAAiB,GAAGA,CAAC;MAAEzB,GAAG;MAAE0B;IAAS,CAAC,KAAK;MAC/C;MACA,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3BvB,OAAO,CAACC,GAAG,CAACsB,QAAQ,CAAC;QACrB,OAAO,SAAS;MAClB,CAAC,MAAM,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAClCvB,OAAO,CAACC,GAAG,CAACsB,QAAQ,CAAC;QACrB,OAAO,UAAU;MACnB;IACF,CAAC;IACD;IACA,MAAMC,SAAS,GAAGA,CAAC;MAAE3B,GAAG;MAAE4B,MAAM;MAAEF,QAAQ;MAAEG;IAAY,CAAC,KAAK;MAC5D,IAAIC,KAAK,GAAG;QAAEC,OAAO,EAAE;MAAU,CAAC;MAClC,OAAOD,KAAK;IACd,CAAC"}]}