{"version": 3, "sources": ["webpack:///./src/views/admin/VehicleReservationSuccess.vue?8edf", "webpack:///./src/icons/svg-black/VehicleReservationSuccess.svg", "webpack:///./src/views/admin/VehicleReservationSuccess.vue", "webpack:///./src/views/admin/VehicleReservationSuccess.vue?0209"], "names": ["module", "exports", "root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "form", "reactive", "data", "id", "yardCode", "yardName", "channelName", "plateNumber", "vehicleClassification", "merchantName", "releaseReason", "notifierName", "appointmentTime", "reserveTime", "remark", "appointmentFlag", "reserveFlag", "value2", "ref", "shortcuts", "text", "value", "end", "Date", "start", "setTime", "getTime", "handleExport", "startDate", "endDate", "console", "log", "window", "location", "href", "yardNameList", "applicantUserId", "localStorage", "getItem", "request", "get", "then", "res", "query", "pageNum", "pageSize", "tableData", "pageTotal", "dialogVisible", "getData", "params", "records", "total", "tableRowClassName", "row", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding", "handleSearch", "handleSizeChange", "val", "handlePageChange", "handleDelete", "index", "sid", "ElMessageBox", "confirm", "type", "delete", "ElMessage", "success", "splice", "error", "catch", "formRef", "__exports__"], "mappings": "kHAAA,W,gDCAAA,EAAOC,QAAU,IAA0B,8C,ycCmGrCC,EAAO,+B,mDACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACV,CAAEC,MAAO,OAAQC,KAAM,YACvB,CAAED,MAAO,OAAQC,KAAM,YACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,OAAQC,KAAM,yBACvB,CAAED,MAAO,OAAQC,KAAM,gBACvB,CAAED,MAAO,QAASC,KAAM,gBACxB,CAAED,MAAO,OAAQC,KAAM,iBACvB,CAAED,MAAO,OAAQC,KAAM,mBACvB,CAAED,MAAO,OAAQC,KAAM,eACvB,CAAED,MAAO,KAAMC,KAAM,UACrB,CAAED,MAAO,OAAQC,KAAM,cACvB,CAAED,MAAO,OAAQC,KAAM,eAGrBC,EAAOC,sBAAS,CAClBC,KAAM,CACFC,GAAI,GACJC,SAAU,GACVC,SAAU,GACVC,YAAa,GACbC,YAAa,GACbC,sBAAuB,GACvBC,aAAc,GACdC,cAAe,GACfC,aAAc,GACdC,gBAAiB,GACjBC,YAAa,GACbC,OAAQ,GACRC,iBAAkB,EAClBC,aAAc,KAIhBC,EAASC,iBAAI,IAEbC,EAAY,CACd,CACIC,KAAM,MACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,OACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,MACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,OACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,KAGvB,CACIF,KAAM,OACNC,MAAOA,KACH,MAAMC,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAElB,OADAC,EAAMC,QAAQD,EAAME,UAAY,QACzB,CAACF,EAAOF,MAIrBK,EAAeA,KACjB,MAAMC,EAAYX,EAAOI,MAAM,GACzBQ,EAAUZ,EAAOI,MAAM,GAC7BS,QAAQC,IAAIH,GACZE,QAAQC,IAAIF,GACZC,QAAQC,IAAI/B,EAAKE,KAAKG,UACtB2B,OAAOC,SAASC,KACZ,6DACAN,EACA,YACAC,EACA,aACA7B,EAAKE,KAAKG,UAoBZ8B,GAFWjB,kBAAI,GACLA,iBAAI,IACCA,iBAAI,KACnBkB,EAAkBlB,iBAAI,IAC5BkB,EAAgBf,MAAQgB,aAAaC,QAAQ,UAC7CC,OAAQC,IAAI,iCAAiCC,KAAMC,IAC/CP,EAAad,MAAQqB,EAAIxC,OAE7B,MAAMyC,EAAQ1C,sBAAS,CACnBI,SAAU,GACVE,YAAa,GACbqC,QAAS,EACTC,SAAU,KAERC,EAAY5B,iBAAI,IAChB6B,EAAY7B,iBAAI,GAEhB8B,GADSX,aAAaC,QAAQ,UACdpB,kBAAI,IAGpB+B,EAAUA,KACZV,OACKC,IAAI/C,EAAO,kBAAmB,CAC3ByD,OAAQP,IAEXF,KAAMC,IAMHI,EAAUzB,MAAQqB,EAAIxC,KAAKiD,QAC3BJ,EAAU1B,MAAQqB,EAAIxC,KAAKkD,MAC3BtB,QAAQC,IAAIW,EAAIxC,SAGtBmD,EAAoBA,EAAGC,MAAKC,eAEzBA,EAAW,GAAK,GAAK,GACtBzB,QAAQC,IAAIwB,GACL,YACCA,EAAW,GAAK,GAAK,GAC7BzB,QAAQC,IAAIwB,GACL,iBAFJ,EAMLC,EAAYA,EAAGF,MAAKG,SAAQF,WAAUG,kBACxC,IAAIC,EAAQ,CAAEC,QAAS,WACvB,OAAOD,GAGXV,IAEA,MAAMY,EAAeA,KACjBlB,EAAMC,QAAU,EAChBK,KAGEa,EAAoBC,IACtBpB,EAAME,SAAWkB,EACjBd,KAGEe,EAAoBD,IACtBpB,EAAMC,QAAUmB,EAChBd,KAGEgB,EAAeA,CAACC,EAAOC,KAEzBC,OAAaC,QAAQ,cAAe,KAAM,CACtCC,KAAM,YAEL7B,KAAK,KACFF,OAAQgC,OAAO9E,EAAO0E,GAAK1B,KAAMC,IACzBA,EAAIxC,MACJsE,OAAUC,QAAQ,QAClB9B,EAAMC,QAAU,EAChBK,IACAH,EAAUzB,MAAMqD,OAAOR,EAAO,IAE9BM,OAAUG,MAAM,YAI3BC,MAAM,SAQTC,EAAU3D,iBAAI,M,81LCxSpB,MAAM4D,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD", "file": "js/chunk-8363f7c8.c3c7727a.js", "sourcesContent": ["export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./VehicleReservationSuccess.vue?vue&type=style&index=0&id=7581bebc&lang=scss&scoped=true\"", "module.exports = __webpack_public_path__ + \"img/VehicleReservationSuccess.7b3246a4.svg\";", "<template>\r\n    <div>\r\n        <div class=\"crumbs\">\r\n            <el-breadcrumb separator=\"/\">\r\n                <el-breadcrumb-item>\r\n                    <i><img src=\"..//../icons/svg-black/VehicleReservationSuccess.svg\"></i>&nbsp; 外来车辆放行管理\r\n                </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n        </div>\r\n        <div class=\"container\">\r\n            <div class=\"handle-box\">\r\n                <el-form :inline=\"true\" :model=\"query\" class=\"demo-form-inline\" label-width=\"60px\">\r\n                    <el-form-item label-width=\"80px\" label=\"车场名称\">\r\n                        <el-input v-model=\"query.yardName\" placeholder=\"车场名称\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label-width=\"80px\" label=\"车牌号码\">\r\n                        <el-input v-model=\"query.plateNumber\" placeholder=\"车牌号码\" class=\"handle-input mr10\"\r\n                            clearable></el-input>\r\n                    </el-form-item>\r\n\r\n                    <el-button type=\"primary\" class=\"searchButton\" icon=\"search\" @click=\"handleSearch\">搜索\r\n                    </el-button>\r\n\r\n                    <!-- <el-button type=\"success\" class=\"addButton\" @click=\"handExport\">导出数据\r\n                    </el-button> -->\r\n                </el-form>\r\n            </div>\r\n            <el-table :data=\"tableData\" border class=\"table\" ref=\"multipleTable\" header-cell-class-name=\"table-header\"\r\n                :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\">\r\n                <el-table-column :show-overflow-tooltip=\"true\" :prop=\"item.prop\" :label=\"item.label\" v-for=\"item in props\"\r\n                    :key=\"item.prop\" align=\"center\" width=\"110px\">\r\n                </el-table-column>\r\n                <el-table-column label=\"预约状态\" prop=\"appointmentFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 0\" effect=\"dark\"\r\n                            size=\"large\">已预约</el-tag>\r\n                        <el-tag type=\"success\" v-if=\"scope.row.appointmentFlag === 1\" effect=\"dark\"\r\n                            size=\"large\">已预约</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"入场状态\" prop=\"reserveFlag\" align=\"center\" width=\"95px\">\r\n                    <template #default=\"scope\">\r\n                        <el-tag type=\"danger\" v-if=\"scope.row.reserveFlag === 0\" effect=\"dark\" size=\"large\">未放行</el-tag>\r\n                        <el-tag type=\"success\" v-else-if=\"scope.row.reserveFlag === 1\" effect=\"dark\"\r\n                            size=\"large\">已放行</el-tag>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"操作\" width=\"150px\" align=\"center\" fixed=\"right\">\r\n                    <template #default=\"scope\">\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" class=\"red\"\r\n                            @click=\"handleDelete(scope.$index, scope.row.id)\" size=\"small\" plain>删除放行\r\n                        </el-button>\r\n                    </template>\r\n                </el-table-column>\r\n\r\n            </el-table>\r\n            <div class=\"pagination\">\r\n                <el-pagination :currentPage=\"query.pageNum\" :page-sizes=\"[10, 20, 40]\" :page-size=\"query.pageSize\"\r\n                    layout=\"total, sizes, prev, pager, next, jumper\" :total=\"pageTotal\" @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handlePageChange\">\r\n                </el-pagination>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <el-dialog title=\"数据导出信息\" v-model=\"dialogVisible\" width=\"50%\">\r\n                <el-form :model=\"form.data\" ref=\"formRef\" :rules=\"rules\" label-width=\"100px\">\r\n                    <el-form-item label=\"选择导出时间\" prop=\"\" label-width=\"128px\">\r\n                        <el-date-picker v-model=\"value2\" type=\"datetimerange\" :shortcuts=\"shortcuts\" range-separator=\"--\"\r\n                            start-placeholder=\"开始时间\" end-placeholder=\"结束时间\" />\r\n                    </el-form-item>\r\n                    <el-form-item label=\"车场名称\" prop=\"yardName\">\r\n                        <el-select v-model=\"form.data.yardName\" placeholder=\"请选择车场名称\">\r\n                            <el-option v-for=\"item in yardNameList\" :key=\"item.yardName\" :label=\"item.yardName\"\r\n                                :value=\"item.yardName\">\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                </el-form>\r\n                <template #footer>\r\n                    <span class=\"dialog-footer\">\r\n                        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n                        <el-button type=\"primary\" @click=\"handleExport\">确 定</el-button>\r\n                    </span>\r\n                </template>\r\n            </el-dialog>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from \"vue-router\";\r\nimport { reactive, ref } from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from \"element-plus\";\r\nimport { useStore } from \"vuex\";\r\n\r\n\r\nimport XLSX from \"xlsx\";\r\nconst root = \"/parking/vehicleReservation/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n    { label: \"车场编码\", prop: \"yardCode\" },\r\n    { label: \"车场名称\", prop: \"yardName\" },\r\n    { label: \"入场通道\", prop: \"channelName\" },\r\n    { label: \"车牌号码\", prop: \"plateNumber\" },\r\n    { label: \"车辆分类\", prop: \"vehicleClassification\" },\r\n    { label: \"商户名称\", prop: \"merchantName\" },\r\n    { label: \"通知人姓名\", prop: \"notifierName\" },\r\n    { label: \"放行原因\", prop: \"releaseReason\" },\r\n    { label: \"预约时间\", prop: \"appointmentTime\" },\r\n    { label: \"放行时间\", prop: \"reserveTime\" },\r\n    { label: \"备注\", prop: \"remark\" },\r\n    { label: \"创建时间\", prop: \"createTime\" },\r\n    { label: \"修改时间\", prop: \"updateTime\" },\r\n];\r\n\r\nconst form = reactive({\r\n    data: {\r\n        id: '',\r\n        yardCode: '',\r\n        yardName: '',\r\n        channelName: '',\r\n        plateNumber: '',\r\n        vehicleClassification: '',\r\n        merchantName: '',\r\n        releaseReason: '',\r\n        notifierName: '',\r\n        appointmentTime: '',\r\n        reserveTime: '',\r\n        remark: '',\r\n        appointmentFlag: -1,\r\n        reserveFlag: -1,\r\n    },\r\n\r\n});\r\nconst value2 = ref([])\r\n\r\nconst shortcuts = [\r\n    {\r\n        text: '前一天',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上一周',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上一个月',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\r\n            return [start, end]\r\n        },\r\n    },\r\n    {\r\n        text: '上三个月',\r\n        value: () => {\r\n            const end = new Date()\r\n            const start = new Date()\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)\r\n            return [start, end]\r\n        },\r\n    },\r\n]\r\nconst handleExport = () => {\r\n    const startDate = value2.value[0]\r\n    const endDate = value2.value[1]\r\n    console.log(startDate)\r\n    console.log(endDate)\r\n    console.log(form.data.yardName)\r\n    window.location.href =\r\n        \"http://localhost:9999/vehicleReservation/export?startDate=\" +\r\n        startDate +\r\n        \"&endDate=\" +\r\n        endDate +\r\n        \"&yardName=\" +\r\n        form.data.yardName +\r\n        \"\";\r\n};\r\n// 重置\r\nconst onReset = () => {\r\n    form.data.id = ''\r\n    form.data.yardCode = ''\r\n    form.data.yardName = ''\r\n    form.data.channelName = ''\r\n    form.data.plateNumber = ''\r\n    form.data.vehicleClassification = ''\r\n    form.data.merchantName = ''\r\n    form.data.releaseReason = ''\r\n    form.data.notifierName = ''\r\n    form.data.appointmentTime = ''\r\n    form.data.reserveTime = ''\r\n    form.data.remark = ''\r\n};\r\nconst viewShow = ref(false)\r\nconst content = ref(\"\");\r\nconst yardNameList = ref([]);\r\nconst applicantUserId = ref(\"\");\r\napplicantUserId.value = localStorage.getItem(\"userId\")\r\nrequest.get(\"/parking/yardInfo/expYardName\").then((res) => {\r\n    yardNameList.value = res.data;\r\n});\r\nconst query = reactive({\r\n    yardName: \"\",\r\n    plateNumber: \"\",\r\n    pageNum: 1,\r\n    pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\nconst userId = localStorage.getItem(\"userId\")\r\nconst dialogVisible = ref(false)\r\n\r\n// 获取表格数据\r\nconst getData = () => {\r\n    request\r\n        .get(root + \"reservationPage\", {\r\n            params: query,\r\n        })\r\n        .then((res) => {\r\n            // tableData.appointmentTime.value = res.data.records.createTime\r\n            //给tableData.appointmentTime的值赋值\r\n            // console.log(res)\r\n            // console.log(res.data)\r\n            // console.log(res.data.records)\r\n            tableData.value = res.data.records;\r\n            pageTotal.value = res.data.total;\r\n            console.log(res.data)\r\n        });\r\n};//指定行颜色\r\nconst tableRowClassName = ({ row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n    if ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n        return 'odd-row';\r\n    } else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n        return 'even-row';\r\n    }\r\n};\r\n//指定行高\r\nconst cellStyle = ({ row, column, rowIndex, columnIndex }) => {\r\n    let style = { padding: '6px 0px' }\r\n    return style\r\n};\r\n\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n    query.pageNum = 1;\r\n    getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n    query.pageSize = val;\r\n    getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n    query.pageNum = val;\r\n    getData();\r\n};\r\n// 删除操作\r\nconst handleDelete = (index, sid) => {\r\n    // 二次确认删除\r\n    ElMessageBox.confirm(\"确定要删除放行信息吗？\", \"提示\", {\r\n        type: \"warning\",\r\n    })\r\n        .then(() => {\r\n            request.delete(root + sid).then((res) => {\r\n                if (res.data) {\r\n                    ElMessage.success(\"删除成功\");\r\n                    query.pageNum = 1;\r\n                    getData();\r\n                    tableData.value.splice(index, 1);\r\n                } else {\r\n                    ElMessage.error(\"删除失败\");\r\n                }\r\n            });\r\n        })\r\n        .catch(() => {\r\n        });\r\n};\r\n\r\n//弹窗显示要导出的数据时间节点\r\nconst handExport = () => {\r\n    dialogVisible.value = true;\r\n}\r\nconst formRef = ref(null);\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n    background-color: rgb(241, 242, 244) !important;\r\n}\r\n\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>\r\n", "import script from \"./VehicleReservationSuccess.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./VehicleReservationSuccess.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./VehicleReservationSuccess.vue?vue&type=style&index=0&id=7581bebc&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7581bebc\"]])\n\nexport default __exports__"], "sourceRoot": ""}