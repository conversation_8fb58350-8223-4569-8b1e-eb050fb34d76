{"version": 3, "sources": ["webpack:///./src/views/admin/IllegalRegiste.vue", "webpack:///./src/views/admin/IllegalRegiste.vue?255f", "webpack:///./src/icons/svg-black/IllegalRegiste.svg", "webpack:///./src/views/admin/IllegalRegiste.vue?f9a8"], "names": ["root", "useRouter", "useRoute", "useStore", "props", "label", "prop", "viewShow", "ref", "content1", "handleView", "row", "purchaseVoucher", "value", "ElMessage", "info", "query", "reactive", "community", "plateNumber", "operatordate", "pageNum", "pageSize", "tableData", "pageTotal", "getData", "request", "get", "params", "then", "res", "data", "records", "console", "log", "total", "handleSearch", "handleSizeChange", "val", "handlePageChange", "tableRowClassName", "rowIndex", "cellStyle", "column", "columnIndex", "style", "padding", "__exports__", "module", "exports"], "mappings": "yeA4GMA,EAAO,2B,wCACEC,iBACDC,iBACAC,iBAFd,MAGMC,EAAQ,CACZ,CAACC,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,QACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,aACpB,CAACD,MAAO,KAAMC,KAAM,YACpB,CAACD,MAAO,KAAMC,KAAM,SACpB,CAACD,MAAO,OAAQC,KAAM,WACtB,CAACD,MAAO,OAAQC,KAAM,eACtB,CAACD,MAAO,OAAQC,KAAM,YACtB,CAACD,MAAO,OAAQC,KAAM,iBASlBC,EAAUC,kBAAI,GACdC,EAAWD,iBAAI,IACfE,EAAcC,IAEU,OAAxBA,EAAIC,iBACNL,EAASM,OAAQ,EACjBJ,EAASI,MAAQF,EAAIC,iBAErBE,OAAUC,KAAK,WAGbC,EAAQC,sBAAS,CACrBC,UAAW,GACXC,YAAa,GACbC,aAAc,GACdC,QAAS,EACTC,SAAU,KAENC,EAAYf,iBAAI,IAChBgB,EAAYhB,iBAAI,GAGhBiB,EAAUA,KACdC,OACKC,IAAI3B,EAAO,UAAW,CACrB4B,OAAQZ,IAETa,KAAMC,IACLP,EAAUV,MAAQiB,EAAIC,KAAKC,QAC3BC,QAAQC,IAAIJ,EAAIC,KAAKC,SACrBR,EAAUX,MAAQiB,EAAIC,KAAKI,SAGnCV,IAEA,MAAMW,EAAeA,KACnBpB,EAAMK,QAAU,EAChBI,KAGIY,EAAoBC,IACxBtB,EAAMM,SAAWgB,EACjBb,KAGIc,EAAoBD,IACxBtB,EAAMK,QAAUiB,EAChBb,KAGIe,EAAoBA,EAAE7B,MAAK8B,eAE3BA,EAAW,GAAK,GAAK,GACnBR,QAAQC,IAAIO,GACX,YACGA,EAAW,GAAK,GAAK,GACzBR,QAAQC,IAAIO,GACX,iBAFF,EAMDC,EAAaA,EAAE/B,MAAKgC,SAAQF,WAASG,kBACvC,IAAKC,EAAQ,CAACC,QAAU,WACxB,OAAOD,G,+tGC5LX,MAAME,EAA2B,IAAgB,EAAQ,CAAC,CAAC,YAAY,qBAExD,gB,4CCRfC,EAAOC,QAAU,IAA0B,mC,kCCA3C", "file": "js/chunk-b26038d4.273edf2f.js", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"crumbs\">\r\n      <el-breadcrumb separator=\"/\">\r\n        <el-breadcrumb-item>\r\n          <i><img src=\"..//../icons/svg-black/IllegalRegiste.svg\"></i>&nbsp; 违规查询\r\n        </el-breadcrumb-item>\r\n      </el-breadcrumb>\r\n    </div>\r\n    <div class=\"container\">\r\n      <div class=\"handle-box\">\r\n        <el-form\r\n            :inline=\"true\"\r\n            :model=\"query\"\r\n            class=\"demo-form-inline\"\r\n            label-width=\"60px\"\r\n        >\r\n    \r\n          <el-form-item label-width=\"80px\" label=\"小区名称\">\r\n            <el-input\r\n                v-model=\"query.community\"\r\n                placeholder=\"部门名称\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"80px\" label=\"违规车牌\">\r\n            <el-input\r\n                v-model=\"query.plateNumber\"\r\n                placeholder=\"违规车牌\"\r\n                class=\"handle-input mr10\"\r\n                clearable\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label-width=\"70px\" label=\"违规日期\">\r\n            <el-date-picker\r\n                v-model=\"query.operatordate\"\r\n                type=\"date\"\r\n                placeholder=\"选择一个日期\"\r\n                format=\"YYYY-MM-DD\"\r\n                value-format=\"YYYY-MM-DD\"\r\n                clearable\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\"\r\n          >搜索\r\n          </el-button\r\n          >\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n          >新增\r\n          </el-button> -->\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n          >导出\r\n          </el-button\r\n          > -->\r\n        </el-form>\r\n      </div>\r\n      <el-table\r\n          :data=\"tableData\"\r\n          border\r\n          class=\"table\"\r\n          ref=\"multipleTable\"\r\n          header-cell-class-name=\"table-header\"\r\n          :cell-style=\"cellStyle\" :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n            :show-overflow-tooltip=\"true\"\r\n            :prop=\"item.prop\"\r\n            :label=\"item.label\"\r\n            v-for=\"item in props\"\r\n            :key=\"item.prop\"\r\n            align=\"center\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"text\" icon=\"el-icon-view\"  @click=\"handleView(scope.row)\"  v-if=\"scope.row.imgurl !=''\">查看图片</el-button>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div class=\"pagination\">\r\n        <el-pagination\r\n            :currentPage=\"query.pageNum\"\r\n            :page-sizes=\"[10, 20, 40]\"\r\n            :page-size=\"query.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"pageTotal\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handlePageChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport {useRoute, useRouter} from \"vue-router\";\r\nimport {reactive, ref} from \"vue\";\r\nimport request from \"@/utils/request\";\r\nimport {ElMessage, ElMessageBox} from \"element-plus\";\r\nimport {useStore} from \"vuex\";\r\n\r\nconst root = \"/parking/illegalregiste/\";\r\nconst router = useRouter();\r\nconst route = useRoute();\r\nconst store = useStore();\r\nconst props = [\r\n  {label: \"省份\", prop: \"province\"},\r\n  {label: \"地市\", prop: \"city\"},\r\n  {label: \"区县\", prop: \"district\"},\r\n  {label: \"小区\", prop: \"community\"},\r\n  {label: \"栋号\", prop: \"building\"},\r\n  {label: \"单元\", prop: \"units\"},\r\n  {label: \"车辆类别\", prop: \"cartype\"},\r\n  {label: \"车牌号码\", prop: \"platenumber\"},\r\n  {label: \"违规位置\", prop: \"location\"},\r\n  {label: \"违规日期\", prop: \"operatordate\"},  \r\n];\r\n\r\n// const handleExport = () => {\r\n//   window.location.href = \"http://localhost:9999/purchase/exportPurchaseManagement\";\r\n// };\r\n\r\n\r\n\r\nconst viewShow= ref(false)\r\nconst content1 = ref(\"\");\r\nconst handleView = (row) => {\r\n  // console.log(\"这批我\")\r\n  if (row.purchaseVoucher !== null) {\r\n    viewShow.value = true\r\n    content1.value = row.purchaseVoucher\r\n  } else {\r\n    ElMessage.info('没有审核原因');\r\n  }\r\n};\r\nconst query = reactive({\r\n  community: \"\",\r\n  plateNumber: \"\",\r\n  operatordate: \"\",\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n});\r\nconst tableData = ref([]);\r\nconst pageTotal = ref(0);\r\n// 获取表格数据\r\n\r\nconst getData = () => {\r\n  request\r\n      .get(root + \"allpage\", {\r\n        params: query,\r\n      })\r\n      .then((res) => {\r\n        tableData.value = res.data.records;\r\n        console.log(res.data.records);\r\n        pageTotal.value = res.data.total;\r\n      });\r\n};\r\ngetData();\r\n// 查询操作\r\nconst handleSearch = () => {\r\n  query.pageNum = 1;\r\n  getData();\r\n};\r\n// 分页大小\r\nconst handleSizeChange = (val) => {\r\n  query.pageSize = val;\r\n  getData();\r\n};\r\n// 分页导航\r\nconst handlePageChange = (val) => {\r\n  query.pageNum = val;\r\n  getData();\r\n};\r\n//指定行颜色\r\nconst tableRowClassName = ({row, rowIndex }) => {\r\n    // console.log(rowIndex)\r\n\tif ((rowIndex + 1) % 2 == 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'odd-row';\r\n\t}else if ((rowIndex + 1) % 2 != 0) {\r\n        console.log(rowIndex)\r\n\t\treturn 'even-row';\r\n\t}\r\n};\r\n//指定行高\r\nconst cellStyle  = ({row, column, rowIndex,columnIndex}) => {\r\n    let  style = {padding : '0px 3px'}\r\n    return style\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.odd-row {\r\n\tbackground-color: rgb(241, 242, 244) !important;\r\n}\r\n.even-row {\r\n    background-color: rgb(255, 255, 255) !important;\r\n}\r\n</style>", "import script from \"./IllegalRegiste.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./IllegalRegiste.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./IllegalRegiste.vue?vue&type=style&index=0&id=648389f3&lang=scss&scoped=true\"\n\nimport exportComponent from \"F:\\\\桌面\\\\ParkingManageDemo\\\\manage-front\\\\node_modules\\\\vue-loader-v16\\\\dist\\\\exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-648389f3\"]])\n\nexport default __exports__", "module.exports = __webpack_public_path__ + \"img/IllegalRegiste.347d0a47.svg\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader-v16/dist/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader-v16/dist/index.js??ref--1-1!./IllegalRegiste.vue?vue&type=style&index=0&id=648389f3&lang=scss&scoped=true\""], "sourceRoot": ""}